/* 
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */


function aplicarScroll(){

    // ScrollInfinito
    var hasInfinito = jQuery('.scrollInfinito');
    if (hasInfinito.length > 0) {
        $(window).scroll(function() {
            window.clearTimeout(timeOutInfiniteScroll);
            timeOutInfiniteScroll = window.setTimeout(function() {
                if (typeof (viewPortTopInicio) == "undefined") {
                    viewPortTopInicio = 0;
                }
                if (typeof(carregaMais) !== "undefined") {
                    var finalAtual = jQuery(hasInfinito).position().top + jQuery(hasInfinito).height();
                    var viewPortFim = jQuery(window).scrollTop() + jQuery(window).height();
                    if (viewPortFim > finalAtual && viewPortTopInicio < jQuery(window).scrollTop()) {
                        carregaMais();
                    }
                    viewPortTopInicio = jQuery(window).scrollTop();
                }
            }, 1000);
        });
    }

}