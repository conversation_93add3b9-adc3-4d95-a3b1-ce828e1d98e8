<!DOCTYPE html>
<html lang="br"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <h:head>
        <title>OAMD</title>
        <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/>
        <link class="component" href="#{request.contextPath}/css/oamd.css" rel="stylesheet" type="text/css"/>
        <link href="#{request.contextPath}/bootstrap/css/bootstrap.min.css" rel="stylesheet"/>
        <script src="#{request.contextPath}/bootstrap/js/jquery.js"></script>
        <script src="#{request.contextPath}/bootstrap/js/bootstrap.min.js"></script>
        <script src="#{request.contextPath}/bootstrap/js/bootstrap-typeahead.js"></script>
        <link href="#{request.contextPath}/bootstrap/css/bootstrap-responsive.min.css" rel="stylesheet"/>

        <link rel="apple-touch-icon-precomposed" sizes="144x144" href='#{resource["imagens/apple-touch-icon.png"]}'/>
        <link rel="apple-touch-icon-precomposed" sizes="114x114" href='#{resource["imagens/apple-touch-icon.png"]}'/>
        <link rel="apple-touch-icon-precomposed" sizes="72x72" href='#{resource["imagens/apple-touch-icon.png"]}'/>
        <link rel="apple-touch-icon-precomposed" sizes="57x57" href='#{resource["imagens/apple-touch-icon.png"]}'/>
        <link rel="shortcut icon" type="image/png" href='#{resource["imagens/favicon.ico"]}' />

        <h:outputStylesheet library="css" name="font-awesome.min.css"/>
        <h:outputStylesheet library="css" name="chosen.css"/>
        <h:outputStylesheet library="css" name="custom1302.css"/>
        <h:outputStylesheet library="css" name="bootstrap.min.css"/>
        <h:outputScript library="js" name="bootstrap.min.js"/>
        <h:outputScript library="js" name="w.js"/>

        <style>
            img {
                max-width: inherit !important;
            }

            .eraser {
                width: 20px;
                margin-top: -3px;
                filter: invert(46%) sepia(0%) saturate(5205%) hue-rotate(94deg) brightness(88%) contrast(91%);
            }
        </style>

        <ui:insert name="CSS">

        </ui:insert>

        <script>
            PrimeFaces.locales['pt'] = {
                closeText: 'fechar',
                prevText: 'anterior',
                nextText: 'proximo',
                currentText: 'atual',
                monthNames: ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"],
                monthNamesShort: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "Dez"],
                dayNames: ['Domingo', 'Segunda-Feira', 'Terça-Feira', 'Quarta-Feira', 'Quinta-Feira', 'Sexta-Feira', 'Sábado'],
                dayNamesShort: ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],
                //dayNamesShort: ['Dom','Seg','Ter','Qua','Qui','Sex','Sab'],
                dayNamesMin: ['D', 'S', 'T', 'Q', 'Q', 'S', 'S'],
                weekHeader: 'Sem',
                firstDay: 0,
                isRTL: false,
                showMonthAfterYear: false,
                yearSuffix: '',
                timeOnlyTitle: 'Horas',
                timeText: 'Tempo',
                hourText: 'Hora',
                minuteText: 'Minuto',
                secondText: 'Segundo',
                month: 'Mês',
                week: 'Semana',
                day: 'Dia',
                allDayText: 'Dia Todo'
            };
        </script>

        <script src="#{request.contextPath}/resources/js/tooltipster/jquery.tooltipster.min.js"></script>
        <link href="#{request.contextPath}/resources/css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
        <link href="#{request.contextPath}/resources/css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>

        <script>
            function carregarTooltipster() {
                try {
                    jQuery('.tooltipster').tooltipster({
                        theme: 'tooltipster-light',
                        position: 'bottom',
                        animation: 'grow',
                        contentAsHTML: true
                    });

                    jQuery('.tooltipstertop').tooltipster({
                        theme: 'tooltipster-light',
                        position: 'top',
                        animation: 'grow',
                        maxWidth: 350,
                        contentAsHTML: true
                    });
                }catch (e) {

                }

            }
        </script>

    </h:head>
    <h:body>
        <h:form prependId="true" id="fmLay" enctype="multipart/form-data">
            <p:hotkey bind="esc" handler="$('.ui-dialog').hide(); $('.ui-widget-overlay').hide(); $('.ui-growl').hide();"  />
            <div class="jumbotron" style="padding-top: 4px; padding-bottom: 4px;">
                <div class="container" style="width: 100%; margin-left: 6px; margin-right: 6px">
                    <div class="navbar">
                        <div class="navbar-inner">
                            <div class="container">
                                <h:commandLink styleClass="brand">OAMD - Sistema Pacto</h:commandLink>

                                <h:panelGroup layout="block" styleClass="nav-collapse collapse navbar-responsive-collapse"
                                    style="display: flex; justify-content: space-between">
                                    <h:panelGroup layout="block" id="pnlConsulta"
                                                  rendered="#{fn:endsWith(request.requestURI, 'empresas.xhtml')}"
                                                  tyleClass="navbar-search pull-left" style="display: flex; padding-top: 5px">
                                        <h:inputText id="consulta" style="margin: 0" value="#{OamdControle.filtroNome}"
                                                     onkeypress="if (event.keyCode === 13) {
                                                         document.getElementById('fmLay:btnConsultar').click();
                                                         return false;
                                                     }"/>
                                        <h:panelGroup id="pnlAcoes" layout="block" style="margin-left: 8px; display: flex">
                                            <p:commandLink id="btnConsultar"
                                                           styleClass="botaoApagarOamd"
                                                           title="Consultar"
                                                           update="fmLay:consulta, geralpainel"
                                                           action="#{OamdControle.filtrar}"
                                                           oncomplete="$(function(){PrimeFaces.focus('fmLay:consulta');});">
                                                <i class="fa-icon-search" style="vertical-align: middle;"/>
                                            </p:commandLink>

                                            <p:commandLink id="limparCampos"
                                                           styleClass="botaoApagarOamd"
                                                           title="Limpar Consulta"
                                                           update="fmLay:consulta, geralpainel"
                                                           action="#{OamdControle.limparCampo}"
                                                           oncomplete="$(function(){PrimeFaces.focus('fmLay:consulta');});">
                                                <i class="fa-icon-arrow-left" style="vertical-align: middle;"/>
                                            </p:commandLink>
                                        </h:panelGroup>
                                    </h:panelGroup>                         

                                    <span id="mensagem" class="label label-warning"/>

                                    <p:menubar id="menu" styleClass="pull-right" style="border: 0">
                                        <p:menuitem value="Home" action="pretty:empresas" 
                                                    process="@this"
                                                    actionListener="#{OamdControle.consultarEmpresas()}" icon="ui-icon-home"/>

                                        <p:submenu label="Cadastros" icon="ui-icon-plus"
                                                   rendered="#{OamdControle.apresentarMenuCadastros}">
                                            <p:menuitem rendered="#{OamdControle.usuario.adm}"
                                                        value="Usuários" action="pretty:usuarios" icon="ui-icon-person"/>
                                            <p:menuitem rendered="#{OamdControle.permiteCriarEmpresa}" value="Empresa" action="pretty:cadempresa"
                                                        actionListener="#{EmpresaControle.consultarEmpresas()}" icon="ui-icon-document"/>
                                            <p:menuitem rendered="#{OamdControle.usuario.adm and OamdControle.oamdPacto}"
                                                    value="Rede de Empresas" action="pretty:cadredeempresa" icon="ui-icon-document" />
                                            <p:menuitem rendered="#{OamdControle.usuario.adm and OamdControle.oamdPacto}"
                                                        value="Infras" actionListener="#{EstatisticasControle.atualizar()}" action="pretty:infras" icon="ui-icon-document" />
                                            <p:menuitem rendered="#{OamdControle.usuario.adm and OamdControle.oamdPacto}" value="Cadastro do Pipz" action="pretty:cadpipz"
                                                         icon="ui-icon-document"/>
                                            <p:menuitem rendered="#{OamdControle.oamdPacto and (OamdControle.usuario.adm or OamdControle.usuario.pmg)}"
                                                        value="Feeds Gestão" action="pretty:feeds" icon="ui-icon-signal-diag"/>
                                            <p:menuitem rendered="#{OamdControle.usuario.adm and OamdControle.oamdPacto}"
                                                        value="Apps" action="pretty:empresas" icon="ui-icon-document"
                                                        process="@this" url="aplicativos"/>
                                            <p:menuitem rendered="#{OamdControle.usuario.adm and OamdControle.oamdPacto}"
                                                        value="Tabela Recorrência" action="pretty:creditoPacto" icon="ui-icon-document"/>
                                            <p:menuitem rendered="#{OamdControle.oamdPacto and OamdControle.usuario.permiteControlarRecursoPadrao}"
                                                        value="Recurso Padrão" action="pretty:infoMigracao" icon="ui-icon-document"/>
                                            <p:menuitem rendered="#{OamdControle.oamdPacto and OamdControle.usuario.permiteControlarRecursoPadrao}"
                                                        value="Recurso Padrão - Data" action="pretty:migracaoRecurso" icon="ui-icon-document"/>
                                        </p:submenu>

                                        <p:submenu label="Conhecimento" icon="ui-icon-help"
                                                   rendered="#{OamdControle.oamdPacto}">
                                            <p:menuitem value="UCP"
                                                        url="redir?up"
                                                        process="@this" icon="ui-icon-info"/>

                                            <p:menuitem value="Tela de apoio" url="redir?apoio"
                                                        process="@this" icon="ui-icon-info"/>

                                            <p:menuitem rendered="#{OamdControle.usuario.adm}"
                                                        value="Game:Indicadores" icon="ui-icon-signal"
                                                        url="redir?gameindicadores"
                                                        target="gameresults"/>
                                        </p:submenu>

                                        <p:submenu label="Planos de Sucesso" icon="ui-icon-check"
                                                   rendered="#{OamdControle.apresentarCS}">
                                            <p:menuitem value="Nível de Engajamento" id="biPlanoSucessoPretty"
                                                        actionListener="#{ModeloPlanoSucessoControle.consultarEngajamento}"
                                                        action="pretty:biPlanoSucesso"
                                                        icon="ui-icon-document"/>
                                            <p:menuitem value="Plano de sucesso" id="planoSucessoPretty"
                                                        actionListener="#{ModeloPlanoSucessoControle.consultar}"
                                                        action="pretty:planosSucesso"
                                                        icon="ui-icon-document"/>
                                            <p:menuitem value="Integrantes" id="csIntegrantes"
                                                        actionListener="#{integrantesCSControle.consultar}"
                                                        action="pretty:integrantesCS"
                                                        icon="ui-icon-person"/>
                                        </p:submenu>



                                        <p:submenu label="Pacto Store+" icon="ui-icon-document"
                                                   rendered="#{OamdControle.apresentarMenuProdutosPacto}">
                                            <p:menuitem value="Produtos Store+"
                                                        id="produtoPacto"
                                                        rendered="#{OamdControle.apresentarProdutosPacto}"
                                                        action="pretty:produtoPacto"
                                                        icon="ui-icon-document"/>
                                            <p:menuitem value="Doc. Store+"
                                                        rendered="#{not empty OamdControle.urlDocumentacaoPactoStore}"
                                                        oncomplete="window.open('#{OamdControle.urlDocumentacaoPactoStore}', '_blank');"
                                                        id="produtoPactoDocumentacao"
                                                        icon="ui-icon-document"/>
                                            <p:menuitem value="Vendas Store+"
                                                        actionListener="#{ProdutoPactoCompraControle.abrirCompras}"
                                                        id="produtoPactoCompras"
                                                        action="pretty:produtoPactoCompra"
                                                        icon="ui-icon-document"/>
                                            <p:menuitem value="Log Store+"
                                                        actionListener="#{ProdutoPactoLogControle.abrir}"
                                                        id="produtoPactoLog"
                                                        action="pretty:produtoPactoLog"
                                                        icon="ui-icon-document"/>
                                            <p:menuitem value="Config Personal Fit"
                                                        id="configAssinatura"
                                                        action="pretty:configAssinatura"
                                                        icon="ui-icon-gear"/>
                                        </p:submenu>

                                        <p:menuitem rendered="#{OamdControle.oamdPacto and OamdControle.usuario.adm}"
                                                    value="Facilite" action="pretty:facilite" icon="ui-icon-angle-right"/>

                                        <p:submenu label="Infras" icon="ui-icon-search"
                                                   rendered="#{OamdControle.oamdPacto and OamdControle.apresentarMenuInfras and fn:endsWith(request.requestURI, 'empresas.xhtml')}">
                                            <p:menuitem value="Todas" action="#{OamdControle.todasInfras()}"
                                                            update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"/>                                                
                                            <c:forEach var="infra" items="#{EmpresaControle.listaInfra}">
                                                <p:menuitem value="#{infra.label}" action="#{OamdControle.filtrarInfra(infra.value)}"
                                                            update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"/>                                                
                                            </c:forEach>                                            
                                        </p:submenu>

                                        <p:submenu label="Consultas" icon="ui-icon-search"
                                                   rendered="#{OamdControle.oamdPacto and fn:endsWith(request.requestURI, 'empresas.xhtml')}">
                                            <p:menuitem rendered="#{OamdControle.usuario.adm}" value="UpdateServlet" url="#{MenuControle.urlUpdateServlet}" target="_blank"/>
                                            <p:menuitem value="Uso Interno" rendered="#{OamdControle.usuario.adm}" action="#{OamdControle.filtrarLocais('USO_INTERNO')}"
                                                        update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"/>
                                            <p:menuitem value="Possuem Treino" rendered="#{OamdControle.usuario.adm}" ajax="true" action="#{OamdControle.filtrarLocais('POSSUEM_TREINO')}" update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"/>
                                            <p:menuitem rendered="#{OamdControle.usuario.adm}" value="Sessões ativas" ajax="true" action="pretty:sessoesAtivas" actionListener="#{SessaoControle.consultarSessoes()}"/>
                                            <p:menuitem rendered="#{OamdControle.usuario.adm}" value="Nº de empresas" ajax="true" action="#{OamdControle.contarEmpresas()}" update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"/>
                                            <p:menuitem rendered="#{OamdControle.usuario.adm}" value="Bloqueadas" ajax="true" action="#{OamdControle.filtrarLocais('TEM_BLOQUEIO')}" update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"/>
                                            <p:menuitem rendered="#{OamdControle.usuario.adm}" value="App Aluno" ajax="true" action="#{OamdControle.filtrarLocais('APP_ALUNO')}" update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"/>
                                            <p:menuitem rendered="#{OamdControle.usuario.adm or OamdControle.usuario.permiteNotaFiscal}" value="Nota Fiscal" action="pretty:notaFiscal"/>
                                        </p:submenu>

                                        <p:submenu label="Carteiras PMG" icon="ui-icon-search" 
                                                   rendered="#{OamdControle.oamdPacto and fn:endsWith(request.requestURI, 'empresas.xhtml') and OamdControle.usuario.pmg}">
                                            <p:menuitem value="Sem carteira" 
                                                        action="#{OamdControle.consultarEmpresas(userMenu.codigo)}"
                                                        update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"/>
                                            <c:forEach items="#{OamdControle.usuariosPMG}" var="userMenu">
                                                <p:menuitem value="#{userMenu.userName}" 
                                                            action="#{OamdControle.consultarEmpresas(userMenu.codigo)}"
                                                            update=":fmLay:conteudoPag :fmLay:pnlLabelFiltrados"/>
                                            </c:forEach>
                                        </p:submenu>
                                        <p:submenu label="Serviços" icon="ui-icon-gear" rendered="#{OamdControle.oamdPacto and OamdControle.usuario.adm}">
                                            <p:menuitem value="Enotas" action="pretty:enotas" icon="ui-icon-document"/>
                                            <p:menuitem value="Notificações MoviDesk" action="pretty:notificacoesMoviDesk" icon="ui-icon-alert"/>
                                        </p:submenu>

                                        <p:menuitem value="Gitlab" action="pretty:gitlab"
                                                    process="@this"
                                                    rendered="#{OamdControle.usuario.permiteControlarGitlab}" icon="ui-icon-document"/>

                                        <p:submenu id="smPerson" label="User: #{OamdControle.usuario.userName}" icon="ui-icon-person">
                                            <p:menuitem icon="ui-icon-refresh" value="Trocar de senha" action="pretty:trocar-senha"/>

                                            <p:menuitem icon="ui-icon-document-b"
                                                        rendered="#{OamdControle.usuario.canUsePg4devContainer}"
                                                        actionListener="#{Pg4devControle.initPg4dev()}"
                                                        value="PG4DEV" action="pretty:pg4dev"/>

                                            <p:menuitem  value="Sair" action="#{LoginControle.logout}" icon="ui-icon-closethick" />
                                        </p:submenu>  
                                    </p:menubar>

                                </h:panelGroup><!-- /.nav-collapse -->
                            </div>
                        </div><!-- /navbar-inner -->
                    </div>
                </div>
            </div>

            <script>
                jQuery.noConflict();
//                document.getElementById("fmLay:consulta_input").className = 'search-query span2';
            </script>

            <h:panelGroup layout="block" id="conteudoPag" styleClass="container conteudoGeralClass">
                <p:dialog modal="true" id="panelCarregando"                                                                  
                          styleClass="panelCarregando"                      
                          showHeader="false"
                          closeOnEscape="false"
                          widgetVar="carregando"
                          draggable="false"
                          closable="false"
                          maximizable="false"
                          minimizable="false"
                          resizable="false"
                          width="210"
                          minWidth="220">
                    <i class="fa-icon-spin fa-icon-refresh fa-icon-2x" style="vertical-align: middle;" />
                    <h:outputText style="font-weight: bold; vertical-align: middle; margin-left: 10px; font-size: 14px;" value="Por favor, aguarde..."/>
                </p:dialog>

                <p:ajaxStatus style="width:64px;height:64px;position:fixed;right:5px;bottom:5px"                          
                              onstart="carregando.show();"
                              oncomplete="carregando.hide();carregarTooltipster();"
                              onerror="carregando.hide();"/>

                <ui:insert name="JS">

                </ui:insert>
                <ui:insert name="conteudo">
                    Conteudo
                </ui:insert>
            </h:panelGroup>

            <h:outputScript target="head" library="primefaces" name="jquery/jquery.js"/>
            <!-- -->
            <h:outputScript target="head" library="js" name="bootstrap.min.js"/>
            <h:outputScript target="head" library="js" name="bootstrap-affix.js"/>
            <h:outputScript target="head" library="js" name="chosen.proto.js"/>
            <h:outputScript target="head" library="js" name="jquery.cookie.js"/>
            <h:outputScript target="head" library="js" name="time.js"/>
            <h:outputScript target="head" library="js" name="allPaginas.js"/>

            <p:growl id="growl" life="3000" autoUpdate="true" globalOnly="true" sticky="true" escape="false" />
            <p:growl id="messages" life="2000" escape="false" autoUpdate="true" sticky="false" severity="info" />

            <c:if test="${not empty OamdControle.usuario.tokenBot1}">
                <script src="https://unpkg.com/blip-chat-widget" type="text/javascript">
                </script>
                <script>
                    ${OamdControle.scriptBot}
                </script>

            </c:if>
        </h:form>

    </h:body>

    <script>
        carregarTooltipster();
    </script>
</html>
