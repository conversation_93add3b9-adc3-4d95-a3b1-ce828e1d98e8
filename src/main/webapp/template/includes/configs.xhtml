<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
    xmlns:p="http://primefaces.org/ui"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <!-- fragmento para futura divisão e reciclagem -->

    <p:dialog id="configuracoesModal" header="#{title[ConfiguracaoControle.tipo.title]}" 
              widgetVar="modalConfs" modal="true" width="950px" height="400px"
              resizable="false" styleClass="modalConfigs">
        <h:panelGroup id="painelModal" layout="block" 
                      style="overflow-x: auto; overflow-y: auto; width: 100%; height: 100%;">

            <!-- Confs Globais -->
            <p:tabView orientation="left" id="tabConfigGlob" rendered="#{ConfiguracaoControle.tipo == 'GLOBAIS'}">
                <p:tab title="Globais">
                    <h:dataTable value="#{ConfiguracaoControle.configuracoesGlobais}" var="configuracao" width="100%">
                        <h:column>
                            <h:outputText value="#{title[configuracao.nome]}:"/>
                        </h:column>
                        <h:column>
                            <p:selectBooleanCheckbox value="#{configuracao.valorAsBoolean}" rendered="#{configuracao.tpBoolean}"/>
                            <p:inputMask value="#{configuracao.valorAsDouble}" rendered="#{configuracao.tpDouble}" mask="#{configuracao.configuracao.mascara}" />
                            <p:inputMask value="#{configuracao.valor}" rendered="#{configuracao.tpString}" mask="#{configuracao.configuracao.mascara}"/>
                            <p:inputMask value="#{configuracao.valorAsInteger}" rendered="#{configuracao.tpInteger}"
                                         mask="#{empty configuracao.configuracao.mascara ? '?999' : configuracao.configuracao.mascara}"/>
                        </h:column>
                    </h:dataTable><br/>
                    <p:commandLink styleClass="btn btn-primary" action="#{ConfiguracaoControle.salvarConfiguracoes}"
                                   ajax="true">
                        <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                    </p:commandLink>  
                </p:tab>
                <p:tab title="Gestão">
                    <h:dataTable value="#{ConfiguracaoControle.configuracoesGestao}" var="configuracaoGestao" width="100%">
                        <h:column>
                            <h:outputText value="#{title[configuracaoGestao.nome]}:"/>
                        </h:column>
                        <h:column>
                            <p:selectBooleanCheckbox value="#{configuracaoGestao.valorAsBoolean}" rendered="#{configuracaoGestao.tpBoolean}"/>
                            <p:inputMask value="#{configuracaoGestao.valorAsDouble}" rendered="#{configuracaoGestao.tpDouble}" mask="#{configuracaoGestao.configuracao.mascara}" />
                            <p:inputMask value="#{configuracaoGestao.valor}" rendered="#{configuracaoGestao.tpString}" mask="#{configuracaoGestao.configuracao.mascara}"/>
                            <p:inputMask value="#{configuracaoGestao.valorAsInteger}" rendered="#{configuracaoGestao.tpInteger}"
                                         mask="#{empty configuracaoGestao.configuracao.mascara ? '?999' : configuracaoGestao.configuracao.mascara}"/>
                        </h:column>
                    </h:dataTable><br/>
                    <p:commandLink styleClass="btn btn-primary" action="#{ConfiguracaoControle.salvarConfiguracoes}"
                                   ajax="true">
                        <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                    </p:commandLink>  
                </p:tab>
            </p:tabView>

            <!-- Editar Perfil Usuário -->
            <p:tabView orientation="left" id="tabConfigUsu" rendered="#{ConfiguracaoControle.tipo == 'USUARIO'}">
                <p:tab title="Usuario">
                    <h:panelGroup layout="block" >
                        <h:panelGroup layout="block" >
                            <h:graphicImage styleClass="span2"
                                            url="#{SuperControl.defineUrlFoto(SuperControl.usuario.chave,ConfiguracaoControle.usuarioEdicao.professor.codigoPessoa)}"
                                            style="padding: 10px;"/>
                            <h2>#{ConfiguracaoControle.usuarioEdicao.professor.nomeAbreviado}</h2>

                            <h:outputText style="vertical-align: middle" value="#{title['usuario']}: "/>
                            <h:outputText style="vertical-align: middle" value="#{ConfiguracaoControle.usuarioEdicao.userName}"/><br/>

                            <h:panelGroup style="vertical-align: middle" rendered="#{SuperControl.usuario.codigo == ConfiguracaoControle.usuarioEdicao.codigo or SuperControl.root}">
                                <h:outputText value="#{title['senha']}:"/>
                                <p:password value="#{ConfiguracaoControle.usuarioEdicao.senhaAlterar}"/><br/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup> <br/> <br/>  <br/>

                    <p:commandLink styleClass="btn btn-primary" action="#{ConfiguracaoControle.salvarUsuario()}"
                                   ajax="true"  update=":fmLay:painelModal,:fmLay:tabConfigPerf:categorias:painelTab"
                                   rendered="#{SuperControl.usuario.isItemHabilitado('USUARIOS', 'EDITAR')}">
                        <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                    </p:commandLink>

                </p:tab>
            </p:tabView>

            <p:tabView orientation="left" id="tabConfigUsuarios"  
                       activeIndex="#{ConfiguracaoControle.indexTabPerfis}"
                       rendered="#{ConfiguracaoControle.tipo == 'USUARIOS'}"
                       value="#{ConfiguracaoControle.usuarios}" var="usuario"
                       onTabChange="jQuery('.tagUser').chosen('destroy')" onTabShow="jQuery('.tagUser').chosen()">
                <p:tab title="#{usuario.professor.nomeAbreviado}" id="tabUsuarios">
                    <script>
                        jQuery('.tagUser').chosen();
                    </script>
                    <h:panelGroup layout="block" >
                        <h:panelGroup layout="block" >
                            <h:graphicImage styleClass="span2"
                                            url="#{SuperControl.defineUrlFoto(SuperControl.usuario.chave,usuario.professor.codigoPessoa)}"
                                            style="padding: 10px;"/>
                            <h2>#{usuario.professor.nomeAbreviado}</h2>

                            <h:outputText value="#{title['usuario']}: "/>
                            <h:outputText value="#{usuario.userName}"/><br/>

                            <h:panelGroup rendered="#{usuario.codigo == SuperControl.usuario.codigo or SuperControl.root}">
                                <h:outputText value="#{title['senha']}:"/>
                                <p:password value="#{usuario.senhaAlterar}"/><br/>
                            </h:panelGroup>

                            <h:outputText value="#{title['agenda.tipo']}: "/>
                            <h:selectOneMenu value="#{usuario.tipoCodigo}" styleClass="tagUser"
                                             rendered="#{SuperControl.root}">
                                <f:selectItems value="#{ConfiguracaoControle.tiposSelect}"/>
                            </h:selectOneMenu>
                            <h:outputText value="#{usuario.tipo.descricao}" rendered="#{!SuperControl.root}"/><br/>

                            <h:outputText value="#{title['perfil']}: "/>
                            <h:selectOneMenu value="#{usuario.perfilCodigo}" styleClass="tagUser"
                                             rendered="#{SuperControl.usuario.isItemHabilitado('USUARIOS', 'EDITAR')}">
                                <f:selectItems value="#{ConfiguracaoControle.perfisSelect}"/>
                            </h:selectOneMenu>
                            <h:outputText value="#{usuario.perfil.nome}"
                                          rendered="#{!SuperControl.usuario.isItemHabilitado('USUARIOS', 'EDITAR')}"/>
                        </h:panelGroup>
                    </h:panelGroup> <br/> <br/>  <br/>

                    <p:commandLink styleClass="btn btn-primary" action="#{ConfiguracaoControle.salvarUsuario(usuario)}"
                                   ajax="true"  update=":fmLay:painelModal,:fmLay:tabConfigPerf:categorias:painelTab"
                                   rendered="#{SuperControl.usuario.isItemHabilitado('USUARIOS', 'EDITAR')}">
                        <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                    </p:commandLink>
                </p:tab>
            </p:tabView>

            <!-- Confs Perfis -->
            <p:tabView orientation="left" id="tabConfigPerf"  
                       activeIndex="#{ConfiguracaoControle.indexTabPerfis}"
                       rendered="#{ConfiguracaoControle.tipo == 'PERFIL'}"
                       value="#{ConfiguracaoControle.perfis}" var="perfil">
                <p:tab title="#{perfil.nome}" id="tabPerfil">
                    <h:outputText value="#{title['nomeperfil']}: "/>
                    <p:inputText rendered="#{perfil.codigo == null or perfil.codigo == 0}" value="#{ConfiguracaoControle.nomeNovoPerfil}"/>
                    <p:inputText rendered="#{perfil.codigo != null and perfil.codigo > 0}" value="#{perfil.nome}">
                        <p:ajax listener="#{ConfiguracaoControle.salvarNome(perfil)}" event="change" process="@this" global="false" partialSubmit="true"/>
                    </p:inputText>

                    <p:accordionPanel value="#{perfil.categorias}" var="categoria" id="categorias" activeIndex="#{ConfiguracaoControle.indexTabCategorias}">
                        <p:tab title="#{title[categoria.categoria]}" id="tabcategorias">
                            <h:panelGroup id="painelTab">
                                <!--h:outputText value="# {title['entidade']}" style="font-weight: bold;" rendered="# {not empty categoria.recursos}"/-->
                                <h:dataTable rendered="#{not empty categoria.recursos}" value="#{categoria.recursos}" var="recurso" width="100%" id="tableRecursos">
                                    <h:column>
                                        <h:outputText value="#{recurso.label}"/>
                                        <h:outputText value="#{recurso.detalhe}"/>
                                    </h:column>
                                    <h:column>
                                        <ui:repeat value="#{recurso.tiposPermissoes}" var="tipoPer">
                                            <h:panelGroup layout="block">
                                                <p:selectBooleanCheckbox value="#{tipoPer.escolhido}" styleClass="checkOpts"
                                                                         rendered="#{recurso.codigo != null}" itemLabel="#{tipoPer.label}"
                                                                         disabled="#{!SuperControl.usuario.isItemHabilitado('PERFIL_USUARIO', 'EDITAR')}">
                                                    <p:ajax listener="#{ConfiguracaoControle.salvarPermissao(perfil, recurso, tipoPer, categoria)}"
                                                            partialSubmit="true"
                                                            process="@this"
                                                            event="change"
                                                            global="false"
                                                            update=":fmLay:tabConfigPerf:categorias:painelTab"
                                                            />
                                                </p:selectBooleanCheckbox>

                                                <p:selectBooleanCheckbox value="#{tipoPer.escolhido}" rendered="#{recurso.codigo == null}"
                                                                         disabled="#{!SuperControl.usuario.isItemHabilitado('PERFIL_USUARIO', 'EDITAR')}"
                                                                         itemLabel="#{tipoPer.label}" styleClass="checkOpts">
                                                    <p:ajax listener="#{ConfiguracaoControle.marcarTodos(perfil, tipoPer, categoria, true)}"
                                                            partialSubmit="true"
                                                            event="change"
                                                            process="@this"
                                                            update=":fmLay:tabConfigPerf:categorias:painelTab"/>

                                                </p:selectBooleanCheckbox>
                                            </h:panelGroup>
                                        </ui:repeat>
                                    </h:column>
                                </h:dataTable>

                                <h4>
                                    <h:outputText value="#{title['funcionalidade']}" style="font-weight: bold;"
                                                  rendered="#{not empty categoria.funcionalidades}"/>
                                </h4>
                                <h:dataTable rendered="#{not empty categoria.funcionalidades}" value="#{categoria.funcionalidades}" 
                                             var="funcionalidade" width="100%" id="tableFuncionalidades" styleClass="funcionalTabela">
                                    <h:column>
                                        <h:outputText title="#{funcionalidade.detalhe}" value="#{funcionalidade.label}"/>
                                    </h:column>
                                    <h:column>
                                        <ui:repeat value="#{funcionalidade.tiposPermissoes}" var="permissaoFuncionalidade">
                                            <h:panelGroup layout="block">
                                                <p:selectBooleanCheckbox value="#{permissaoFuncionalidade.escolhido}"
                                                                         itemLabel="#{title['permitir']}" rendered="#{funcionalidade.codigo != null}"
                                                                         disabled="#{!SuperControl.usuario.isItemHabilitado('PERFIL_USUARIO', 'EDITAR')}">
                                                    <p:ajax listener="#{ConfiguracaoControle.salvarPermissao(perfil, funcionalidade, permissaoFuncionalidade, categoria)}"
                                                            partialSubmit="true"
                                                            process="@this"
                                                            event="change"
                                                            global="false"
                                                            update=":fmLay:tabConfigPerf:categorias:painelTab"/>
                                                </p:selectBooleanCheckbox>

                                                <p:selectBooleanCheckbox value="#{permissaoFuncionalidade.escolhido}" rendered="#{funcionalidade.codigo == null}"
                                                                         itemLabel="#{title['permitir']}" disabled="#{!SuperControl.usuario.isItemHabilitado('PERFIL_USUARIO', 'EDITAR')}">
                                                    <p:ajax listener="#{ConfiguracaoControle.marcarTodos(perfil, permissaoFuncionalidade, categoria, false)}"
                                                            partialSubmit="true"
                                                            event="change"
                                                            process="@this"
                                                            update=":fmLay:tabConfigPerf:categorias:painelTab"/>

                                                </p:selectBooleanCheckbox>

                                            </h:panelGroup>
                                        </ui:repeat>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGroup>
                        </p:tab>
                    </p:accordionPanel>

                    <p:commandLink styleClass="btn btn-primary" action="#{ConfiguracaoControle.salvarNovo(perfil)}"
                                   ajax="true"  update=":fmLay:painelModal,:fmLay:tabConfigPerf:categorias:painelTab"
                                   rendered="#{perfil.codigo == null or perfil.codigo == 0}">
                        <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                    </p:commandLink>

                    <p:commandLink styleClass="btn btn-primary" action="#{ConfiguracaoControle.excluir(perfil)}" 
                                   rendered="#{perfil.codigo > 0 and perfil.permiteExclusao}" ajax="true" update="tabConfigPerf"
                                   disabled="#{SuperControl.usuario.isItemHabilitado('PERFIL_USUARIO', 'EXCLUIR')}">
                        <i class="fa-icon-remove"/> #{title['cadastros.excluir']}
                    </p:commandLink>
                </p:tab>
            </p:tabView>
        </h:panelGroup> 

    </p:dialog>
</ui:composition>