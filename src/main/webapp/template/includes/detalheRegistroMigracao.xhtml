<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
    xmlns:p="http://primefaces.org/ui">

    <p:dialog widgetVar="modalDetalheRegistroMigracao"
              showHeader="true"
              header="Registro de migração"
              modal="true"
              closable="true"
              width="720"
              height="500"
              id="detalheRegistroMigracaoDialog"
              hideEffect="fade"
              resizable="false">

        <h:panelGroup layout="block" id="detalhesRegistroMigracao">

            <h:panelGroup layout="block"
                          pt:data-spy="affix"
                          pt:data-offset-top="0">
                    <p:panelGrid style="margin-left: 1em; width: 100%;">
                        <p:row>
                            <p:column>
                                <h:outputText value="Codigo:"/>
                            </p:column>
                            <p:column>
                                <h:outputText value="#{MigracaoControle.registroMigracaoSelecionado.codigo}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column>
                                <h:outputText value="MD5:"/>
                            </p:column>
                            <p:column>
                                <h:outputText value="#{MigracaoControle.registroMigracaoSelecionado.md5}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column>
                                <h:outputText value="Tipo:"/>
                            </p:column>
                            <p:column>
                                <h:outputText value="#{MigracaoControle.registroMigracaoSelecionado.tipo.nome}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column>
                                <h:outputText value="Status:"/>
                            </p:column>
                            <p:column>
                                <h:outputText value="#{MigracaoControle.registroMigracaoSelecionado.status.nome}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column>
                                <h:outputText value="Codigo Externo:"/>
                            </p:column>
                            <p:column>
                                <h:outputText value="#{MigracaoControle.registroMigracaoSelecionado.codigoExterno}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column>
                                <h:outputText value="Codigo Interno:"/>
                            </p:column>
                            <p:column>
                                <h:outputText value="#{MigracaoControle.registroMigracaoSelecionado.codigoInterno}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column>
                                <h:outputText value="Data importação:"/>
                            </p:column>
                            <p:column>
                                <h:outputText value="#{MigracaoControle.registroMigracaoSelecionado.dataImportacaoApresentacao}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column colspan="2">
                                <h:outputText value="Mensagem:"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column colspan="2">
                                <label onclick="$(this).hide();$(this).next().show();"
                                        style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 250px;"
                                        title="#{solicitacaoMigracao.mensagem}">
                                     #{MigracaoControle.registroMigracaoSelecionado.ultimaMensagem}
                                 </label>

                                 <p:outputPanel id="listaMensagens"
                                                style="display: none !important;">
                                     <p:dataList value="#{MigracaoControle.registroMigracaoSelecionado.mensagens}"
                                                 var="mensagem">
                                         #{mensagem}
                                     </p:dataList>
                                 </p:outputPanel>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column colspan="2">
                                <h:outputText value="Conteúdo:"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column colspan="2">
                                <div style="word-break: break-all;" onclick="this.innerText = JSON.stringify(JSON.parse(this.innerText), undefined, 2);">
                                    #{MigracaoControle.registroMigracaoSelecionado.conteudo}
                                </div>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
            </h:panelGroup>
        </h:panelGroup>
    </p:dialog>
</ui:composition>