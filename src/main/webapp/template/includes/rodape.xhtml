<!--suppress XmlUnusedNamespaceDeclaration -->
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:fn="http://java.sun.com/jsp/jstl/functions"
    xmlns:c="http://java.sun.com/jsp/jstl/core">

    <span id="includeRodape">
        
    <div  class="navbar navbar-inverse navbar-fixed-bottom nav-bottom" style="overflow: hidden">
        <div class="navbar-inner" style="position: relative">
            <div class="container-fluid innerMNU statusBarra">
                <ui:fragment rendered="#{SuperControl.usuario.empresasZW.size() > 1}">
                    <ul class="nav pull-left">
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown"
                               style="background-color: darkblue;">
                                <i class="fa-icon-chevron-up"/>
                            </a>
                            <ul class="dropdown-menu">
                                <ui:repeat value="#{SuperControl.usuario.empresasZW}" var="emp">
                                    <ui:fragment rendered="#{SuperControl.usuario.empresaLogada.codigo != emp.codigo}">
                                        <li><h:commandLink actionListener="#{LoginControle.trocarEmpresa}">
                                                ${emp.nome}
                                                <f:param name="empresa" value="#{emp}"/>
                                            </h:commandLink>
                                        </li>
                                    </ui:fragment>
                                </ui:repeat>
                            </ul>
                        </li>
                    </ul>
                </ui:fragment>

                <div class="pull-left footerUnidade">#{SuperControl.usuario.empresaLogada.nome}</div>
                <h:panelGroup id="pnlBarraMeio" layout="block" styleClass="barraMeio">
                    <h:panelGroup layout="block" styleClass="span1 notificaBarraInferior #{NotificacaoControle.totalNaoLidas > 0 ? 'ativa' : ''}" rendered="#{!fn:endsWith(request.requestURI, 'notificacoes.xhtml')}">
                        <p:commandLink ajax="true" global="true" action="#{NotificacaoControle.listarRodape()}" update=":fmLay:pnlBarraMeio,:fmLay:pnlContemListaNotf"
                                       oncomplete="mostraEscondeNotifica()">
                            <f:param name="op" value="listarRodape"/>
                            <i class="fa-icon-asterisk"/>
                            <h:panelGroup layout="block" styleClass="contaNotifica">
                                <h:outputText escape="false" value="#{NotificacaoControle.totalNaoLidas > 0 ? NotificacaoControle.totalNaoLidas : ''}"/>
                            </h:panelGroup>
                        </p:commandLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="span1 alunosBarraInferior" rendered="#{!fn:endsWith(request.requestURI, 'alunos.xhtml')}">
                        <p:commandLink onclick="mostraEscondeAlunos()">
                            <i class="fa-icon-group"/>
                        </p:commandLink>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="pull-right" style="line-height: 30px; vertical-align: middle;padding-right: 5px;">
                    <span id="dataHoje">Segunda-feira, 30 de Setembro de 2013</span> <span id="horaAgora">11:39</span> <span id="dashFoo">-</span>
                    <span style="color: red; font-family: 'Oxygen Mono',sans-serif;" id="tempoLogout">30:00</span>
                </div>                
                <script>
                            startCountdown();
                </script>
            </div>
            <h:panelGroup layout="block" styleClass="popDoRodape contemUltimasNotifica">
                <h:panelGroup id="pnlContemListaNotf" layout="block" styleClass="contemTabelaNotifica semHeaderColunas">
                    <h:commandLink styleClass="headerPopDoRodape" onclick="return mostraEscondeNotifica();">
                        <h:outputLabel styleClass="pull-left" value="#{SuperControl.usuario.nomeApresentar}" />
                        <h:outputLabel styleClass="pull-right" value="#{fn:length(NotificacaoControle.notificacoes)}" />
                    </h:commandLink>
                    <c:set var="isRodapeTabela" value="listaNotfRodape"/>
                    <f:subview id="contemNotifGaveta">
                        <ui:include src="./tabelaNotificacoes.xhtml"/>
                    </f:subview>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup id="pnlContemListaAcomp" layout="block" styleClass="popDoRodape contemAcompanhados">
                <h:panelGroup layout="block" styleClass="contemTabelaAcomp semHeaderColunas">
                    <h:commandLink styleClass="headerPopDoRodape" onclick="return mostraEscondeAlunos();">
                        <h:outputLabel styleClass="pull-left" value="#{SuperControl.usuario.nomeApresentar}" />
                        <h:panelGroup styleClass="totalAcomp">
                            <h:outputText rendered="#{ClientesControle.totalTabela != fn:length(ClientesControle.clientes)}"
                                          value="#{ClientesControle.totalTabela} de "/>
                            <h:outputText value="#{fn:length(ClientesControle.clientes)} "/>
                            <h:outputText value="#{fn:length(ClientesControle.clientes) > 1 ? title['principal.alunos']: title['agenda.aluno']}"/>
                        </h:panelGroup>
                    </h:commandLink>
                    <f:subview id="contemAlunosGaveta">
                        <ui:include src="./tabelaAlunos.xhtml"/>
                    </f:subview>
                </h:panelGroup>
            </h:panelGroup>
        </div>
    </div>

    <ui:fragment rendered="#{!MenuControle.modoFullScreen and !(SuperControl.mobile and not SuperControl.smallMobile)}">
        <footer>
            <div class="footer">
                <div class="container narrow row-fluid">
                    <div class="span8">
                        <div class="span7">
                            <div class="span8 container">
                                <h3><h:outputText rendered="#{SuperControl.usuario.cadastroHabilitado}" value="Cadastros"/></h3>
                                <ol class="fadedSubs span6" style="margin-left: 0;">
                                    <li><h:commandLink action="pretty:musculo"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('MUSCULOS', 'CONSULTAR')}">
                                            #{title['cadastros.musculos']}</h:commandLink></li>

                                    <li><h:commandLink action="pretty:gmusculo"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('GRUPOS_MUSCULARES', 'CONSULTAR')}">
                                            #{title['cadastros.gruposmusculares']}</h:commandLink></li>

                                    <li><h:commandLink action="pretty:aparelhos"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('APARELHOS', 'CONSULTAR')}">
                                            #{title['cadastros.aparelhos']}</h:commandLink></li>

                                    <li><h:commandLink action="pretty:atividades"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('ATIVIDADES', 'CONSULTAR')}">
                                            #{title['cadastros.atividades']}</h:commandLink></li>

                                    <li><h:commandLink action="#{SuperControl.redirect('/cadastros/fichas/predefinidas')}"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('FICHAS_PRE_DEFINIDAS', 'CONSULTAR')}">
                                            Fichas Predefinidas</h:commandLink></li>
                                </ol>
                                <ol class="fadedSubs span6">
                                    <li><h:commandLink action="pretty:catatividades"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('CATEGORIA_ATIVIDADE', 'CONSULTAR')}">
                                            #{title['cadastros.categoriaAtividades']}</h:commandLink></li>

                                    <li><h:commandLink action="pretty:catfichas"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('CATEGORIA_FICHAS', 'CONSULTAR')}">
                                            #{title['cadastros.categoriaficha']}</h:commandLink></li>

                                    <li><h:commandLink action="pretty:nivel"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('NIVEIS', 'CONSULTAR')}">
                                            #{title['cadastros.nivel']}</h:commandLink></li>

                                    <li><h:commandLink action="pretty:objetivos"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('OBJETIVOS', 'CONSULTAR')}">
                                            #{title['cadastros.objetivos']}</h:commandLink></li>

                                    <li><h:commandLink action="pretty:imagem"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('IMAGENS', 'CONSULTAR')}">
                                            #{title['cadastros.imagens']}</h:commandLink></li>
                                </ol>
                            </div>
                            <ui:remove>
                                <div class="span4">
                                    <h3>Gestão</h3>
                                    <ol class="fadedSubs">
                                        <li>Geral</li>
                                    </ol>
                                </div>
                            </ui:remove>
                        </div>
                        <div class="span5">
                            <h3 style="color:rgba(0,0,0,0);">-</h3>

                            <div class="span6" style="margin-left: 0;">
                                <ol>

                                    <li><h:commandLink action="#{SuperControl.redirect('/alunos/todosalunos')}"
                                                       rendered="#{SuperControl.usuario.isItemHabilitado('ALUNOS', 'CONSULTAR')}">
                                            #{title['cadastros.todosalunos']}</h:commandLink></li>

                                    <li class=""><h:commandLink action="pretty:agenda">#{title['principal.agenda']}</h:commandLink></li>

                                    <li class=""><h:commandLink action="#{SuperControl.redirect('/notificacoes/listarTodas')}"
                                                                rendered="#{SuperControl.usuario.isItemHabilitado('NOTIFICACOES', 'CONSULTAR')}">
                                            #{title['principal.notificacoes']}</h:commandLink></li>

                                    <li class=""><h:commandLink action="pretty:acompanhar"
                                                                rendered="#{SuperControl.usuario.isItemHabilitado('ACOMPANHAR', 'CONSULTAR')}">Acompanhar</h:commandLink></li>
                                </ol>
                            </div>
                            <div class="span6">
                                <ol>
                                    <li>Suporte</li>
                                    <li>WikiPacto</li>
                                    <li>Configurações</li>
                                    <li>Sobre</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    <div class="span4 assinatura">
                        <h:graphicImage library="imagens" name="assi_logo.png"/>

                        <div class="sociais">
                            <h:graphicImage library="imagens" name="assi_twitter.png" style="margin-right: 10px;"/>
                            <h:graphicImage library="imagens" name="assi_face.png"/>
                        </div>
                        <div>
                            <div>Pacto Treino #{SuperControl.versao}</div>
                            <div>IP.: #{SuperControl.ip}</div>
                            <div>Copyright 2013-2017 © - Todos os direitos reservados</div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
        
    </ui:fragment>
        </span>
</ui:composition>