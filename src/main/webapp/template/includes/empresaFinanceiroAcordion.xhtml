<ui:composition
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:h="http://java.sun.com/jsf/html"
        xmlns:ui="http://java.sun.com/jsf/facelets"
        xmlns:c="http://java.sun.com/jsp/jstl/core"
        xmlns:p="http://primefaces.org/ui"
        xmlns:pt="http://xmlns.jcp.org/jsf/passthrough">

    <p:panel id="formularioEmpresaDaRede">
        <h:outputScript>
            $(PrimeFaces.escapeClientId("fmLay:txtPesquisarContato_input")).val('');
            $(PrimeFaces.escapeClientId("fmLay:txtPesquisarEmpresa_input")).val('');
        </h:outputScript>

        <style>
            input::-webkit-input-placeholder {
                background-color: white;
                color: #B4B4B4 !important;
                border-color: #777 !important;
                opacity: 1 !important;
            }
        </style>

        <c:set var="idFormularioEmpresaDaRede" value=":fmLay:empresasDaRedeAccordion:#{RedeEmpresaControle.indiceAtivoDoAccordion}:formularioEmpresaDaRede" />

        <h:panelGroup id="containerDadosRedeEmpresa" layout="block" styleClass="container-fluid content" style="min-height: 800px;">
            <h:panelGroup layout="block" styleClass="row-fluid">
                <h:panelGroup layout="block" styleClass="col-md-12" style="padding: 10px;">
                    <h1>Adicionar nova empresa à rede</h1>

                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="form-group col-md-10">
                <h:inputText id="titulo"
                             style="width: 85%;  margin-left: 110px;background-color: white;color: #777 !important;border-color: #777 !important;opacity: 1 !important;"
                             styleClass="form-control input-lg text-left"
                             pt:placeholder="Título"
                             value="#{empresaDaRede.nomeResumo}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="form-group col-md-10">
                <p:autoComplete id="txtPesquisarEmpresa" multiple="false"
                                converter="empresaFinanceiroConverter"
                                inputStyleClass="form-control input-lg text-left inputGrupo txtPesquisaEmpresa"
                                placeholder="Pesquisar Academia"
                                inputStyle="background-color: white;color: #777 !important;border-color: #777 !important;opacity: 1 !important;"
                                style="width: 85%;  margin-left: 110px;color:#777 !important;"
                                forceSelection="true"
                                cache="true"
                                emptyMessage="Pesquisar Academia"
                                maxResults="10"
                                var="emp"
                                itemLabel="#{emp.nomeResumo}"
                                itemValue="#{emp}"
                                value="#{RedeEmpresaControle.empresaFinanceiro}"
                                completeMethod="#{RedeEmpresaControle.buscarEmpresas}">
                    <p:ajax event="itemSelect" listener="#{RedeEmpresaControle.empresaSelect}"/>
                </p:autoComplete>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="form-group col-md-2">
                <p:commandLink action="#{RedeEmpresaControle.addEmpresaSelecionada()}"
                               styleClass="btn btn-primary"
                               update="#{idFormularioRede}"
                               style="margin-left: 5px;padding: 11px;color: #fff">
                    <i class="fa fa-plus-circle fa-3"></i>
                </p:commandLink>
            </h:panelGroup>




        </h:panelGroup>
    </p:panel>
</ui:composition>
