<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
    xmlns:fn="http://java.sun.com/jsp/jstl/functions"
    xmlns:p="http://primefaces.org/ui"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <ui:fragment>
        <p:dataTable var="cliente" value="#{ClientesControle.listaTela}"                    
                     emptyMessage="#{msg['tabela.semregistros']}"
                     id="listaAlunos" styleClass="tabelaUsuarios scrollInfinito">            
            <f:facet name="header">
                <p:outputPanel styleClass="tabelaHeaderFull" rendered="#{fn:endsWith(request.requestURI, 'alunos.xhtml')}">
                    <h:panelGroup layout="block" styleClass="blocoQuantidadeAlunos">
                        <h:outputText rendered="#{ClientesControle.totalTabela != fn:length(ClientesControle.clientes)}"
                                      value="#{ClientesControle.totalTabela} de "/>
                        <h:outputText value="#{fn:length(ClientesControle.clientes)} "/>
                        <h:outputText value="#{fn:length(ClientesControle.clientes) > 1 ? title['principal.alunos']: title['agenda.aluno']}"/>

                    </h:panelGroup>
                </p:outputPanel>
            </f:facet>
            <p:column styleClass="span1" style="text-align: center;">
                <f:facet name="header">
                    <i class='fa-icon-picture'/>
                </f:facet>                
                <p:commandLink action="#{ClientesControle.clienteAtual}">                    
                    <p:graphicImage cache="true"
                        url="#{SuperControl.defineUrlFoto(SuperControl.usuario.chave, cliente.codigoPessoa)}" />
                    <f:attribute name="ts" value="#{SuperControl.timeStamp}"/>
                    <f:setPropertyActionListener value="#{cliente}" target="#{ClientesControle.cliente}" />
                </p:commandLink>
            </p:column>
            <p:column filterBy="#{cliente.matricula}" filterMatchMode="contains" headerText="Matricula">
                <p:commandLink
                    action="#{ClientesControle.clienteAtual(cliente)}">#{cliente.matricula}</p:commandLink>
            </p:column>
            <p:column filterBy="#{cliente.nome}" filterMatchMode="contains" headerText="Nome">
                <p:commandLink
                    action="#{ClientesControle.clienteAtual(cliente)}">#{cliente.getNomeMobile(SuperControl.mobile)}</p:commandLink>
            </p:column>
            <p:column filterBy="#{cliente.professorSintetico.nome}" filterMatchMode="contains" headerText="Professor" rendered="#{fn:endsWith(request.requestURI, 'alunos.xhtml')}">
                <p:commandLink
                    action="#{ClientesControle.clienteAtual(cliente)}">#{cliente.professorSintetico.nomeAbreviado}</p:commandLink>
            </p:column>
        </p:dataTable>
    </ui:fragment>
</ui:composition>