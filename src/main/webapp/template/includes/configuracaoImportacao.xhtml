<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
    xmlns:p="http://primefaces.org/ui">

    <p:dialog widgetVar="modalConfiguracaoMigracao"
              showHeader="true"
              header="Configuracao de migração"
              modal="true"
              closable="false"
              width="720"
              height="500"
              id="configuracaoImportacaoDialog"
              hideEffect="fade"
              resizable="false">

        <h:panelGroup layout="block" id="detalhesConfiguracaoImportacao">

            <h:panelGroup layout="block"
                          pt:data-spy="affix"
                          pt:data-offset-top="0">
                <h:form id="form" enctype="multipart/form-data">
                    <p:panelGrid style="margin-left: 1em; width: 100%;" id="painel">
                        <p:row>
                            <p:column>
                                <h:outputText for="chave" value="Chave:"/>
                            </p:column>
                            <p:column>
                                <h:inputText id="chave"
                                             styleClass="width-90-perc"
                                             value="#{MigracaoControle.chave}"
                                             disabled="true"
                                             title="Chave da empresa"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column>
                                <h:outputText for="codigo" value="Codigo Empresa:"/>
                            </p:column>
                            <p:column>
                                <h:inputText id="codigo"
                                             styleClass="width-90-perc"
                                             value="#{MigracaoControle.codigoEmpresa}"
                                             disabled="true"
                                             title="Código da empresa"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column>
                                <h:outputText for="tipoFonteImportacao" value="Fonte de importação: " />
                            </p:column>
                            <p:column>
                                <p:selectOneMenu id="tipoFonteImportacao"
                                                 value="#{MigracaoControle.configuracaoImportacao.tipoFonteImportacao}">
                                    <f:selectItem itemLabel="Selecione uma fonte " itemValue=""/>
                                    <f:selectItems value="#{MigracaoControle.listaFonteImportacao}" />
                                    <p:ajax update="painel" listener="#{MigracaoControle.onSelectDataSource}" />
                                </p:selectOneMenu>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column colspan="2" rendered="#{not empty MigracaoControle.configuracaoImportacao.listaDataSourceImportacao}">
                                <h5>DataSources</h5>
                                <hr/>
                                <ui:repeat value="#{MigracaoControle.configuracaoImportacao.listaDataSourceImportacao}" var="dataSourceImportacao">
                                    <ui:fragment rendered="#{dataSourceImportacao.tipoDataSource.requerConexaoBD}">
                                        <p:panelGrid>
                                            <p:row>
                                                <p:column>
                                                    <h:outputText value="Descrição:"/>
                                                </p:column>
                                                <p:column>
                                                    <h:outputText value="#{dataSourceImportacao.tipoDataSource.descricao}"/>
                                                </p:column>
                                            </p:row>
                                            <p:row>
                                                <p:column>
                                                    <h:outputText value="Host:"/>
                                                </p:column>
                                                <p:column>
                                                    <h:inputText value="#{dataSourceImportacao.host}"/>
                                                </p:column>
                                            </p:row>
                                            <p:row>
                                                <p:column>
                                                    <h:outputText value="Porta:"/>
                                                </p:column>
                                                <p:column>
                                                    <h:inputText value="#{dataSourceImportacao.porta}"/>
                                                </p:column>
                                            </p:row>
                                            <p:row>
                                                <p:column>
                                                    <h:outputText value="Nome BD:"/>
                                                </p:column>
                                                <p:column>
                                                    <h:inputText value="#{dataSourceImportacao.nome}"/>
                                                </p:column>
                                            </p:row>
                                            <p:row>
                                                <p:column>
                                                    <h:outputText value="Usuário:"/>
                                                </p:column>
                                                <p:column>
                                                    <h:inputText value="#{dataSourceImportacao.usuario}"/>
                                                </p:column>
                                            </p:row>
                                            <p:row>
                                                <p:column>
                                                    <h:outputText value="Senha:"/>
                                                </p:column>
                                                <p:column>
                                                    <h:inputText value="#{dataSourceImportacao.senha}"/>
                                                </p:column>
                                            </p:row>
                                        </p:panelGrid>
                                    </ui:fragment>
                                    <ui:fragment rendered="#{dataSourceImportacao.tipoDataSource.requerUploadArquivo}">
                                        <p:panelGrid id="dataSourceGrid">
                                            <p:row>
                                                <p:column>
                                                    <h:outputText value="Descrição:"/>
                                                </p:column>
                                                <p:column>
                                                    <h:outputText value="#{dataSourceImportacao.tipoDataSource.descricao}"/>
                                                </p:column>
                                            </p:row>
                                            <p:row>
                                                <p:column>
                                                    <h:outputText value="Arquivo:"/>
                                                </p:column>
                                                <p:column>
                                                    <p:fileUpload fileUploadListener="#{MigracaoControle.onUploadArquivo}"
                                                                  label="Selecionar"
                                                                  cancelLabel="Cancelar"
                                                                  mode="advanced"
                                                                  dragDropSupport="false"
                                                                  fileLimit="1"
                                                                  rendered="#{empty dataSourceImportacao.nomeArquivo}"
                                                                  update="dataSourceGrid">
                                                        <f:attribute name="dataSourceImportacao" value="#{dataSourceImportacao}" />
                                                    </p:fileUpload>
                                                    <h:panelGroup rendered="#{not empty dataSourceImportacao.nomeArquivo}" layout="block">
                                                        <div class="ui-fileupload-progress" style="float: left">
                                                            <input type="text" value="#{dataSourceImportacao.nomeArquivo}" title="#{dataSourceImportacao.nomeArquivo}" disabled="true"/>
                                                            <h:outputText value="&#160;" />
                                                        </div>
                                                        <p:commandButton
                                                                style="float: left"
                                                                icon="ui-icon-close"
                                                                update="dataSourceGrid"
                                                                action="#{MigracaoControle.removerArquivoDataSourceImportacao(dataSourceImportacao)}"/>
                                                        <br style="clear: both"/>
                                                    </h:panelGroup>
                                                </p:column>
                                            </p:row>
                                        </p:panelGrid>
                                    </ui:fragment>
                                    <hr/>
                                </ui:repeat>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column colspan="2">
                                <p:commandLink
                                        ajax="true"
                                        process="form"
                                        action="#{MigracaoControle.salvarConfiguracaoImportacao}"
                                        oncomplete="#{MigracaoControle.onCompleteAcao}"
                                        style="margin-right: 1em;margin-bottom: 1em;"
                                        styleClass="btn btn-primary pull-right">
                                    <i class="fa-icon-save"/> #{title['cadastros.salvar']}
                                </p:commandLink>
                                <p:commandLink
                                        process="form"
                                        oncomplete="modalConfiguracaoMigracao.hide();"
                                        style="margin-right: 1em;margin-bottom: 1em;"
                                        styleClass="btn btn-secundary pull-right">
                                    <i class="fa-icon-remove"/> #{title['cancelar']}
                                </p:commandLink>
                            </p:column>
                        </p:row>
                    </p:panelGrid>
                </h:form>
            </h:panelGroup>
        </h:panelGroup>
    </p:dialog>
</ui:composition>