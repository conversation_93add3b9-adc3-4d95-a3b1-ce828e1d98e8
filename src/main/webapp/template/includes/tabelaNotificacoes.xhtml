<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">
    <c:set var="telaAluno" value="#{fn:endsWith(request.requestURI, 'aluno.xhtml')}" scope="view"/>
    <ui:fragment rendered="#{!(NotificacaoControle.mostrarData)}">
        <p:dataTable var="notificacao" value="#{NotificacaoControle.notificacoes}" id="listaNotfRodape"
                     emptyMessage="#{msg['tabela.semregistros']}"
                     rowStyleClass="#{notificacao.tipo.style}">
            <p:column headerText="Notificação" styleClass="linhaNotifica">
                <p:commandLink action="#{NotificacaoControle.redirect(('/aluno/').concat(notificacao.cliente.codigo))}">
                    <h:graphicImage width="48" height="48"
                                    url="#{SuperControl.defineUrlFoto(SuperControl.usuario.chave, notificacao.cliente.codigoPessoa)}"/>
                    <h:outputText value="#{notificacao.cliente.nomeAbreviado} "/>
                    <h:outputText style="font-weight: bold" value="#{notificacao.nome}"/>
                    <ui:fragment rendered="#{notificacao.serieRealizada != null}">
                        <h:outputText value=" da atividade "/>
                        <h:outputText style="font-weight: bold"
                                      value="#{notificacao.serieRealizada.atividadeFicha.atividade.nome}"/>
                    </ui:fragment>
                </p:commandLink>
            </p:column>            
        </p:dataTable>
    </ui:fragment>
    <ui:fragment rendered="#{NotificacaoControle.mostrarData}">
        <p:dataTable var="notificacao" value="#{NotificacaoControle.notificacoes}" id="listaNotif"
                     emptyMessage="#{msg['tabela.semregistros']}"
                     rowStyleClass="#{notificacao.tipo.style}">
            <p:column headerText="Notificação">
                <p:commandLink style="#{telaAluno ? 'padding: 0px 0px 0px 0px; font-size: 12px;' : ''}"
                               action="#{NotificacaoControle.redirect(('/aluno/').concat(notificacao.cliente.codigo))}">
                    <h:graphicImage rendered="#{!telaAluno}"
                                    url="#{SuperControl.defineUrlFoto(SuperControl.usuario.chave, notificacao.cliente.codigoPessoa)}"/>
                    <h:outputText rendered="#{!telaAluno}" value="#{notificacao.cliente.nomeAbreviado} "/>
                    <h:outputText style="font-weight: bold" value="#{notificacao.nome}"/>
                    <ui:fragment rendered="#{notificacao.serieRealizada != null}">
                        <h:outputText value=" na atividade "/>
                        <h:outputText style="font-weight: bold"
                                      value="#{notificacao.serieRealizada.atividadeFicha.atividade.nome}"/>
                    </ui:fragment>
                </p:commandLink>
            </p:column>
            <p:column headerText="Data">
                <p:commandLink action="#{NotificacaoControle.redirect(('/aluno/').concat(notificacao.cliente.codigo))}">
                    <h:outputText value="#{notificacao.dataRegistro}">
                        <f:convertDateTime timeZone="#{SuperControl.usuario.timeZone}" pattern="dd/MM HH:mm:ss"/>
                    </h:outputText>
                </p:commandLink>
            </p:column>
            <p:column>
                <h:panelGroup rendered="#{notificacao.pushEnviado}">
                    <i title="#{title['notificacao.push']}" class="fa-icon-asterisk"/>
                </h:panelGroup>
                <h:panelGroup rendered="#{notificacao.smsEnviado}">
                    <i title="#{title['notificacao.sms']}" class="fa-icon-envelope"/>
                </h:panelGroup>
            </p:column>
        </p:dataTable>
    </ui:fragment>
</ui:composition>