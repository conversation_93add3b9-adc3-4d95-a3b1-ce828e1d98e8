<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui"
    xmlns:h="http://xmlns.jcp.org/jsf/html">
    <style>
        .ui-log {
            top: 120px;
            left: 320px;
            width: 1024px;
            height: 150px;
        }
    </style>
    <!--<p:log />-->
    <p:dataTable var="obj" value="#{ListaControle.objects}"
                 id="tabelaGenerica"
                 scrollable="#{ListaControle.scrollable}"
                 scrollHeight="#{ListaControle.scrollHeight}"
                 emptyMessage="#{msg['tabela.semregistros']}">
        <f:facet name="header">
            <h:outputText value="#{ListaControle.headerTable}"/>
        </f:facet>

        <p:columns value="#{ListaControle.columns}" var="column" columnIndexVar="colIndex" 
                   sortBy="#{obj[column.property]}">
            <f:facet name="header">
                <h:outputText value="#{column.header}"/>
            </f:facet>
            <h:outputText rendered="#{!column.link}" value="#{obj[column.property]}"/>
            
            <h:commandLink rendered="#{column.link}" target="cli#{obj['chaveLink']}" 
                          action="#{GestaoControle.clienteAtual(obj['chaveLink'])}">
                <h:outputText  value="#{obj[column.property]}"/>
            </h:commandLink>
            
        </p:columns>
        <f:facet name="footer">  
            #{fn:length(ListaControle.objects)} #{title['itens']}
        </f:facet>

    </p:dataTable>
    <p:commandButton ajax="false" partialSubmit="false" value="XML">
        <p:dataExporter type="xml" target="tabelaGenerica" fileName="#{ListaControle.headerTable}"/>
    </p:commandButton>
    
    <p:commandButton ajax="false" partialSubmit="false" value="XLS">
        <p:dataExporter type="xls" target="tabelaGenerica" fileName="#{ListaControle.headerTable}" 
                        postProcessor="#{ListaControle.postProcessXLS}"/>
    </p:commandButton>

    <p:commandButton ajax="false" partialSubmit="false" value="PDF"
                     actionListener="#{ListaControle.listenerNome}">
        <p:dataExporter type="pdf" target="tabelaGenerica" fileName="#{ListaControle.headerTable}" 
                        preProcessor="#{ListaControle.preProcessPDF}"/>
        <f:attribute name="tituloPdf" 
                     value="#{ListaControle.headerTable}"/>
    </p:commandButton>
</ui:composition>