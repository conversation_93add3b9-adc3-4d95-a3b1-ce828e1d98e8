<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:p="http://primefaces.org/ui">

    <p:dialog widgetVar="modalMigracaoRegistro"
              showHeader="true"
              modal="true"
              closable="true"
              width="720"
              height="500"
              id="migracaoRegistroDialog"
              hideEffect="fade"
              resizable="false">
        <f:facet name="header">
            <h:outputText value="Importação de registros"
                          rendered="#{MigracaoControle.solicitacaoImportacaoSelecionado}"/>
            <h:outputText value="Exportação de registros"
                          rendered="#{not MigracaoControle.solicitacaoImportacaoSelecionado}"/>
        </f:facet>

        <h:panelGroup layout="block" id="detalhesMigracaoRegistro">

            <h:panelGroup layout="block"
                          pt:data-spy="affix"
                          pt:data-offset-top="0">

                <h:form id="formMigracaoRegistro">
                    <p:panelGrid style="margin-left: 1em; width: 100%;" id="painel">
                        <p:row>
                            <p:column>
                                <table>
                                    <tr>
                                        <td>
                                            <h:outputText for="opcoesTipoRegistroMigracao" value="Tipo de registro: "/>
                                        </td>
                                        <td style="width: 50%;">
                                            <p:selectOneMenu id="opcoesTipoRegistroMigracao" value="#{MigracaoControle.tipoRegistroMigracaoSelecionado}">
                                                <f:selectItem itemLabel="TODOS" itemValue=""/>
                                                <f:selectItems value="#{MigracaoControle.tiposRegistroMigracao}" />
                                            </p:selectOneMenu>
                                        </td>
                                        <td>
                                            <p:commandLink
                                                    action="#{MigracaoControle.migrarRegistros()}"
                                                    ajax="true"
                                                    process="formMigracaoRegistro"
                                                    rendered="#{not MigracaoControle.solicitacaoImportacaoSelecionado}"
                                                    update="tabelaSolicitacaoMigracao"
                                                    styleClass="btn btn-default">
                                                <i class="fa-icon-arrow-up"/> Exportar
                                            </p:commandLink>
                                            <p:commandLink
                                                    action="#{MigracaoControle.migrarRegistros()}"
                                                    ajax="true"
                                                    process="formMigracaoRegistro"
                                                    rendered="#{MigracaoControle.solicitacaoImportacaoSelecionado}"
                                                    update="tabelaSolicitacaoMigracao"
                                                    styleClass="btn btn-default">
                                                <i class="fa-icon-arrow-down"/> Importar
                                            </p:commandLink>
                                        </td>
                                    </tr>
                                </table>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column>
                                <hr/>
                                <p:dataTable var="solicitacaoMigracao"
                                             id="tabelaSolicitacaoMigracao"
                                             value="#{MigracaoControle.listarSolicitacoesMigracao()}"
                                             emptyMessage="#{msg['tabela.semregistros']}"
                                             tableStyleClass="table table-striped table-hover"
                                             tableStyle="width: 670px;">

                                    <f:facet name="header">
                                        Solicitações de migração
                                        <h:panelGroup styleClass="pull-right">
                                            <p:commandLink partialSubmit="true"
                                                           update=":fmLay:formMigracaoRegistro:tabelaSolicitacaoMigracao"
                                                           title="Atualizar tabela">
                                                <i class="fa-icon-refresh"/>
                                            </p:commandLink>
                                        </h:panelGroup>
                                    </f:facet>
                                    <p:column headerText="Data de criação">
                                        <h:outputText value="#{solicitacaoMigracao.dataCriacaoApresentacao}"/>
                                    </p:column>
                                    <p:column headerText="Tipo de registro">
                                        <h:outputText value="TODOS" rendered="#{empty solicitacaoMigracao.tipoRegistroMigracao}"/>
                                        <h:outputText value="#{solicitacaoMigracao.tipoRegistroMigracao.nome}"
                                                      rendered="#{not empty solicitacaoMigracao.tipoRegistroMigracao}"/>
                                    </p:column>
                                    <p:column headerText="Tipo">
                                        <h:outputText value="#{solicitacaoMigracao.tipo.nome}"/>
                                    </p:column>
                                    <p:column headerText="">
                                        <label onclick="$(this).hide();$(this).next().show();"
                                               style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 250px;"
                                               title="#{solicitacaoMigracao.descricao}">
                                            #{solicitacaoMigracao.ultimaMensagem}
                                        </label>

                                        <p:outputPanel id="listaMensagens"
                                                       style="display: none !important;">
                                            <p:dataList value="#{solicitacaoMigracao.mensagens}"
                                                        var="mensagem">
                                                #{mensagem}
                                            </p:dataList>
                                        </p:outputPanel>

                                    </p:column>
                                </p:dataTable>

                            </p:column>
                        </p:row>
                    </p:panelGrid>

                </h:form>
            </h:panelGroup>
        </h:panelGroup>
    </p:dialog>
</ui:composition>