<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
    xmlns:c="http://xmlns.jcp.org/jsp/jstl/core" xmlns:p="http://primefaces.org/ui">

    <h:panelGroup layout="block" styleClass="navbar navbar-inverse navbar-fixed-top" id="topoMenu" rendered="#{!MenuControle.modoFullScreen}">
        <div class="navbar-inner">
            <div class="container-fluid">
                <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                    <span class="icon-bar"/>
                    <span class="icon-bar"/>
                    <span class="icon-bar"/>
                </button>
                <a class="brand" href="#">
                    <p:graphicImage  cache="true"  library="imagens" name="pacto_home.png" style="height:50px;"/>                    
                </a>

                <div class="topper">
                    <ul class="nav pull-right">
                        <ui:fragment rendered="#{MenuControle.habilitadoModuloFINAN}">
                            <li class="finan dropdown">
                                <h:outputLink target="finan" value="#{MenuControle.urlFINAN}">
                                    <p:graphicImage cache="true" library="imagens" name="logo_finan.png"/>
                                </h:outputLink>
                            </li>
                        </ui:fragment>

                        <ui:fragment rendered="#{MenuControle.habilitadoModuloCRM}">
                            <li class="crm dropdown">
                                <h:outputLink target="crm" value="#{MenuControle.urlCRM}">
                                    <p:graphicImage cache="true" library="imagens" name="logo_crm.png"/>
                                </h:outputLink>
                            </li>
                        </ui:fragment>
                        <li class="zw dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <p:graphicImage cache="true" library="imagens" name="logo_zw.png"/>
                            </a>
                            <ul class="dropdown-menu drop-sub">
                                <ui:fragment rendered="#{MenuControle.habilitadoModuloZW}">
                                    <li><h:outputLink target="zw"
                                                      value="#{MenuControle.urlZW_Inicial}">Inicial</h:outputLink></li>
                                </ui:fragment>
                                <ui:fragment rendered="#{MenuControle.habilitadoModuloZW}">
                                    <li><h:outputLink target="zw"
                                                      value="#{MenuControle.urlZW_Cadastros}">Cadastros</h:outputLink>
                                    </li>
                                </ui:fragment>
                                <ui:fragment rendered="#{MenuControle.habilitadoModuloZW}">
                                    <li><h:outputLink target="zw"
                                                      value="#{MenuControle.urlZW_BI}">Business Intelligence</h:outputLink>
                                    </li>
                                </ui:fragment>
                                <ui:fragment rendered="#{MenuControle.habilitadoModuloZW}">
                                    <li><h:outputLink target="zw"
                                                      value="#{MenuControle.urlZW_Clientes}">Clientes</h:outputLink>
                                    </li>
                                </ui:fragment>
                                <ui:fragment rendered="#{MenuControle.habilitadoModuloStudio}">
                                    <li><h:outputLink target="zw"
                                                      value="#{MenuControle.urlZW_Agenda}">Agenda</h:outputLink></li>
                                </ui:fragment>
                            </ul>
                        </li>
                        <li class="atual"><p:graphicImage cache="true" library="imagens" name="logo_treino.png"/></li>
                    </ul>
                </div>
                <div class="nav-collapse collapse">
                    <ul class="nav pull-right mainMNU">
                        <li class="modulo"><p:commandLink title="#{msg['alternarModulos']}" onclick="return false;"  id="lnkGotoModulo"/></li>
                        <li class="menuToggleMobile" onclick="modalMenuMobile.show()"><i class="fa-icon-align-justify"/></li>
                        <li class="modFull">
                            <p:commandLink id="lnkEnableModoFullScreen" title="#{msg['habilitar']} #{msg['modoFullScreen']}" actionListener="#{MenuControle.toggleFullScreen(!MenuControle.modoFullScreen)}"
                                           action="pretty:" style="padding: 0;" ajax="false">
                                <p:graphicImage cache="true" library="svg" name="fullscreen_go.svg" style="width: 40px; padding: 0px; margin: -8px;"/>
                            </p:commandLink>
                        </li>
                        <p:hotkey rendered="#{SuperControl.usuario.isItemHabilitado('ADD_ALUNO', 'CONSULTAR')}" 
                                  global="false" bind="ctrl+shift+a" action="pretty:adicionar"/>
                        <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('ADD_ALUNO', 'CONSULTAR')}">
                            <li style="padding: 0;"><p:commandLink id="lnkAddAluno" title="#{msg['addAluno']}" action="pretty:adicionar" style="padding: 0">
                                    <p:graphicImage cache="true"  library="svg" name="icon_add.svg" style="width: 35px; padding: 0px; margin: -5px;"/>
                                </p:commandLink></li>
                        </ui:fragment>
                        <li style="padding: 0;">
                            <h:outputLink id="lnkWiki" value="https://app.pactosolucoes.com.br/wiki/index.php/ZillyonWeb:Treino"
                                          title="#{msg['wiki']}"
                                          target="wiki"
                                          style="padding: 0">
                                <p:graphicImage cache="true" library="svg" name="icon_wiki.svg" style="width: 35px; padding: 0px; margin: -5px;"/>
                            </h:outputLink>
                        </li>

                        <li style="padding: 0;">
                            <p:commandLink title="#{msg['entrarModoAtendimento']}" id="lnkModoAtendimento" style="padding: 0" >
                                <p:graphicImage cache="true" library="svg" name="icon_atendimento.svg" style="width: 35px; padding: 0px; margin: -5px;"/>
                            </p:commandLink>
                        </li>


                        <li class="dropdown conf">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown"/>
                            <ul class="dropdown-menu">
                                <li class="avatar">
                                    <img src="#{SuperControl.usuario.avatarEmpresa}" width="80%"/>
                                </li>
                                <li class="hotLinkOpcoes">
                                    <p:commandLink id="lnkConfiguracoes" title="#{msg['configuracoes']}" action="#{ConfiguracaoControle.abrirModal('PERFIL')}" update="configuracoesModal" 
                                                   oncomplete="modalConfs.show();" styleClass="itemOp itemUm"
                                                   rendered="#{SuperControl.usuario.isItemHabilitado('PERFIL_USUARIO', 'CONSULTAR')}">
                                        <i class="fa-icon-group"/>
                                        <h:outputText styleClass="etiqueta" value="#{title['editarPerfil']}"/>
                                    </p:commandLink>

                                    <p:commandLink action="#{ConfiguracaoControle.abrirModal('USUARIOS')}" update="configuracoesModal" 
                                                   oncomplete="modalConfs.show()" styleClass="itemOp itemUm"
                                                   rendered="#{SuperControl.usuario.isItemHabilitado('USUARIOS', 'CONSULTAR')}">
                                        <i class="fa-icon-group"/>
                                        <h:outputText styleClass="etiqueta" value="#{title['editarUsuario']}"/>
                                    </p:commandLink>

                                    <p:commandLink action="#{ConfiguracaoControle.abrirModal('GLOBAIS')}"  update="configuracoesModal" 
                                                   oncomplete="modalConfs.show()" styleClass="itemOp itemUm"
                                                   rendered="#{SuperControl.usuario.isItemHabilitado('CONFIGURACOES_EMPRESA', 'EDITAR')}">
                                        <i class="fa-icon-group"/>
                                        <h:outputText styleClass="etiqueta" value="Configurações"/>
                                    </p:commandLink>


                                </li>
                            </ul>
                        </li>
                        <li class="dropdown menuUsuarioPegar">
                            <a href="#" class="dropdown-toggle usrMenu"
                               data-toggle="dropdown">
                                <p:graphicImage cache="true" url="#{SuperControl.usuario.avatar}" style="width: 48px; height: 48px;display: inline-block;background-color: white;"/>
                                <!--b></b-->
                                <i class="fa-icon-caret-down"/>
                            </a>
                            <ul class="dropdown-menu">
                                <li class="avatar">
                                    <h:panelGroup layout="block" styleClass="avartarEditar">
                                        <p:graphicImage cache="true" url="#{SuperControl.usuario.avatar}"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="avatarNome">
                                        <h:outputText value="#{SuperControl.usuario.nomeApresentarLower}" />
                                    </h:panelGroup>
                                </li>
                                <li class="hotLinkOpcoes">
                                    <p:commandLink styleClass="itemOp itemUm">
                                        <i class="fa-icon-user"/>
                                        <h:outputText styleClass="etiqueta" value="perfil"/>
                                    </p:commandLink>
                                    <p:commandLink styleClass="itemOp itemDois">
                                        <i class="fa-icon-cogs"/>
                                        <h:outputText styleClass="etiqueta" value="Edtar Perfil"/>
                                    </p:commandLink>
                                    <p:commandLink styleClass="itemOp itemTres"  
                                                   action="#{ConfiguracaoControle.abrirModal('USUARIO')}" 
                                                   update="configuracoesModal" 
                                                   oncomplete="modalConfs.show()">
                                        <i class="fa-icon-key"/>
                                        <h:outputText styleClass="etiqueta" value="Alterar Senha"/>
                                    </p:commandLink>
                                </li>
                                <li>
                                    <p:accordionPanel activeIndex="null">
                                        <p:tab>
                                            <f:facet name="title">
                                                <h:outputText value="#{language.localeCode}"/>
                                            </f:facet>
                                            <p:dataTable value="#{language.linguas}" var="lingua">
                                                <p:column>
                                                    <p:commandLink action="#{language.localeCode}" value="#{lingua}" />
                                                </p:column>
                                            </p:dataTable>
                                        </p:tab>
                                    </p:accordionPanel>
                                </li>
                                <li>
                                    <p:commandLink ajax="true" onclick="logoutRemote();" global="false" partialSubmit="true" process="@none" styleClass="linkLogout">
                                        <i class="fa-icon-remove"/> Sair
                                    </p:commandLink>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
                <!--/.nav-collapse -->
            </div>

            <div class="container-fluid innerMNU innerTopo">
                <ul class="nav pull-left">
                    <li class="active"><p:commandLink action="pretty:home">
                            <p:graphicImage cache="true" library="imagens" name="home-menu.png" width="16" height="16"/>
                        </p:commandLink>
                    </li>
                    <li class="dropdown">
                        <ui:fragment rendered="#{SuperControl.usuario.cadastroHabilitado}">
                            <a class="dropdown-toggle" data-toggle="dropdown" href="#">#{title['principal.cadastros']}</a>
                        </ui:fragment>
                        <ul class="dropdown-menu drop-sub">
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('MUSCULOS', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:musculo">#{title['cadastros.musculos']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('GRUPOS_MUSCULARES', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:gmusculo">#{title['cadastros.gruposmusculares']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('APARELHOS', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:aparelhos">#{title['cadastros.aparelhos']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('CATEGORIA_ATIVIDADE', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:catatividades">#{title['cadastros.categoriaAtividades']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('NIVEIS', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:nivel">#{title['cadastros.nivel']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('ATIVIDADES', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:atividades">#{title['cadastros.atividades']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('FICHAS_PRE_DEFINIDAS', 'CONSULTAR')}">
                                <li><h:commandLink action="#{SuperControl.redirect('/cadastros/fichas/predefinidas')}">#{title['cadastros.fichas.predefinidas']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('CATEGORIA_FICHAS', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:catfichas">#{title['cadastros.categoriaficha']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('OBJETIVOS', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:objetivos">#{title['cadastros.objetivos']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('IMAGENS', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:imagem">#{title['cadastros.imagens']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('BADGES', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:badge">#{title['badge']}</h:commandLink></li>
                            </ui:fragment>
                            <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('TIPO_EVENTO', 'CONSULTAR')}">
                                <li><h:commandLink action="pretty:tiposEvento">#{title['cadastros.tipoEvento']}</h:commandLink></li>
                            </ui:fragment>
                        </ul>
                    </li>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('GESTAO', 'CONSULTAR')}">
                        <li class="dropdown">
                            <a class="dropdown-toggle" data-toggle="dropdown" href="#">#{title['principal.gestao']}</a>
                            <ul class="dropdown-menu drop-sub">
                                <li><h:commandLink action="pretty:gestao">#{title['AGENDA']}</h:commandLink></li>
                                <li><h:commandLink action="#{SuperControl.redirect('/gestao/carteiras')}">#{title['carteiras']}</h:commandLink></li>
                                <li><h:commandLink action="#{SuperControl.redirect('/gestao/atividades')}">#{title['atividadesProfessores']}</h:commandLink></li>
                                <li><h:commandLink action="#{SuperControl.redirect('/gestaoNotificacoes/notificacoes')}">#{title['principal.notificacoes']}</h:commandLink></li>
                            </ul>
                        </li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('ALUNOS', 'CONSULTAR')}">
                        <li><h:commandLink action="#{SuperControl.redirect('/alunos/todosalunos')}">#{title['cadastros.todosalunos']}</h:commandLink></li>
                    </ui:fragment>

                    <ui:fragment>
                        <li class="">
                            <h:commandLink target="#{MenuControle.getConfig('AGENDA_NOVA_ABA').valorAsBoolean ? 'myAgenda' : '_self'}"  
                                           action="pretty:agenda">#{title['principal.agenda']}</h:commandLink>
                        </li>
                    </ui:fragment>

                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('NOTIFICACOES', 'CONSULTAR')}">
                        <li><h:link id="linkNotificacoes" outcome="notificacoes">
                                #{title['principal.notificacoes']}
                                <f:param name="op" value="listarTodas"/>
                            </h:link>
                        </li>
                    </ui:fragment>

                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('ACOMPANHAR', 'CONSULTAR')}">
                        <li class=""><h:commandLink action="pretty:acompanhar">Acompanhar</h:commandLink></li>
                    </ui:fragment>
                </ul>

                <h:panelGroup layout="block" class="pesq pesq-alunos-topo #{not empty ClientesControle.filtroPesquisa ? 'expandido' : ''} pull-right">
                    <label>
                        <p:inputText title="#{title['pesquisaGeralAluno']}" id="inputPesquisaGeral" pt:placeholder="#{title['principal.pesquisa']}" 
                                     value="#{ClientesControle.filtroPesquisa}"                                     
                                     onblur="if ($('div.pesq > input').text() === '') {
                    $('.expandido').removeClass('expandido');
                }"
                                     onclick="this.select()"
                                     onkeypress="if (event.keyCode === 13) {
                    document.getElementById('fmLay:btnConsultarGeral').click();
                    return false;
                }">

                        </p:inputText>
                        <p:hotkey global="false" bind="ctrl+shift+l" onsuccess="document.getElementById('fmLay:inputPesquisaGeral').focus();"/>
                        <p:commandLink id="btnConsultarGeral"
                                       style="display: none;"
                                       update="@(.pesq)"
                                       actionListener="#{ClientesControle.redirectConsulta}"/>
                    </label>
                </h:panelGroup>
            </div>
        </div>
    </h:panelGroup>
    <h:panelGroup layout="block" id="panelContainerTopo">
        <h:panelGroup layout="block" styleClass="navbar navbar-inverse navbar-fixed-top topoFullScreen" rendered="#{MenuControle.modoFullScreen}">
            <p:graphicImage cache="true" library="svg" name="tr.svg" />
            <div class="nav-collapse collapse">
                <ul class="nav pull-right mainMNU">
                    <li>
                        <p:commandLink id="lnkDisableModoFullScreen" actionListener="#{MenuControle.toggleFullScreen(!MenuControle.modoFullScreen)}" 
                                       title="#{msg['desabilitar']} #{msg['modoFullScreen']}"
                                       action="pretty:"
                                       style="padding: 0;" ajax="false">
                            <p:graphicImage cache="true" library="svg" name="fullscreen_back.svg" style="width: 40px; padding: 0; margin: 5px 0 0 0;"/>
                        </p:commandLink>
                    </li>
                    <li class="dropdown menuUsuarioPegar" style="margin: 0 15px 0 0;">
                        <a href="#" class="dropdown-toggle usrMenu"
                           data-toggle="dropdown">
                            <p:graphicImage cache="true" url="#{SuperControl.usuario.avatar}" style="width: 48px; height: 48px;display: inline-block;background-color: white;"/>
                            <!--b></b-->
                            <i class="fa-icon-caret-down"/>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="avatar">
                                <h:panelGroup layout="block" styleClass="avartarEditar">
                                    <p:graphicImage cache="true" url="#{SuperControl.usuario.avatar}"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="avatarNome">
                                    <h:outputText value="#{SuperControl.usuario.nomeApresentarLower}" />
                                </h:panelGroup>
                            </li>
                            <li class="hotLinkOpcoes">
                                <p:commandLink styleClass="itemOp itemUm">
                                    <i class="fa-icon-user"/>
                                    <h:outputText styleClass="etiqueta" value="perfil"/>
                                </p:commandLink>
                                <p:commandLink styleClass="itemOp itemDois">
                                    <i class="fa-icon-cogs"/>
                                    <h:outputText styleClass="etiqueta" value="Edtar Perfil"/>
                                </p:commandLink>
                                <p:commandLink styleClass="itemOp itemTres">
                                    <i class="fa-icon-key"/>
                                    <h:outputText styleClass="etiqueta" value="Alterar Senha"/>
                                </p:commandLink>

                            </li>
                            <li>
                                <p:accordionPanel activeIndex="null">
                                    <p:tab>
                                        <f:facet name="title">
                                            <h:outputText value="#{language.localeCode}"/>
                                        </f:facet>
                                        <p:dataTable value="#{language.linguas}" var="lingua">
                                            <p:column>
                                                <p:commandLink action="#{language.localeCode}" value="#{lingua}" />
                                            </p:column>
                                        </p:dataTable>
                                    </p:tab>
                                </p:accordionPanel>
                            </li>
                            <li>
                                <h:commandLink action="#{LoginControle.logout(SuperControl.usuario.chave)}" styleClass="linkLogout">
                                    <i class="fa-icon-remove"/> Sair
                                </h:commandLink>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </h:panelGroup>
    </h:panelGroup>
    <p:dialog rendered="#{SuperControl.isMobile()}" modal="true" header="Menu" id="modalMenuMobile"
              widgetVar="modalMenuMobile" styleClass="menuModal" height="320px" resizable="false">
        <p:commandLink action="pretty:home">
            Inicio
        </p:commandLink>
        <p:accordionPanel rendered="#{SuperControl.usuario.cadastroHabilitado}" activeIndex="null">
            <p:tab title="Cadastros">
                <ul class="subMobile">
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('MUSCULOS', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:musculo">#{title['cadastros.musculos']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('GRUPOS_MUSCULARES', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:gmusculo">#{title['cadastros.gruposmusculares']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('APARELHOS', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:aparelhos">#{title['cadastros.aparelhos']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('CATEGORIA_ATIVIDADE', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:catatividades">#{title['cadastros.categoriaAtividades']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('NIVEIS', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:nivel">#{title['cadastros.nivel']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('ATIVIDADES', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:atividades">#{title['cadastros.atividades']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('FICHAS_PRE_DEFINIDAS', 'CONSULTAR')}">
                        <li><h:commandLink action="#{SuperControl.redirect('/cadastros/fichas/predefinidas')}">#{title['cadastros.fichas.predefinidas']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('CATEGORIA_FICHAS', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:catfichas">#{title['cadastros.categoriaficha']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('OBJETIVOS', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:objetivos">#{title['cadastros.objetivos']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('IMAGENS', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:imagem">#{title['cadastros.imagens']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('BADGES', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:badge">#{title['badge']}</h:commandLink></li>
                    </ui:fragment>
                    <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('TIPO_EVENTO', 'CONSULTAR')}">
                        <li><h:commandLink action="pretty:tiposEvento">#{title['cadastros.tipoEvento']}</h:commandLink></li>
                    </ui:fragment>
                </ul>
            </p:tab>
        </p:accordionPanel>

        <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('ALUNOS', 'CONSULTAR')}">
            <h:commandLink action="#{SuperControl.redirect('/alunos/todosalunos')}">#{title['cadastros.todosalunos']}</h:commandLink>
        </ui:fragment>

        <ui:fragment >
            <h:commandLink action="pretty:agenda">#{title['principal.agenda']}</h:commandLink>
        </ui:fragment>

        <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('NOTIFICACOES', 'CONSULTAR')}">
            <h:link id="linkNotificacoesPop" outcome="notificacoes">
                #{title['principal.notificacoes']}
                <f:param name="op" value="listarTodas"/>
            </h:link>
        </ui:fragment>
        <ui:fragment rendered="#{SuperControl.usuario.isItemHabilitado('ACOMPANHAR', 'CONSULTAR')}">
            <h:commandLink action="pretty:acompanhar">Acompanhar</h:commandLink>
        </ui:fragment>
    </p:dialog>
</ui:composition>