/*
    Document   : oamd
    Created on : 01/10/2012, 22:49:54
    Author     : waller
    Description:
        Purpose of the stylesheet 
*/

/*
   TODO customize this sample style
   Syntax recommendation http://www.w3.org/TR/REC-CSS2/
*/

.rich-panel {
    overflow: hidden;
    width: 132px;
    display: block;
    float: left;
    text-align: center;
    margin: 2px 2px 2px 2px;

    border: 1px solid #e5e5e5;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .05);
    -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, .05);
    box-shadow: 0 1px 2px rgba(0, 0, 0, .05);

}

.rich-panel-body {
    padding: 0px 0px 0px 0px;
}

.rich-sb-int {
    font-size: 9px;
    font-family: 'Lucida Grande', Geneva, Verdana, Arial, sans-serif;
}

.nome-empresa {
    font-size: 7px;
    font-family: 'Lucida Grande', Geneva, Verdana, Arial, sans-serif;
}


.rich-panel-header {
    border: none;
    background: #feffff; /* Old browsers */

}

.table-invisivel {
    border: none;
    /*background-color: gray;filter:alpha(opacity=60); -moz-opacity:0.6; opacity: 0.6;border: 1px solid black;*/
    background-color: white;
    filter: alpha(opacity=90);
    -moz-opacity: 0.90;
    opacity: 0.90;
}

.rich-table-cell {
    border: none;
}


/*Transparent Tables*/
table, tr, td {
    background: transparent;
    border: 0px;
}

table table table {
    background: transparent;
}

table table table td {
    background-color: transparent;
}

.botaoApagarOamd{
    width: 30px;
    height: 100%;
}

.botaoApagarOamd:hover{
    text-decoration: none;
}

::-webkit-scrollbar {
    width: 8px;
    height: 6px;
    margin-right: 2px;
}

::-webkit-scrollbar-button:start:decrement, ::-webkit-scrollbar-button:end:increment {
    display: block;
    height: 0px;
}

::-webkit-scrollbar-button:vertical:end:increment {
    background: #FCF9F9;
    border-right: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-top: 1px solid #ccc;
}

::-webkit-scrollbar-button:vertical:increment {
    background-color: #FCF9F9;
    border-right: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    border-top: 1px solid #ccc;
}

::-webkit-scrollbar-track-piece {
    background: #FCF9F9;
    border-right: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-top: 1px solid #ccc;
}

::-webkit-scrollbar-thumb:vertical {
    background-color: #ccc;
}

::-webkit-scrollbar-thumb:vertical:hover {
    background-color: #666;
}

::-webkit-scrollbar-thumb:vertical:active {
    background-color: #333;
}

#table {
    width: 100%;
    height: 100%;
    display: table;
}

#cell {
    vertical-align: middle;
    display: table-cell;
    _position: absolute;
    _top: 0;
}

#conteudo {
    width: 90%;
    margin: 0 auto;
    _left: -50%;
    padding: 0 0 0 0;
    _position: relative;
    _top: 0;
    text-align: left;
}

#divCentralizada {
    position: absolute;
    text-align: center;
    width: 100%;
    height: 100%;
    left: 50%;
    top: 0;
    margin-left: -50%;
    margin-top: 0px;
    vertical-align: middle;
    overflow: auto;

}

img#bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

input-search {
    font-size: 18px;
    padding: 5px 5px 5px 5px;
    border: 1px solid #CCC;
    border-bottom-color: #999;
    border-right-color: #999;
    height: 40px;
    color: black;
    margin: 0px;
    width: 350px;
    vertical-align: middle;
    background: rgb(248, 255, 232); /* Old browsers */
    background: #ffffff; /* Old browsers */
    /* IE9 SVG, needs conditional override of 'filter' to 'none' */
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNkNmYwZmQiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background: -moz-linear-gradient(top, #ffffff 0%, #d6f0fd 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffffff), color-stop(100%, #d6f0fd)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #ffffff 0%, #d6f0fd 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #ffffff 0%, #d6f0fd 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #ffffff 0%, #d6f0fd 100%); /* IE10+ */
    background: linear-gradient(top, #ffffff 0%, #d6f0fd 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#d6f0fd', GradientType=0); /* IE6-8 */

    -moz-box-shadow: 1px 1px 1px 1px #ccc;
    -webkit-box-shadow: 1px 1px 1px 1px #ccc;
}

.divForm {
    border: solid 1px #DDDDDD;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    padding: 10px 10px 10px 10px;
    background-color: #FFFFFF;
}

.divEmpresaSinc {
    border: solid 1px #DDDDDD;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    padding: 10px 10px 10px 10px;
    background-color: #EEEEEE;
}

.textoDireita {
    text-align: right;
}

.textoEsquerda {
    text-align: left;
}

.marginLeft10 {
    margin-left: 10px;
}

.marginRight10 {
    margin-right: 10px;
}

.tituloLogin {
    padding: 30px 0px 15px 0px;
}

.divExterna {
    padding: 0px;
    text-align: center;
}

.inputFormRight {
    text-align: right;
}

.inputFormLeft {
    text-align: left;
}

.cantoRedondoInput {
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
}

.marginTop5 {
    margin-top: 5px;
}

.marginTop10 {
    margin-top: 10px;
}

.marginTop15 {
    margin-top: 15px;
}

.marginTop20 {
    margin-top: 20px;
}

.marginTop30 {
    margin-top: 30px;
}

.marginTop50 {
    margin-top: 50px;
}

.marginTop100 {
    margin-top: 100px;
}

.font7 {
    font-size: 7pt;
}

.font8 {
    font-size: 8pt;
}

.font9 {
    font-size: 9pt;
}

.font10 {
    font-size: 10pt;
}

.font11 {
    font-size: 11pt;
}

.font12 {
    font-size: 12pt;
}
.floatLeft{
    float: left;
}
.floatRight{
    float: right;
}

