<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">

<f:metadata>
    <f:event type="preRenderView" listener="#{SuperControl.validaAdmin}"/>
</f:metadata>

<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <link type="text/css" href="#{request.contextPath}/resources/js/amcharts/plugins/export/export.css"
              rel="stylesheet"/>

        <h:panelGroup styleClass="span12 offset3 caixaPrincipal" id="enotasPanel"
                      style="padding: 20px; width: 100%; margin-left: 1%;">
            <h4 style="margin-left: auto">PG4DEV</h4>

            <p:panel id="pnlContainer">

                <p:commandLink styleClass="btn btn-primary"
                               action="#{Pg4devControle.prepararContainer()}"
                               update="@form" ajax="true"
                               rendered="#{Pg4devControle.situacaoContainer == 'NENHUM'}">
                    <i class="fa-icon-check"/> Preparar container...
                </p:commandLink>

                <h:panelGroup rendered="#{Pg4devControle.situacaoContainer == 'PREPARANDO_PG'}">
                    <p:poll listener="#{Pg4devControle.atualizarSituacao}"
                            update=":fmLay:pnlContainer"
                            interval="5"/>
                </h:panelGroup>

                <h:outputText rendered="#{Pg4devControle.situacaoContainer == 'PREPARANDO_PG'}"
                              value="#{Pg4devControle.mensagemSituacaoContainer}"/>


                <h:outputLink value="#{Pg4devControle.linkContainer}" styleClass="btn btn-primary"
                              rendered="#{Pg4devControle.situacaoContainer == 'PREPARADO_USO'}"
                              target="_blank">
                    <i class="fa-icon-check"/> Acessar container
                </h:outputLink>

            </p:panel>

        </h:panelGroup>
    </ui:define>
</ui:decorate>
</html>
