<ui:composition
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:h="http://java.sun.com/jsf/html"
        xmlns:ui="http://java.sun.com/jsf/facelets"
        xmlns:f="http://java.sun.com/jsf/core"
        xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
        xmlns:fn="http://java.sun.com/jsp/jstl/functions"
        xmlns:p="http://primefaces.org/ui"
        xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

    <style>
        .w40 {
            width: 40%;
            text-align: left;
            vertical-align: top;
        }

        .w60 {
            width: 60%;
            text-align: left;
            vertical-align: top;
        }
    </style>

    <p:dialog widgetVar="modalDialogCobrancaPacto" modal="true" id="modalCobrancaPacto" showEffect="fade"
              hideEffect="fade"
              resizable="true" width="400">
        <f:facet name="header">
            <h:outputText
                    rendered="#{OamdControle.empresaUnidSelecionada.apresentarAdicionarCredito or OamdControle.empresaUnidSelecionada.tipoCobrancaRedeEmpresa}"
                    value="Adicionar Crédito Manual"/>
            <h:outputText
                    rendered="#{!OamdControle.empresaUnidSelecionada.apresentarAdicionarCredito and !OamdControle.empresaUnidSelecionada.tipoCobrancaRedeEmpresa}"
                    value="Gerar Cobrança Manual"/>
        </f:facet>

        <p:outputLabel value="#{OamdControle.empresaUnidSelecionada.nome}"
                       style="font-weight: bold; text-align: center;"/>

        <h:panelGrid columns="2" id="panelGridCobranca">

            <p:outputLabel value="#{OamdControle.empresaUnidSelecionada.labelData}" style="font-weight: bold"/>
            <h:outputText value="#{OamdControle.empresaUnidSelecionada.dtultimacobrancadcc_Apresentar}"/>

            <p:outputLabel value="#{OamdControle.empresaUnidSelecionada.labelCredito}" style="font-weight: bold"/>
            <p:inputText
                    rendered="#{OamdControle.empresaUnidSelecionada.apresentarAdicionarCredito or OamdControle.empresaUnidSelecionada.tipoCobrancaRedeEmpresa}"
                    value="#{OamdControle.qtdCreditoAdicionar}"
                    onkeypress="mascara(this, somenteNumeros)">
                <p:ajax event="change" update=":fmLay:valorTotal :fmLay:valorParcela"/>
            </p:inputText>
            <h:outputText
                    rendered="#{!OamdControle.empresaUnidSelecionada.apresentarAdicionarCredito and !OamdControle.empresaUnidSelecionada.tipoCobrancaRedeEmpresa}"
                    value="#{OamdControle.empresaUnidSelecionada.qtdCreditoUtilizadoPosPagoPacto}"/>

            <p:outputLabel value="Gerar Cobrança no Financeiro:" style="font-weight: bold"/>
            <p:selectBooleanCheckbox value="#{OamdControle.gerarCobrancaPacto}">
                <p:ajax event="change" update=":fmLay:panelGridCobranca :fmLay:panelMsgPrimeiraParcela"/>
            </p:selectBooleanCheckbox>

            <p:outputLabel rendered="#{!OamdControle.gerarCobrancaPacto}"
                           value="Justificativa:" style="font-weight: bold"/>
            <p:inputText rendered="#{!OamdControle.gerarCobrancaPacto}"
                         maxlength="250"
                         value="#{OamdControle.justificativa}"/>

            <p:outputLabel rendered="#{OamdControle.gerarCobrancaPacto}"
                           value="Valor Unitário:" style="font-weight: bold"/>
            <p:inputText rendered="#{OamdControle.gerarCobrancaPacto}"
                         onkeypress="mascara(this, moeda)"
                         value="#{OamdControle.empresaUnidSelecionada.valorcreditopacto}">
                <p:ajax event="change" update=":fmLay:valorTotal :fmLay:valorParcela"/>
            </p:inputText>

            <p:outputLabel rendered="#{OamdControle.gerarCobrancaPacto}"
                           value="Valor Total:" style="font-weight: bold"/>
            <h:outputText rendered="#{OamdControle.gerarCobrancaPacto}"
                          id="valorTotal" value="#{OamdControle.valorTotalApresentar}"/>

            <p:outputLabel rendered="#{OamdControle.gerarCobrancaPacto}"
                           value="Qtd Parcelas:" style="font-weight: bold"/>
            <p:inputText rendered="#{OamdControle.gerarCobrancaPacto}"
                         onkeypress="mascara(this, somenteNumeros)"
                         value="#{OamdControle.empresaUnidSelecionada.qtdparcelascobrancapacto}">
                <p:ajax event="change" update=":fmLay:valorTotal :fmLay:valorParcela"/>
            </p:inputText>

            <p:outputLabel rendered="#{OamdControle.gerarCobrancaPacto}"
                           value="Valor Cada Parcela:" style="font-weight: bold"/>
            <h:outputText rendered="#{OamdControle.gerarCobrancaPacto}"
                          id="valorParcela" value="#{OamdControle.valorParcelaApresentar}"/>

            <p:outputLabel rendered="#{OamdControle.gerarCobrancaPacto}"
                           value="* Data 1ª Parcela:" style="font-weight: bold"/>
            <p:calendar rendered="#{OamdControle.gerarCobrancaPacto}"
                        value="#{OamdControle.dataPrimeiraParcela}"
                        pattern="dd/MM/yyyy"
                        disabled="true"
                        size="10"
                        showButtonPanel="false"/>

            <p:outputLabel rendered="#{OamdControle.gerarCobrancaPacto}"
                           value="Gerar Nota Fiscal:" style="font-weight: bold"/>
            <p:selectBooleanCheckbox rendered="#{OamdControle.gerarCobrancaPacto}"
                                     value="#{OamdControle.empresaUnidSelecionada.gerarnotafiscalcobrancapacto}"/>
        </h:panelGrid>

        <h:panelGroup layout="block" id="panelMsgPrimeiraParcela">
            <p:outputLabel id="msgPrimeiraParcela"
                           rendered="#{OamdControle.gerarCobrancaPacto}"
                           value="* O vencimento da 1ª parcela será sempre 16 dias após a data de lançamento (Data Atual)."
                           style="font-size: 13px; color: red;"/>
        </h:panelGroup>

        <h:panelGrid width="100%" style="text-align: center">
            <p:commandLink
                    rendered="#{OamdControle.empresaUnidSelecionada.apresentarAdicionarCredito and not OamdControle.configurandoRede}"
                    styleClass="btn btn-primary" value="Gravar"
                    update=":fmLay:tabFuncoes:dcc"
                    action="#{OamdControle.gravarPrePagoDCCLancarAgendamentoFinan}"/>
            <p:commandLink
                    rendered="#{!OamdControle.empresaUnidSelecionada.apresentarAdicionarCredito and not OamdControle.configurandoRede}"
                    styleClass="btn btn-primary" value="Gerar Cobrança"
                    update=":fmLay:tabFuncoes:dcc"
                    action="#{OamdControle.gravarPosPagoDCCLancarAgendamentoFinan}"/>

            <p:commandLink rendered="#{OamdControle.configurandoRede}"
                           styleClass="btn btn-primary" value="Adicionar"
                           update=":fmLay:tabFuncoes:dcc"
                           action="#{OamdControle.gravarDCCRede(true)}"/>
        </h:panelGrid>
        <p:messages id="messagesModalCobrancaPacto" showSummary="true" showDetail="false" autoUpdate="true"
                    closable="true"/>
    </p:dialog>

    <p:dialog widgetVar="modalDialogCobrancaConfig" modal="true" id="modalCobrancaConfig" showEffect="fade"
              hideEffect="fade" resizable="true" width="500">
        <f:facet name="header">
            <h:outputText value="Configurações de Cobrança"/>
        </f:facet>

        <p:outputLabel value="#{OamdControle.empresaUnidSelecionadaConfigurar.nome}"
                       style="font-weight: bold; text-align: center;"/>

        <h:panelGroup layout="block" id="config" style="padding-bottom: 10px">
            <h:panelGrid columns="2">
                <p:outputLabel value="#{OamdControle.empresaUnidSelecionadaConfigurar.labelData}"
                               style="font-weight: bold"/>
                <h:outputText value="#{OamdControle.empresaUnidSelecionadaConfigurar.dtultimacobrancadcc_Apresentar}"/>

                <p:outputLabel value="Tipo de Cobrança:" style="font-weight: bold"/>
                <h:selectOneMenu value="#{OamdControle.empresaUnidSelecionadaConfigurar.tipocobrancapacto}"
                                 disabled="#{OamdControle.configurandoRede}">
                    <f:selectItems value="#{OamdControle.selectItemTipoCobrancaPacto}"/>
                    <p:ajax event="change" update=":fmLay:config"
                            listener="#{OamdControle.selecionouTipoCobrancaPacto}"/>
                </h:selectOneMenu>

                <p:outputLabel value="Cobrar crédito de Boletos:" style="font-weight: bold"/>
                <p:selectBooleanCheckbox value="#{OamdControle.empresaUnidSelecionadaConfigurar.cobrarcreditopactoboleto}"/>

                <p:outputLabel value="Tabela Recorrência:"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal}"
                               style="font-weight: bold"/>
                <h:selectOneMenu rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal}"
                                 value="#{OamdControle.creditoPactoSelecionado}">
                    <f:selectItems value="#{OamdControle.selectItemTabelaCreditoPacto}"/>
                    <p:ajax event="change" update=":fmLay:config"/>
                </h:selectOneMenu>

                <p:outputLabel rendered="#{!OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal}"
                               value="Transação Recorrente:" style="font-weight: bold"/>
                <p:selectBooleanCheckbox
                        rendered="#{!OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal}"
                        value="#{OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto}">
                    <p:ajax event="change" update=":fmLay:config"/>
                </p:selectBooleanCheckbox>

                <p:outputLabel value="Empresa Responsável pela cobrança:"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaRedeEmpresa}"
                               style="font-weight: bold"/>
                <h:selectOneMenu value="#{OamdControle.codEmpresaFinanceiroCobranca}"
                                 style="width: 100%"
                                 rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaRedeEmpresa}">
                    <f:selectItems value="#{OamdControle.selectItemEmpresaFinanceiroCobranca}"/>
                    <p:ajax event="change" update=":fmLay:config"/>
                </h:selectOneMenu>

                <p:outputLabel value="Qtd Máxima Crédito:"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"
                               style="font-weight: bold"/>
                <p:outputLabel value="#{OamdControle.creditoPactoSelecionadoObj.qtdMaxima}"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"/>

                <p:outputLabel value="Valor Mensal:"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"
                               style="font-weight: bold"/>
                <p:outputLabel value="#{OamdControle.creditoPactoSelecionadoObj.valorMensalApresentar}"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"/>

                <p:outputLabel value="Valor Unitário:"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"
                               style="font-weight: bold"/>
                <p:outputLabel value="#{OamdControle.creditoPactoSelecionadoObj.valorUnitarioMensalApresentar}"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"/>

                <p:outputLabel value="Valor Unitário Excedente:"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"
                               style="font-weight: bold"/>
                <p:outputLabel value="#{OamdControle.creditoPactoSelecionadoObj.valorUnitarioExcedenteApresentar}"
                               rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"/>

                <p:outputLabel
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto and !OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal}"
                        value="Valor Unitário:" style="font-weight: bold"/>
                <p:inputText
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto and !OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal}"
                        value="#{OamdControle.empresaUnidSelecionadaConfigurar.valorcreditopacto}"
                        onkeypress="mascara(this, moeda)"/>

                <p:outputLabel
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.apresentarDiasFechar and !OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto}"
                        value="Qtd Dias Fechar:" style="font-weight: bold"/>
                <p:outputLabel
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"
                        value="Dia Fechamento:" style="font-weight: bold"/>
                <p:inputText
                        rendered="#{(OamdControle.empresaUnidSelecionadaConfigurar.apresentarDiasFechar and !OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto) or (OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0)}"
                        value="#{OamdControle.empresaUnidSelecionadaConfigurar.qtddiasfechamentocobrancapacto}"
                        onkeypress="mascara(this, somenteNumeros)">
                </p:inputText>

                <p:outputLabel
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"
                        value="Dia Vencimento Cobrança:" style="font-weight: bold"/>
                <p:inputText
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0}"
                        value="#{OamdControle.empresaUnidSelecionadaConfigurar.diavencimentocobrancapacto}"
                        onkeypress="mascara(this, somenteNumeros)">
                </p:inputText>

                <p:outputLabel
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.apresentarAdicionarCredito and OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto}"
                        value="Qtd Crédito Renovar:" style="font-weight: bold"/>
                <p:inputText
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.apresentarAdicionarCredito and OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto}"
                        value="#{OamdControle.empresaUnidSelecionadaConfigurar.qtdcreditorenovarprepagocobrancapacto}"
                        onkeypress="mascara(this, somenteNumeros)"/>

                <p:outputLabel
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto and !OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal}"
                        value="Qtd Parcelas:" style="font-weight: bold"/>
                <p:inputText
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto and !OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal}"
                        value="#{OamdControle.empresaUnidSelecionadaConfigurar.qtdparcelascobrancapacto}"
                        onkeypress="mascara(this, somenteNumeros)"/>

                <p:outputLabel rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto}"
                               value="Gerar Nota Fiscal:" style="font-weight: bold"/>
                <p:selectBooleanCheckbox
                        rendered="#{OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto}"
                        value="#{OamdControle.empresaUnidSelecionadaConfigurar.gerarnotafiscalcobrancapacto}"/>

            </h:panelGrid>

            <p:outputLabel
                    rendered="#{(not empty OamdControle.empresaUnidSelecionadaConfigurar.msgAtencao and !OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.empresaUnidSelecionadaConfigurar.gerarcobrancaautomaticapacto) or (OamdControle.empresaUnidSelecionadaConfigurar.tipoCobrancaPosPagoMensal and OamdControle.creditoPactoSelecionado > 0)}"
                    value="#{OamdControle.empresaUnidSelecionadaConfigurar.msgAtencao}"
                    style="font-size: 13px; color: red;"/>
        </h:panelGroup>


        <h:panelGrid width="100%" style="text-align: center">
            <p:commandLink value="Gravar" rendered="#{not OamdControle.configurandoRede}"
                           styleClass="btn btn-primary"
                           action="#{OamdControle.alterarInformacoesEmpresaCobrancaPacto(OamdControle.empresaUnidSelecionadaConfigurar)}"
                           update=":fmLay:tabFuncoes:dcc"/>

            <p:commandLink rendered="#{OamdControle.configurandoRede}"
                           styleClass="btn btn-primary" value="Gravar"
                           update=":fmLay:tabFuncoes:dcc"
                           action="#{OamdControle.gravarDCCRede(false)}"/>
        </h:panelGrid>


        <p:messages id="messagesModalCobrancaConfig" showSummary="true" showDetail="false" autoUpdate="true"
                    closable="true"/>
    </p:dialog>

    <p:dialog widgetVar="modalDialogCobrancaMensalRede" modal="true" id="modalCobrancaMensalRede" showEffect="fade"
              hideEffect="fade" resizable="true" width="500">
        <f:facet name="header">
            <h:outputText value="Pós-Pago Efetivado - Mensal"/>
        </f:facet>

        <p:outputLabel value="#{CreditoPactoConfigControle.redeEmpresa.nome}"
                       style="font-weight: bold; text-align: center;"/>

        <h:panelGroup layout="block" id="configCobRede" style="padding-bottom: 10px">
            <h:panelGrid columns="2" columnClasses="w40,w60">
                <p:outputLabel value="Dt Última Cobrança:"
                               style="font-weight: bold"/>
                <h:outputText value="#{CreditoPactoConfigControle.redeEmpresa.dataUltimaCobrancaPactoApresentar}"/>

                <p:outputLabel rendered="#{CreditoPactoConfigControle.redeEmpresa != null}" value="Cobrar crédito de Boletos:" style="font-weight: bold"/>
                <p:selectBooleanCheckbox rendered="#{CreditoPactoConfigControle.redeEmpresa != null}" value="#{CreditoPactoConfigControle.redeEmpresa.cobrarCreditoPactoBoleto}"/>

                <p:outputLabel value="Tabela Recorrência:"
                               style="font-weight: bold"/>
                <h:selectOneMenu value="#{CreditoPactoConfigControle.creditoPactoSelecionado}"
                                 style="width: 100%">
                    <f:selectItems value="#{CreditoPactoConfigControle.selectItemTabelaCreditoPacto}"/>
                    <p:ajax event="change" update=":fmLay:configCobRede"/>
                </h:selectOneMenu>

                <p:outputLabel value="Empresa Responsável pela cobrança:"
                               rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                               style="font-weight: bold"/>
                <h:selectOneMenu value="#{CreditoPactoConfigControle.empresaFinanceiroCobranca}"
                                 style="width: 100%"
                                 rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}">
                    <f:selectItems value="#{CreditoPactoConfigControle.selectItemEmpresaFinanceiroCobranca}"/>
                    <p:ajax event="change" update=":fmLay:configCobRede"/>
                </h:selectOneMenu>

                <p:outputLabel value="Qtd Máxima Crédito:"
                               rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                               style="font-weight: bold"/>
                <p:outputLabel value="#{CreditoPactoConfigControle.creditoPactoSelecionadoObj.qtdMaxima}"
                               rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"/>

                <p:outputLabel value="Valor Mensal:"
                               rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                               style="font-weight: bold"/>
                <p:outputLabel value="#{CreditoPactoConfigControle.creditoPactoSelecionadoObj.valorMensalApresentar}"
                               rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"/>

                <p:outputLabel value="Valor Unitário:"
                               rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                               style="font-weight: bold"/>
                <p:outputLabel
                        value="#{CreditoPactoConfigControle.creditoPactoSelecionadoObj.valorUnitarioMensalApresentar}"
                        rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"/>

                <p:outputLabel value="Valor Unitário Excedente:"
                               rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                               style="font-weight: bold"/>
                <p:outputLabel
                        value="#{CreditoPactoConfigControle.creditoPactoSelecionadoObj.valorUnitarioExcedenteApresentar}"
                        rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"/>

                <p:outputLabel rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                               value="Dia Fechamento:" style="font-weight: bold"/>
                <p:inputText rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                             value="#{CreditoPactoConfigControle.redeEmpresa.qtdDiasFechamentoCobrancaPacto}"
                             onkeypress="mascara(this, somenteNumeros)">
                    <p:ajax event="change" update=":fmLay:configCobRede"/>
                </p:inputText>

                <p:outputLabel rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                               value="Dia Vencimento Cobrança:" style="font-weight: bold"/>
                <p:inputText rendered="#{CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                             value="#{CreditoPactoConfigControle.redeEmpresa.diaVencimentoCobrancaPacto}"
                             onkeypress="mascara(this, somenteNumeros)">
                    <p:ajax event="change" update=":fmLay:configCobRede"/>
                </p:inputText>

            </h:panelGrid>

            <p:outputLabel
                    rendered="#{not empty CreditoPactoConfigControle.msgAtencaoConfigRedeMensal and CreditoPactoConfigControle.creditoPactoSelecionado > 0}"
                    value="#{CreditoPactoConfigControle.msgAtencaoConfigRedeMensal}"
                    style="font-size: 13px; color: red;"/>
        </h:panelGroup>

        <h:panelGrid width="100%" style="text-align: center">
            <p:commandLink styleClass="btn btn-primary" value="Gravar"
                           action="#{CreditoPactoConfigControle.gravarRedeMensal()}"/>
        </h:panelGrid>

        <p:messages id="messagesModalCobrancaMensalRede" showSummary="true" showDetail="false" autoUpdate="true"
                    closable="true"/>
    </p:dialog>

    <p:dialog widgetVar="modalDataPosPago" modal="true" id="modalDataPosPago" showEffect="fade"
              hideEffect="fade"
              resizable="true" width="400">
        <f:facet name="header">
            <h:outputText value="Ajustar início Pós-Pago"/>
        </f:facet>

        <p:outputLabel value="#{OamdControle.empresaUnidSelecionada.nome}"
                       style="font-weight: bold; text-align: center;"/>

        <h:panelGrid columns="2" id="panelDataPosPago">

            <p:outputLabel value="Data:" style="font-weight: bold"/>
            <p:calendar id="dataAlteracaoPosPago" value="#{OamdControle.dataAlteracaoPosPago}"
                        pattern="dd/MM/yyyy HH:mm:ss"/>

        </h:panelGrid>

        <h:panelGroup layout="block">
            <p:outputLabel id="msgDataPosPago"
                           value="* Sistema irá marcar como já contabilizado todas os itens de remessa e transações anteriores a data e hora informada."
                           style="font-size: 13px; color: red;"/>
            <p:outputLabel id="msgDataPosPago2"
                           value="* A data da última cobrança Pacto será a data informada."
                           style="font-size: 13px; color: red;"/>
        </h:panelGroup>

        <h:panelGrid width="100%" style="text-align: center">
            <p:commandLink styleClass="btn btn-primary" value="Ajustar"
                           update="fmLay:messagesModalDataPosPago"
                           action="#{OamdControle.ajustarEmpresaParaIniciarPosPago}"/>
        </h:panelGrid>
        <p:messages id="messagesModalDataPosPago" showSummary="true" showDetail="false" autoUpdate="true"
                    closable="true"/>
    </p:dialog>

    <p:dialog widgetVar="modalDialogCreditoPactoBonus" modal="true" id="modalCreditoPactoBonus" showEffect="fade"
              hideEffect="fade" resizable="true" width="400">

        <f:facet name="header">
            <h:outputText value="Alterar bônus"/>
        </f:facet>

        <p:outputLabel value="#{OamdControle.empresaUnidSelecionada.nome}"
                       style="font-weight: bold; text-align: center;"/>

        <h:panelGrid columns="2" id="panelGridBonus">

            <p:outputLabel value="Qtd atual de bônus:" style="font-weight: bold"/>
            <p:outputLabel value="#{OamdControle.qtdBonusAtual}"/>

            <p:outputLabel value="Qtd Crédito (Bônus):" style="font-weight: bold"/>
            <p:inputText value="#{OamdControle.qtdBonusCredito}" maxlength="8"
                         onkeyup="$(this).val($(this).val().replace(/[^0-9]/g, ''));"/>

            <p:outputLabel value="Justificativa:" style="font-weight: bold"/>
            <p:inputText maxlength="250"
                         value="#{OamdControle.justificativaBonusCredito}"/>
        </h:panelGrid>

        <h:panelGrid width="100%" style="text-align: center">
            <p:commandLink styleClass="btn btn-primary" value="Gravar"
                           update=":fmLay:tabFuncoes:dcc"
                           action="#{OamdControle.gravarAdicionarBonusCreditoPacto}"/>
        </h:panelGrid>
        <p:messages id="messagesModalCreditoPactoBonus" showSummary="true" showDetail="false" autoUpdate="true"
                    closable="true"/>
    </p:dialog>

</ui:composition>
