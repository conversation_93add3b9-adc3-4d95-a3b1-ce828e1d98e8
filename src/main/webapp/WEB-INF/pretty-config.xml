<?xml version='1.0' encoding='UTF-8'?>

<pretty-config xmlns="http://ocpsoft.org/schema/rewrite-config-prettyfaces"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:schemaLocation="http://ocpsoft.org/schema/rewrite-config-prettyfaces
                      http://ocpsoft.org/xml/ns/prettyfaces/rewrite-config-prettyfaces.xsd">

    <url-mapping id="login">
        <pattern value="/login" />
        <view-id value="/login.xhtml" />
    </url-mapping>

    <url-mapping id="empresas">
        <pattern value="/empresas" />
        <view-id value="/empresas.xhtml" />
    </url-mapping>

    <url-mapping id="aplicativos">
        <pattern value="/aplicativos" />
        <view-id value="/aplicativos.xhtml" />
    </url-mapping>

    <url-mapping id="aplicativoEmpresa">
        <pattern value="/aplicativoEmpresa" />
        <view-id value="/aplicativoEmpresa.xhtml" />
    </url-mapping>

    <url-mapping id="feeds">
        <pattern value="/feeds" />
        <view-id value="/feedsGestao.xhtml" />
    </url-mapping>

    <url-mapping id="cadempresa">
        <pattern value="/cadastro" />
        <view-id value="/cadempresa.xhtml" />
    </url-mapping>

    <url-mapping id="trocar-senha">
        <pattern value="/trocar-senha" />
        <view-id value="/trocarSenha.xhtml" />
    </url-mapping>


    <url-mapping id="infras">
        <pattern value="/infras" />
        <view-id value="/estatisticas.xhtml" />
    </url-mapping>

    <url-mapping id="cadredeempresa">
        <pattern value="/redeEmpresa" />
        <view-id value="/redeEmpresa.xhtml" />
    </url-mapping>

    <url-mapping id="cadpipz">
        <pattern value="/pipz" />
        <view-id value="/cadpipz.xhtml" />
    </url-mapping>

    <url-mapping id="usuarios">
        <pattern value="/usuarios" />
        <view-id value="/usuarios.xhtml" />
    </url-mapping>

    <url-mapping id="produtoPacto">
        <pattern value="/produtoPacto" />
        <view-id value="/produtoPacto.xhtml" />
    </url-mapping>

    <url-mapping id="infoMigracao">
        <pattern value="/infoMigracao" />
        <view-id value="/infoMigracao.xhtml" />
    </url-mapping>

    <url-mapping id="migracaoRecurso">
        <pattern value="/migracaoRecurso" />
        <view-id value="/migracaoRecurso.xhtml" />
    </url-mapping>

    <url-mapping id="migracaoRecursoFeedback">
        <pattern value="/migracaoRecursoFeedback" />
        <view-id value="/migracaoRecursoFeedback.xhtml" />
    </url-mapping>

    <url-mapping id="produtoPactoView">
        <pattern value="/produtoPactoView/#{id}" />
        <view-id value="/produtoPactoView.xhtml" />
    </url-mapping>

    <url-mapping id="configAssinatura">
        <pattern value="/assinaturas" />
        <view-id value="/assinaturas.xhtml" />
    </url-mapping>

    <url-mapping id="termos">
        <pattern value="/termos/#{id}" />
        <view-id value="/produtoPactoTermos.xhtml" />
    </url-mapping>

    <url-mapping id="produtoPactoLog">
        <pattern value="/produtoPactoLog" />
        <view-id value="/produtoPactoLog.xhtml" />
    </url-mapping>

    <url-mapping id="produtoPactoCompra">
        <pattern value="/produtoPactoCompra" />
        <view-id value="/produtoPactoCompra.xhtml" />
    </url-mapping>

    <url-mapping id="produtoPactoCupomDesconto">
        <pattern value="/produtoPactoCupomDesconto" />
        <view-id value="/produtoPactoCupomDesconto.xhtml" />
    </url-mapping>

    <url-mapping id="creditoPacto">
        <pattern value="/creditoPacto" />
        <view-id value="/creditoPacto.xhtml" />
    </url-mapping>

    <url-mapping id="notaFiscal">
        <pattern value="/notaFiscal" />
        <view-id value="/notaFiscal.xhtml" />
    </url-mapping>

    <url-mapping id="sessoesAtivas">
        <pattern value="/sessoesAtivas" />
        <view-id value="/sessoesAtivas.xhtml" />
    </url-mapping>

    <url-mapping id="cadastroSite">
        <pattern value="/cadastroSite" />
        <view-id value="/cadastroSite.xhtml" />
    </url-mapping>

    <url-mapping id="projetos">
        <pattern value="/projetos" />
        <view-id value="/linhatempoprojetos.xhtml" />
    </url-mapping>

    <url-mapping id="facilite">
        <pattern value="/facilite" />
        <view-id value="/facilite.xhtml" />
    </url-mapping>

    <url-mapping id="projetosparam">
        <pattern value="/projetos/#{par}" />
        <view-id value="/linhatempoprojetos.xhtml" />
    </url-mapping>

    <url-mapping id="dashboardintegracaoprotheus">
        <pattern value="/intprotheus/dash" />
        <view-id value="/intprotheus/index.html" />
    </url-mapping>

    <url-mapping id="monitorServicos">
        <pattern value="/monitorServicos" />
        <view-id value="/testeProducao.xhtml" />
    </url-mapping>

    <url-mapping id="migracaoDados">
        <pattern value="/migracao/#{chave}/#{codigoEmpresa}" />
        <view-id value="/migracaoDados.xhtml" />
    </url-mapping>

    <url-mapping id="planosSucesso">
        <pattern value="/plano-sucesso" />
        <view-id value="/planoSucesso/consultaPlano.xhtml" />
    </url-mapping>

    <url-mapping id="biPlanoSucesso">
        <pattern value="/bi-plano-sucesso" />
        <view-id value="/planoSucesso/biPlanoSucesso.xhtml" />
    </url-mapping>

    <url-mapping id="integrantesCS">
        <pattern value="/cs/integrantes"/>
        <view-id value="/planoSucesso/integrantes.xhtml"/>
    </url-mapping>

    <url-mapping id="formIntegrantesCS">
        <pattern value="/novoIntegrante"/>
        <view-id value="/planoSucesso/formIntegrante.xhtml"/>
    </url-mapping>

    <url-mapping id="formEditarIntegrantesCS">
        <pattern value="/cs/integrante/#{id}"/>
        <view-id value="/planoSucesso/formIntegrante.xhtml"/>
    </url-mapping>

    <url-mapping id="enotas">
        <pattern value="/enotas" />
        <view-id value="/enotas.xhtml" />
    </url-mapping>

    <url-mapping id="notificacoesMoviDesk">
        <pattern value="/notificacoesMoviDesk" />
        <view-id value="/notificacoesMoviDesk.xhtml" />
    </url-mapping>

    <url-mapping id="cadastroPlanoSucesso">
        <pattern value="/plano-sucesso/cadastro" />
        <view-id value="/planoSucesso/cadastroPlano.xhtml" />
    </url-mapping>

    <url-mapping id="gitlab">
        <pattern value="/gitlab" />
        <view-id value="/gitlab.xhtml" />
    </url-mapping>

    <url-mapping id="pg4dev">
        <pattern value="/pg4dev" />
        <view-id value="/pg4dev.xhtml" />
    </url-mapping>
</pretty-config>
