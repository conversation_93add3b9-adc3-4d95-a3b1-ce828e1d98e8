
function fireElement(id) {    
    var target = document.getElementById(id);   
    if (document.dispatchEvent) { // W3C      
        var oEvent = document.createEvent( "MouseEvents" );
        oEvent.initMouseEvent("click", true, true,window, 1, 1, 1, 1, 1, false, false, false, false, 0, target);
        target.dispatchEvent( oEvent );
    } else {
        if(document.fireEvent) { // IE               
            target.fireEvent("onclick");          
        }
    }
}

function addFav(url, title){    
    if (window.sidebar)
        window.sidebar.addPanel(title, url,"");
    else if(window.opera && window.print){
        var mbm = document.createElement('a');
        mbm.setAttribute('rel','sidebar');
        mbm.setAttribute('href',url);
        mbm.setAttribute('title',title);
        mbm.click();
    }
    else if(document.all){
        window.external.AddFavorite(url, title);
    }    
}

function bookmarksite(url, nome)
{

    if (window.sidebar) { // Mozilla Firefox
        window.sidebar.addPanel(nome, url, "");
    }
    else if (window.external) { // IE
        window.external.AddFavorite(url, nome);
    }
    else if (window.opera && window.print) {
        window.external.AddFavorite(url, nome);
    }
    else {
        alert('not supported');
    }
}

