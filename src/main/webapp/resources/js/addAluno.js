/**
 * Created with IntelliJ IDEA.
 * User: joaobeno
 * Date: 16/01/14
 * Time: 09:37
 * To change this template use File | Settings | File Templates.
 */
var passoAtual = 1;
var firstClick = false;
var isCarregando = false;
jQuery(document).ready(function() {
    // Pesquisar com o [ENTER]
    jQuery('form').on("keypress", "input", function(e) {
        if (jQuery(this).hasClass("inputBuscar")) {
            if (e.which == 13) {
                buscaAlunos();
                return false;
            }
        }
        if (jQuery(this).hasClass("inputSalvar")) {
            if (e.which == 13) {
                rodaGravar();
                return false;
            }
        }
    });
    jQuery("input#matricula").prop('type', 'number');

    $("div.ui-selectonemenu.full-width").each(
            function()
            {
                $(this).css("width", $(this).parent().width());
            }
    );

});

function passoDireto() {
    jQuery(".passoGrande:not(.QRContem)").removeClass("passoB passoGrande").addClass("passoC passoPequeno");
    jQuery(".passo2").removeClass("passoA passoListaClientes").addClass("passoC");
    jQuery(".passoC").addClass("stack2");
    jQuery(".passo3").removeClass("passoA passoPequeno").addClass("passoB passoGrande");
    jQuery(".passo3 .fotoAlunoTopoM").animate({opacity: 1}, 500);
    passoAtual = 3;
    carregandoBtn();
}

function passoSeguinte() {
    jQuery(".passoGrande:not(.QRContem)").removeClass("passoB passoGrande passoListaClientes").addClass("passoC passoPequeno");
    switch (passoAtual) {
        case 1 :
            {
                jQuery(".passo2").removeClass("passoA passoPequeno").addClass("passoB passoGrande passoListaClientes");
                passoAtual = 2;
                window.setTimeout(function() {
                    jQuery("#listaClientesZW").niceScroll("#listaClientes", {cursorcolor: "#000", cursoropacitymax: 0.4,
                        hwacceleration: false, scrollspeed: 30});
                }, 1000);
                carregandoBtn();
                break;
            }
        case 2 :
            {
                jQuery(".passoC").addClass("stack2");
                jQuery(".passo3").removeClass("passoA passoPequeno").addClass("passoB passoGrande");
                jQuery(".passo3 .fotoAlunoTopoM").animate({opacity: 1}, 500);
                jQuery("#listaClientesZW").niceScroll().hide();
                passoAtual = 3;
                break;
            }
        case 3 :
            {
                jQuery(".passo4").removeClass("passoA passoPequeno").addClass("passoB passoGrande");
                passoAtual = 4;
                break;
            }
        case 4 :
            {
                jQuery(".passo5").removeClass("passoA passoPequeno").addClass("passoB passoGrande");
                passoAtual = 5;
                break;
            }
        case 5 :
            {
                jQuery(".passo6").removeClass("passoA passoPequeno").addClass("passoB passoGrande");
                passoAtual = 6;
                break;
            }
    }
}

function passoArbitrario(passo) {
    switch (passo) {
        case 1 :
            {
                jQuery(".passoC").removeClass("passoC").addClass("passoA");
                jQuery(".passoB:not(.QRContem)").removeClass("passoB passoGrande").addClass("passoA passoPequeno");
                jQuery(".passo1").removeClass("passoA passoPequeno").addClass("passoB passoGrande");
                jQuery(".passo2").removeClass("passoListaClientes");
                //jQuery(".passo3 .fotoAlunoTopoM").animate({opacity:1},500);
                passoAtual = 1;
                break;
            }
        case 2 :
            {
                jQuery(".passoC:not(.passo1)").removeClass("passoC").addClass("passoA");
                jQuery(".passoB:not(.QRContem)").removeClass("passoB passoGrande").addClass("passoA passoPequeno");
                jQuery(".passo2").removeClass("passoA passoPequeno").addClass("passoB passoGrande passoListaClientes");
                //jQuery(".passo3 .fotoAlunoTopoM").animate({opacity:1},500);
                window.setTimeout(function() {
                    jQuery("#listaClientesZW").niceScroll().show();
                }, 1000);
                passoAtual = 2;
                break;
            }
    }
}

jQuery('.passoC').live('click', function() {
    if (jQuery(this).hasClass("passo1")) {
        passoArbitrario(1);
    } else if (jQuery(this).hasClass("passo2")) {
        passoArbitrario(2);
    } else if (jQuery(this).hasClass("passo3")) {
        passoArbitrario(3);
    } else if (jQuery(this).hasClass("passo4")) {
        passoArbitrario(4);
    } else if (jQuery(this).hasClass("passo5")) {
        passoArbitrario(5);
    }
});

function toggleQRCodeView() {
    jQuery(".passo3").toggleClass("girado");
    jQuery(".QRContem").toggleClass("girado");
}

function carregandoBtn() {
    var btn = jQuery(".btn-buscarJS");
    if (!isCarregando) {
        //Armazena valor antigo devido a i18
        valorAntigoBtnBuscar = btn.html();
        var html = "<i class='fa-icon-refresh fa-icon-spin'/> Buscando...";
        btn.html(html);
        isCarregando = true;
    } else {
        var html = valorAntigoBtnBuscar;
        btn.html(html);
        isCarregando = false;
    }
}
function salvarBtn() {
    var btn = jQuery(".btn-concluir");
    if (!isCarregando) {
        //Armazena valor antigo devido a i18
        valorAntigoBtnGravar = btn.html();
        var html = "<i class='fa-icon-refresh fa-icon-spin'/> Salvando...";
        btn.html(html);
        isCarregando = true;
    } else {
        try{
            var html = valorAntigoBtnGravar;
            btn.html(html);
        }catch(e){
            
        }
        
        isCarregando = false;
    }
}