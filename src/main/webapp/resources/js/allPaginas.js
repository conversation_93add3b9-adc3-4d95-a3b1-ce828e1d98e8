var timerToggleModulos, intervalo;
var rExps = {a: '[a\xE0-\xE6]', e: '[e?\xE8-\xEB]', i: '[i\xEC-\xEF]', o: '[o\xF2-\xF6]', u: '[u\xF9-\xFC]', n: '[n\xF1]'};
var contaTempo, timeOutInfiniteScroll;
var algumaGavetaRodapeAberta = false;
var isFull = false;

$(document).ready(function(e) {

    // Conta as notificações
    contaNotificacao = 0;

    // Seta evento para acontecer quando usuário desliza(Scroll) a página
    $(window).scroll(fixaDesfixaBarraNotifica);
    fixaDesfixaBarraNotifica();

    // Toggle do menu dos modulos (ZW,CRM,FINAN,ETC...)
    /*
     $(".topper ul").mouseenter(function() {
     window.clearInterval(timerToggleModulos);
     $(".topper").animate({top: 0}, 300);
     }).mouseleave(function() {
     timerToggleModulos = window.setTimeout(function() {
     $(".topper li").removeClass("open");
     $(".topper").animate({top: -66}, 300);
     }, 1000);
     });
     */
    $(".topper ul").mouseleave(function() {
        timerToggleModulos = window.setTimeout(function() {
            $(".topper li").removeClass("open");
            $(".topper").animate({top: -66}, 300);
        }, 1000);
    });
    $(".modulo").click(function() {
        $(".topper").animate({top: 0}, 300);
    });
    
    try {
        document.addEventListener('touchstart', function(e) {
            //e.preventDefault();
            var myTarget = $(".topper ul");
            var clicked = e.target.className;
            if ($.trim(clicked) !== '') {
                if ($.trim(myTarget) !== '') {
                    if ($("." + myTarget) !== clicked) {
                        $(".topper li").removeClass("open");
                        $(".topper").animate({top: -66}, 300);
                    }
                }
            }
        }, false);
    } catch (e) {
        console.log("Erro: " + e);
    }

    try {
        $(".tagGrupos").chosen({});
    } catch (e) {
        //console.log("Erro: "+e)
    }

    // Listener para desativar menu de usuário apenas no mouseLeave
    jQuery(".nav.pull-right.mainMNU > li.dropdown > ul.dropdown-menu").on("mouseleave", function() {
        jQuery(this).parent(".open").removeClass("open");
    });

    // ScrollInfinito
    var hasInfinito = jQuery('.scrollInfinito');
    if (hasInfinito.length > 0) {
        $(window).scroll(function() {
            window.clearTimeout(timeOutInfiniteScroll);
            timeOutInfiniteScroll = window.setTimeout(function() {
                if (typeof (viewPortTopInicio) == "undefined") {
                    viewPortTopInicio = 0;
                }
                if (typeof(carregaMais) !== "undefined") {
                    var finalAtual = jQuery(hasInfinito).position().top + jQuery(hasInfinito).height();
                    var viewPortFim = jQuery(window).scrollTop() + jQuery(window).height();
                    if (viewPortFim > finalAtual && viewPortTopInicio < jQuery(window).scrollTop()) {
                        carregaMais();
                    }
                    viewPortTopInicio = jQuery(window).scrollTop();
                }
            }, 1000);
        });
    }

});

$(window).load(function() {
    fixaDesfixaBarraNotifica();

    // Limpar campos numericos se "0"
    jQuery("body").on("focus",".ui-inputNum input.ui-inputfield.ui-inputtext",function(){
        if(jQuery(this).val() == "0") {
            jQuery(this).val("");

            jQuery(this).on("blur", function(){
                if(jQuery(this).val() == "") {
                    jQuery(this).val("0");
                }
            });
        }
    });
});

function fixaDesfixaBarraNotifica() {
    var viewportHeight = $(window).height();
    var fooHeight = $("footer").height();
    var bodyHeight = $("body").height();
    var myDistance = $(window).scrollTop();
    var botPos = myDistance + viewportHeight;
    var limite = (bodyHeight - fooHeight) + 103;
    if (botPos < limite) {
        jQuery(".nav-bottom").addClass('navbar-fixed-bottom');
        jQuery(".affix").removeClass("fixedBottom");
        jQuery(".nav-bottom > i").removeClass("fa-icon-chevron-down").addClass('fa-icon-chevron-up');
    } else {
        jQuery(".nav-bottom").removeClass('navbar-fixed-bottom');
        jQuery(".affix").addClass("fixedBottom");
        jQuery(".nav-bottom > i").removeClass("fa-icon-chevron-up").addClass('fa-icon-chevron-down');
    }
}
function vaiCard(obj) {
    jQuery(obj).parents('.block').addClass('flip');
}
function voltaCard(obj) {
    jQuery(obj).parents('.block').removeClass('flip');
}

function fixViewCards() {
    try {
        $(".tagGrupos").chosen({});
    } catch (e) {
        //console.log("Erro: "+e)
    }
}

function funcDetalha() {
    var viewportWidth = $(window).width();
    $(".contemTabela").animate({width: (viewportWidth / 4)}, 1000);
    $(".contemDeta").animate({width: (viewportWidth / 4) * 3}, 1000);
}

function adicionandoFicha() {
    var curHeight = jQuery('div.menuLateral.listaPequena').height();
    jQuery('div.menuLateral.listaPequena').css('height', 'auto');
    var autoHeight = $('div.menuLateral.listaPequena').height();
    jQuery('div.menuLateral.listaPequena').height(curHeight);
    jQuery("div.menuLateral.listaPequena").animate({opacity: 1}, 250).animate({height: autoHeight}, 550);
    jQuery('div.menuLateral.listaPequena.semHeaderColunas').height(autoHeight);
    if (jQuery('div.menuLateral.listaPequena.semHeaderColunas').width() > 350) {
        jQuery(window).scrollTop(jQuery('div.caixaPrincipal').offset().top - 115);
    }
}

function adicionandoFichaAtividades() {
    var curHeight = $('div.menuLateral.listaPequenaAtv').height();
    jQuery('div.menuLateral.listaPequenaAtv').css('height', 'auto');
    var autoHeight = $('div.menuLateral.listaPequenaAtv').height();
    jQuery('div.menuLateral.listaPequenaAtv').height(curHeight);
    jQuery("div.menuLateral.listaPequenaAtv").animate({opacity: 1}, 250).animate({height: autoHeight}, 750, function() {
        jQuery("div.menuLateral.listaPequenaAtv").css('height', 'auto')
    });
}

function adicionouFicha() {
    jQuery("div.menuLateral.listaPequena").animate({height: 0}, 500).animate({opacity: 0}, 250);
}

function setaFiltroPrincipal(obj) {
    var objeto = jQuery(obj);
    jQuery(objeto).addClass("span3");
    jQuery(".atividadeFiltroContainer table.span3 tr").remove();
    var celula = jQuery('<td/>');
    jQuery(celula).addClass("selecionado").append(objeto);
    var linhaSelecionada = jQuery('<tr/>');
    jQuery(linhaSelecionada).append(celula);
    jQuery(".atividadeFiltroContainer table.span3").append(linhaSelecionada);
    jQuery(".atividadeFiltroContainer > .span6").animate({"opacity": 1}, 150);
}

function setaFiltroSecundario(obj) {
    var objeto = jQuery(obj);
    jQuery(".atividadeFiltroContainer .span6 div").remove();
    var celula = jQuery('<div/>');
    jQuery(celula).addClass("selecionado").css("width", "600px").append(objeto);
    jQuery(".atividadeFiltroContainer .span6").append(celula);
    jQuery(".atividadeFiltroContainer .span6").css("overflow-y", "hidden").css("height", "40px").css("width", "600px")
            .css("border-bottom", "1px solid #9B9B9B");
    jQuery(".atividadeFiltroContainer").animate({"height": 40}, 150);
}

function mostraEscondeNotifica() {
    topAtual = parseFloat(jQuery(".popDoRodape.contemUltimasNotifica").css("top"), 10) * (-1);
    aux = parseFloat(jQuery(".contemTabelaNotifica").css("height"), 10);
    tamanhoTabelaNotifica = aux * (-1);
    if (topAtual > 0) {
        algumaGavetaRodapeAberta = false;
        //console.log("Val: " + topAtual);
        jQuery(".popDoRodape.contemUltimasNotifica").stop().animate({top: 0}, 1000, "easeOutQuint", function() {
            if (!algumaGavetaRodapeAberta) {
                jQuery(".nav-bottom").css("overflow", "hidden");
            }
        });
        topAtual = parseFloat(jQuery(".popDoRodape.contemUltimasNotifica").css("top"), 10) * (-1);
    } else {
        algumaGavetaRodapeAberta = true;
        jQuery(".popDoRodape.contemUltimasNotifica").stop().animate({top: tamanhoTabelaNotifica}, 1000, "easeOutQuint");
        topAtual = parseFloat(jQuery(".popDoRodape.contemUltimasNotifica").css("top"), 10) * (-1);
        jQuery(".nav-bottom").css("overflow", "visible");
    }
    return false;
}

function contaMaisNotificacao(reset) {
    if (reset) {
        jQuery(".notificaBarraInferior").removeClass("ativa");
        contaNotificacao = 0;
        jQuery(".contaNotifica").html("");
    } else {
        contaNotificacao = jQuery(".contaNotifica").html().length === 0 ? 0 : jQuery(".contaNotifica").html();
        if (contaNotificacao === 0) {
            jQuery(".notificaBarraInferior").addClass("ativa");
        }
        contaNotificacao++;
        jQuery(".contaNotifica").html(contaNotificacao);
    }
}

function mostraEscondeAlunos() {
    topAtual = parseFloat(jQuery(".popDoRodape.contemAcompanhados").css("top"), 10) * (-1);
    aux = parseFloat(jQuery(".contemAcompanhados .contemTabelaAcomp").css("height"), 10);
    tamanhoTabelaNotifica = aux * (-1);
    if (topAtual > 0) {
        algumaGavetaRodapeAberta = false;
        jQuery(".popDoRodape.contemAcompanhados").stop().animate({top: 0}, 1000, "easeOutQuint", function() {
            if (!algumaGavetaRodapeAberta) {
                jQuery(".nav-bottom").css("overflow", "hidden");
            }
        });
        topAtual = parseFloat(jQuery(".popDoRodape.contemAcompanhados").css("top"), 10) * (-1);
    } else {
        algumaGavetaRodapeAberta = true;
        //console.log("Val: " + topAtual);
        jQuery(".popDoRodape.contemAcompanhados").stop().animate({top: tamanhoTabelaNotifica}, 1000, "easeOutQuint");
        topAtual = parseFloat(jQuery(".popDoRodape.contemAcompanhados").css("top"), 10) * (-1);
        jQuery(".nav-bottom").css("overflow", "visible");
    }
    return false;
}

//Futuramente criar engine generica para contagem de tempo.
var timerTempoExecucaoSerie;
var horaInicio;
function contaTempoExecucaoSerie(acumulado) {
    var alvo = jQuery(".caixaPrincipal .topoExecucao .contadorTempoTreino");
    horaInicio = new Date().getTime();
    var tempoTrans = parseInt(acumulado);
    timerTempoExecucaoSerie = window.setInterval(function() {
        var agora = new Date().getTime();
        var diff = ((agora - horaInicio) / 1000) + tempoTrans;
        var minutos = parseInt(diff / 60);
        var segundos = parseInt(diff % 60);
        if (minutos < 10 && minutos >= 0) {
            minutos = "0" + minutos;
        }
        if (segundos < 10 && segundos >= 0) {
            segundos = "0" + segundos;
        }

        jQuery(alvo).children(".minutosSerie").html(minutos);
        jQuery(alvo).children(".segundosSerie").html(segundos);
    }, 250);
}
function removeTempoExecucaoSerie() {
    window.clearInterval(timerTempoExecucaoSerie);
    var alvo = jQuery(".caixaPrincipal .topoExecucao .contadorTempoTreino");
    jQuery(alvo).children(".minutosSerie").html("--");
    jQuery(alvo).children(".segundosSerie").html("--");
}
// OUTRAS FUNCS
/**
  * jQuery.browser.mobile (http://detectmobilebrowser.com/)
  * jQuery.browser.mobile will be true if the browser is a mobile device
  **/
// (function(a) {
//     jQuery.browser.mobile = /android|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(ad|hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|symbian|tablet|treo|up\.(browser|link)|vodafone|wap|webos|windows (ce|phone)|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|e\-|e\/|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(di|rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|xda(\-|2|g)|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4))
// })(navigator.userAgent || navigator.vendor || window.opera);
/**
  * jQuery.browser.mobile (http://detectmobilebrowser.com/)
  * jQuery.browser.mobile will be true if the browser is a mobile device
  **/
// (function(a) {
//     jQuery.browser.smallMobile = /android.+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|e\-|e\/|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(di|rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|xda(\-|2|g)|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4))
// })(navigator.userAgent || navigator.vendor || window.opera);

function incrementaSpinner(classe, max) {
    var valor = parseInt($('.' + classe).val());
    if (valor < max) {
        $('.' + classe).val(valor + 1);
    }
}
function decrementaSpinner(classe, min) {
    var valor = parseInt($('.' + classe).val());
    if (valor > min) {
        $('.' + classe).val(valor - 1);
    }
}
function selecionarAtividade(classe) {
    $('.' + classe).addClass('atividadeSelecionada');
    $('.' + classe).css('color', '#fff');
}

function selecionarAtividades() {
    $('a[class*="atv"]').addClass('atividadeSelecionada');
    $('a[class*="atv"]').css('color', '#fff');
}