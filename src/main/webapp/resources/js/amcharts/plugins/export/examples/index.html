<html>
    <head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />

		<script src="http://code.jquery.com/jquery-1.11.1.min.js"></script>

		<style type="text/css">
			iframe {
				width: 900px;
				height: 600px;
				padding: 0;
				border: 2px solid #ddd;
				overflow: hidden;
				position: relative;
			}
			#controls {
				background: #ccc;
				opacity: 0.5;
				padding: 5px 8px;
				font-size: 14px;
				font-weight: bold;
				font-family: Verdana;
				margin-bottom: 10px;
			}
		</style>

		<script type="text/javascript">
			function setType( type ) {
				$( '#iframe' ).attr( 'src', type );
			}

			function reload() {
				setType( $( '#type' ).val() + "?" + Number( new Date() ) );
			}
		</script>
	</head>
	<body>
		<div id="controls">
			<select id="type" onchange="setType(this.options[this.selectedIndex].value);">
				<option value="pie1.html" selected="selected">Pie #1 (Simple w/ legend)</option>
				<option value="pie2.html"/>Pie #2 (3D w/ legend)</option>
				<option value="pie3.html"/>Pie #3 (w/ ext. legend bottom)</option>
				<option value="pie4.html"/>Pie #4 Multiple instances</option>
				<option value="serial1.html">Serial #1 (Area)</option>
				<option value="serial2.html">Serial #2 (Bar &amp; Line)</option>
				<option value="serial3.html">Serial #3 (Line w/ zoom)</option>
				<option value="xy.html">XY</option>
				<option value="radar.html">Radar</option>
				<option value="gauge.html">Gauge</option>
				<option value="funnel.html">Funnel</option>
				<option value="stock.html">Stock</option>
				<option value="map.html">Map</option>
				<option value="gantt.html">Gantt</option>
				<option value="advanced.html">Advanced</option>
			</select>
			<input type="button" value="reload" onclick="reload();" />
		</div>

		<iframe id="iframe" src="pie1.html" frameborder="0"></iframe>
	</body>
</html>