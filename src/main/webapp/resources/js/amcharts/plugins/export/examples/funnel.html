<html>
    <head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />

		<!-- AmCharts includes -->
		<script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
		<script src="http://www.amcharts.com/lib/3/funnel.js"></script>

		<!-- Export plugin includes and styles -->
		<script src="../export.js"></script>
		<link  type="text/css" href="../export.css" rel="stylesheet">

		<style>
		body, html {
			height: 100%;
			padding: 0;
			margin: 0;
			overflow: hidden;
			font-size: 11px;
			font-family: Verdana;
		}
		#chartdiv {
			width: 100%;
			height: 100%;
		}
		</style>

		<script type="text/javascript">
			var chart = AmCharts.makeChart( "chartdiv", {
				"type": "funnel",
				"dataProvider": [ {
					"title": "Website visits",
					"value": 200
				}, {
					"title": "Downloads",
					"value": 123
				}, {
					"title": "Requested price list",
					"value": 98
				}, {
					"title": "Contaced for more info",
					"value": 72
				}, {
					"title": "Purchased",
					"value": 35
				}, {
					"title": "Contacted for support",
					"value": 35
				}, {
					"title": "Purchased additional products",
					"value": 26
				} ],
				"balloon": {
					"fixedPosition": true
				},
				"legend": {},
				"valueField": "value",
				"titleField": "title",
				"marginRight": 240,
				"marginLeft": 50,
				"startX": -500,
				"depth3D": 100,
				"angle": 40,
				"outlineAlpha": 1,
				"outlineColor": "#FFFFFF",
				"outlineThickness": 2,
				"labelPosition": "right",
				"balloonText": "[[title]]: [[value]]n[[description]]",
				"export": {
					"enabled": true
				}
			} );
		</script>
	</head>
	<body>
		<div id="chartdiv"></div>
	</body>
</html>