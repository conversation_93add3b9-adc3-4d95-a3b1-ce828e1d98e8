<html>
    <head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />

		<!-- AmCharts includes -->
		<script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
		<script src="http://www.amcharts.com/lib/3/pie.js"></script>

		<!-- Export plugin includes and styles -->
		<script src="../export.js"></script>
		<link  type="text/css" href="../export.css" rel="stylesheet">

		<style>
		body, html {
			height: 100%;
			padding: 0;
			margin: 0;
			overflow: hidden;
			font-size: 11px;
			font-family: Verdana;
		}
		#chartdiv {
			width: 100%;
			height: 100%;
		}
		</style>

		<script type="text/javascript">
			var chart = AmCharts.makeChart( "chartdiv", {
				"type": "pie",
				"dataProvider": [ {
					"country": "Czech Republic",
					"litres": 156.9
				}, {
					"country": "Ireland",
					"litres": 131.1
				}, {
					"country": "Germany",
					"litres": 115.8
				}, {
					"country": "Australia",
					"litres": 109.9
				}, {
					"country": "Austria",
					"litres": 108.3
				}, {
					"country": "UK",
					"litres": 65
				}, {
					"country": "Belgium",
					"litres": 50
				} ],
				"titleField": "country",
				"valueField": "litres",
				"balloonText": "[[title]]<br><span style='font-size:14px'><b>[[value]]</b> ([[percents]]%)</span>",
				"innerRadius": "30%",
				"legend": {
					"position": "right",
					"markerType": "circle"
				},
				"export": {
					"enabled": true
				}
			} );
		</script>
	</head>
	<body>
		<div id="chartdiv"></div>
	</body>
</html>