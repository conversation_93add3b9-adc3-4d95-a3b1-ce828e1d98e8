<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />

    <style>
      html, body {
        width: 100%;
        height: 100%;
        margin: 0px;
      }

      #chartdiv {
        width: 100%;
        height: 100%;
      }
    </style>
  </head>
  <body>
    <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
    <script src="http://www.amcharts.com/lib/3/pie.js"></script>
    <script src="http://www.amcharts.com/lib/3/themes/light.js"></script>
    <script src="../animate.min.js"></script>

    <div id="chartdiv"></div>

    <script>
      var chart = AmCharts.makeChart("chartdiv", {
        "type": "pie",
        "theme": "light",
        "dataProvider": generateChartData(),
        "valueField": "value",
        "titleField": "category",
        "labelRadiusField": "labelRadius",
        "alphaField": "alpha",
        "startDuration": 0
      });


      function generateChartData() {
        var chartData = [];

        for ( var i = 0; i < 10; i++ ) {
          var value = Math.floor(Math.random() * 100);
          var labelRadius = Math.floor(Math.random() * 100);
          var alpha = Math.random();

          chartData.push( {
            category: "" + i,
            value: value,
            labelRadius: labelRadius,
            alpha: alpha
          } );
        }

        return chartData;
      }


      function loop() {
        var data = generateChartData();

        chart.animateData(data, {
          duration: 1000,
          complete: function () {
            setTimeout(loop, 2000);
          }
        });
      }

      chart.addListener("init", function () {
        setTimeout(loop, 1000);
      });
    </script>
  </body>
</html>
