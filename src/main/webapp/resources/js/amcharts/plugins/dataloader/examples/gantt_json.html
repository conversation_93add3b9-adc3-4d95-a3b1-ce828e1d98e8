<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>amCharts Data Loader Example</title>
    <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
    <script src="http://www.amcharts.com/lib/3/serial.js"></script>
    <script src="http://www.amcharts.com/lib/3/gantt.js"></script>
    <script src="http://www.amcharts.com/lib/3/themes/light.js"></script>
    <script src="../dataloader.min.js"></script>
    <style>
    body, html {
      font-family: Verdana;
      font-size: 12px;
    }
    #chartdiv {
      width: 100%;
      height: 500px;
    }
    </style>
    <script>
      AmCharts.useUTC = true;
      var chart = AmCharts.makeChart("chartdiv", {
        "type": "gantt",
        "theme": "light",
        "dataLoader": {
          "url": "data/gantt.json"
        },
        "dataProvider": [],
        "period": "hh",
        "dataDateFormat": "YYYY-MM-DD",
        "balloonDateFormat": "JJ:NN",
        "columnWidth": 0.5,
        "valueAxis": {
          "type": "date",
          "minimum": 7,
          "maximum": 31
        },
        "brightnessStep": 10,
        "graph": {
          "fillAlphas": 1,
          "balloonText": "<b>[[task]]</b>: [[open]] [[value]]"
        },
        "rotate": true,
        "categoryField": "category",
        "segmentsField": "segments",
        "colorField": "color",
        "startDate": "2015-01-01",
        "startField": "start",
        "endField": "end",
        "durationField": "duration",
        "chartScrollbar": {},
        "chartCursor": {
          "valueBalloonsEnabled": false,
          "cursorAlpha": 0.1,
          "valueLineBalloonEnabled": true,
          "valueLineEnabled": true,
          "fullWidth": true
        }
      });
    </script>
  </head>

  <body>
    <div id="chartdiv"></div>
  </body>

</html>