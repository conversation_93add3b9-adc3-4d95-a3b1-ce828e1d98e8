<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>amCharts Data Loader Example</title>
    <link rel="stylesheet" href="http://www.amcharts.com/lib/3/ammap.css" type="text/css">
    <script src="http://www.amcharts.com/lib/3/ammap.js"></script>
    <script src="http://www.amcharts.com/lib/3/maps/js/usaLow.js"></script>
    <script src="../dataloader.min.js"></script>
    <style>
     body, html {
      font-family: Verdana;
      font-size: 12px;
    }
    #chartdiv {
      width: 100%;
      height: 500px;
    }
    </style>
    <script>
      var map = AmCharts.makeChart( "chartdiv", {
        type: "map",
        "dataLoader": {
          "url": "data/map.json",
          "showErrors": false
        },
        "colorSteps": 10,
        "areasSettings": {
          "autoZoom": true
        },
        "smallMap": {},
        "valueLegend": {
          "right": 10,
          "minValue": "little",
          "maxValue": "a lot!"
        }
      } );
    </script>
  </head>

  <body>
    <div id="chartdiv"></div>
  </body>

</html>