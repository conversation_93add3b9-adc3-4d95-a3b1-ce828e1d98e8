<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>amCharts Responsive Example</title>
    <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
    <script src="http://www.amcharts.com/lib/3/gauge.js"></script>
    <script src="../responsive.min.js"></script>
    <style>
    body, html {
      height: 100%;
      padding: 0;
      margin: 0;
    }
    </style>
    <script>
      var chart = AmCharts.makeChart("chartdiv", {
        "type": "gauge",
        "titles": [{
            "text": "Speedometer",
            "size": 15
        }],
        "axes": [{
          "startValue": 0,
          "axisThickness": 1,
          "endValue": 220,
          "valueInterval": 10,
          "bottomTextYOffset": -20,
          "bottomText": "0 km/h",
          "bands": [{
              "startValue": 0,
              "endValue": 90,
              "color": "#00CC00"
            },
            {
              "startValue": 90,
              "endValue": 130,
              "color": "#ffac29"
            },
            {
              "startValue": 130,
              "endValue": 220,
              "color": "#ea3838",
              "innerRadius": "95%"
            }
          ]
        }],
        "arrows": [{}],
        "responsive": {
          "enabled": true
        }
      });

      setInterval(randomValue, 2000);

      // set random value
      function randomValue() {
        var value = Math.round(Math.random() * 200);
        chart.arrows[0].setValue(value);
        chart.axes[0].setBottomText(value + " km/h");
      }
    </script>
  </head>

  <body>
    <div id="chartdiv" style="width: 100%; height: 100%;"></div>
  </body>

</html>