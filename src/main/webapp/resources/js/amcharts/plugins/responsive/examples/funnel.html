<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

  <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
      <title>amCharts Responsive Example</title>
      <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
      <script src="http://www.amcharts.com/lib/3/funnel.js"></script>
      <script src="../responsive.min.js"></script>
      <style>
      body, html {
          height: 100%;
          padding: 0;
          margin: 0;
      }
      </style>
      <script>
      var chart = AmCharts.makeChart("chartdiv", {
        "type": "funnel",
        "dataProvider": [{
          "title": "Website visits",
          "value": 200
        }, {
          "title": "Downloads",
          "value": 123
        }, {
          "title": "Requested price list",
          "value": 98
        }, {
          "title": "Contaced for more info",
          "value": 72
        }, {
          "title": "Purchased",
          "value": 35
        }, {
          "title": "Contacted for support",
          "value": 35
        }, {
          "title": "Purchased additional products",
          "value": 26
        }],
        "balloon": {
          "fixedPosition": true
        },
        "legend": {},
        "valueField": "value",
        "titleField": "title",
        "marginRight": 240,
        "marginLeft": 50,
        "startX": -500,
        "depth3D":100,
        "angle":40,
        "outlineAlpha":1,
        "outlineColor":"#FFFFFF",
        "outlineThickness":2,
        "labelPosition": "right",
        "balloonText": "[[title]]: [[value]]n[[description]]",
        "responsive": {
          "enabled": true
        }
      });

      </script>
  </head>

  <body>
    <div id="chartdiv" style="width: 100%; height: 100%;"></div>
  </body>

</html>