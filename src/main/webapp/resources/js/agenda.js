/**
 * Created with IntelliJ IDEA.
 * User: joaobeno
 * Date: 01/02/14
 * Time: 09:08
 * To change this template use File | Settings | File Templates.
 */
var arrayProfessoresDisponibilidade = [];
var diasDisponibilidadeCount = [];
var listaTodosEventos = [];
var professorMargemEsquerda = [];
var barraLateral = null;
var conflitosDoDia = [];

jQuery(window).load(function(e) {

    largura = jQuery(window).width();

    if (jQuery.browser.mobile) {
        //agendaFull();
    }

    jQuery(window).on("resize", function() {
        if (largura < (jQuery(window).width() - 30) || largura > (jQuery(window).width() + 30)) {
            myschedule.jqc.fullCalendar('option', 'height', alturaAgendaObjt);
            window.setTimeout(function() {
                corrigeDisponibilidade();
                largura = jQuery(window).width();
            }, 350);
        }
    });

});

jQuery(document).on("ajaxComplete", function() {
    myschedule.jqc.fullCalendar('option', 'height', alturaAgendaObjt);
    corrigeDisponibilidade();
    corrigeAgendamentos();

});


function corrigeDisponibilidade() {
    arrayProfessoresDisponibilidade = [];
    diasDisponibilidadeCount = [];
    listaTodosEventos = [];
    professorMargemEsquerda = [];
    conflitosDoDia = [];

    jQuery(".legendas .blocoLegenda[class*='prof']").removeClass("cor1 cor2 cor3 cor4 cor5 cor6 cor7 cor8 cor9 cor10");

    //Mes Agrupado por Professor
    jQuery(".miniDisponibilidade.miniDispProfs .fc-view-month div.fc-event[class*='dia'].disponibilidade").each(function() {
        // Acha o dia do item
        var diaEvento = jQuery(this).attr("class");
        var indexDia = diaEvento.indexOf("dia");
        diaEvento = diaEvento.substr(indexDia + 3, 2);
        diaEvento = parseInt(diaEvento);
        // seta objeto outro mes ou esse mes
        var seletorCaixaDia;
        var seletor;
        if (jQuery(this).hasClass("mesA")) {
            seletor = "td.ui-widget-content > div:contains('" + diaEvento + "')";
            seletorCaixaDia = jQuery(seletor).parent(".ui-widget-content:not(.fc-other-month)");
        } else {
            seletor = ".fc-other-month > div:contains('" + diaEvento + "')";
            if (diaEvento < 10) {
                seletorCaixaDia = jQuery(seletor).last().parent(".fc-other-month");
            } else {
                seletorCaixaDia = jQuery(seletor).first().parent(".fc-other-month");
            }
        }
        var alturaDia = jQuery(seletorCaixaDia).height();
        jQuery(this).css("height", alturaDia);
        var offsetParent = jQuery(".fc-content").offset().top;
        var offsetDia = jQuery(seletorCaixaDia).offset().top;
        var offsetFinal = offsetDia - offsetParent;
        jQuery(this).css("top", (offsetFinal + 1) + "px");

        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("profe");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);

        // Acha o professor do item
        if (diasDisponibilidadeCount[diaEvento] == undefined) {
            diasDisponibilidadeCount[diaEvento] = 1;
        } else if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            diasDisponibilidadeCount[diaEvento] = diasDisponibilidadeCount[diaEvento] + 1;
        }

        if (professorMargemEsquerda[diaEvento] === undefined) {
            professorMargemEsquerda[diaEvento] = [];
        }

        var corDispProf;
        var margemEsquerda;

        // Seta apenas as CORES dos professores, cuidado
        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);
            var seletorLegenda = ".legendas .blocoLegenda." + classeProfessorCodigo;
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(seletorLegenda).addClass("prof" + (corDispProf + 1));
        } else {
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
        }

        jQuery(this).addClass("prof" + (corDispProf + 1));

        // Seta apenas as POSIÇÕES dos professores, cuidado
        if (professorMargemEsquerda[diaEvento].indexOf(classeProfessorCodigo) === -1) {
            professorMargemEsquerda[diaEvento].push(classeProfessorCodigo);
            margemEsquerda = professorMargemEsquerda[diaEvento].indexOf(classeProfessorCodigo);
        } else {
            margemEsquerda = professorMargemEsquerda[diaEvento].indexOf(classeProfessorCodigo);
        }
        jQuery(this).css("margin-left", ((margemEsquerda * 3) - 3) + "px");
    });

    //Mes Agrupado por Evento
    jQuery(".miniDisponibilidade.miniDispTipoEvento .fc-view-month div.fc-event[class*='dia'].disponibilidade").each(function() {
        // Acha o dia do item
        var diaEvento = jQuery(this).attr("class");
        var indexDia = diaEvento.indexOf("dia");
        diaEvento = diaEvento.substr(indexDia + 3, 2);
        diaEvento = parseInt(diaEvento);
        // seta objeto outro mes ou esse mes
        var seletorCaixaDia;
        var seletor;
        if (jQuery(this).hasClass("mesA")) {
            seletor = "td.ui-widget-content div.fc-day-number:contains('" + diaEvento + "')";
            seletorCaixaDia = jQuery(seletor).parents().parent(".ui-widget-content:not(.fc-other-month)");
        } else {
            seletor = ".fc-other-month div.fc-day-number:contains('" + diaEvento + "')";
            if (diaEvento < 10) {
                seletorCaixaDia = jQuery(seletor).last().parents().parent(".fc-other-month");
            } else {
                seletorCaixaDia = jQuery(seletor).first().parents().parent(".fc-other-month");
            }
        }
        var alturaDia = jQuery(seletorCaixaDia).height();
        jQuery(this).css("height", alturaDia);
        var offsetParent = jQuery(".fc-content").offset().top;
        var offsetDia = jQuery(seletorCaixaDia).offset().top;
        var offsetFinal = offsetDia - offsetParent;
        jQuery(this).css("top", (offsetFinal + 1) + "px");

        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("tipoCod");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);

        // Acha o professor do item
        if (diasDisponibilidadeCount[diaEvento] == undefined) {
            diasDisponibilidadeCount[diaEvento] = 1;
        } else if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            diasDisponibilidadeCount[diaEvento] = diasDisponibilidadeCount[diaEvento] + 1;
        }

        if (professorMargemEsquerda[diaEvento] === undefined) {
            professorMargemEsquerda[diaEvento] = [];
        }

        var margemEsquerda;

        // Seta apenas as POSIÇÕES dos professores, cuidado
        if (professorMargemEsquerda[diaEvento].indexOf(classeProfessorCodigo) === -1) {
            professorMargemEsquerda[diaEvento].push(classeProfessorCodigo);
            margemEsquerda = professorMargemEsquerda[diaEvento].indexOf(classeProfessorCodigo);
        } else {
            margemEsquerda = professorMargemEsquerda[diaEvento].indexOf(classeProfessorCodigo);
        }
        jQuery(this).css("margin-left", ((margemEsquerda * 3) - 3) + "px");
    });

    //Mes Agrupado por Professor e exibindo disponibilidades
    jQuery("div.miniDispProfs:not(.miniDisponibilidade) .fc-view-month div.fc-event[class*='dia'].disponibilidade").each(function() {
        // Acha o dia do item
        var diaEvento = jQuery(this).attr("class");
        var indexDia = diaEvento.indexOf("dia");
        diaEvento = diaEvento.substr(indexDia + 3, 2);
        diaEvento = parseInt(diaEvento);

        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("profe");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);

        // Acha o professor do item
        if (diasDisponibilidadeCount[diaEvento] == undefined) {
            diasDisponibilidadeCount[diaEvento] = 1;
        } else if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            diasDisponibilidadeCount[diaEvento] = diasDisponibilidadeCount[diaEvento] + 1;
        }

        if (professorMargemEsquerda[diaEvento] === undefined) {
            professorMargemEsquerda[diaEvento] = [];
        }

        var corDispProf;

        // Seta apenas as CORES dos professores, cuidado
        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);
            var seletorLegenda = ".legendas .blocoLegenda." + classeProfessorCodigo;
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(seletorLegenda).addClass("prof" + (corDispProf + 1));
        } else {
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
        }

        jQuery(this).addClass("prof" + (corDispProf + 1));
    });

    //Semana Por Evento
    jQuery(".miniDisponibilidade.miniDispTipoEvento .fc-view-agendaWeek .disponibilidade").each(function() {
        //console.log(this);
        // Acha o dia do item
        jQuery(this).css("background-color", "black !important");
        var diaEvento = jQuery(this).attr("class");
        var indexDia = diaEvento.indexOf("dia");
        diaEvento = (diaEvento.substr(indexDia + 3, 2)).valueOf();

        // seta objeto outro mes ou esse mes
        var seletorCaixaDia;
        var seletor;

        if (diaEvento < 10) {
            seletor = "th[class*='fc-col']:contains('0" + diaEvento + "/')";
        } else {
            seletor = "th[class*='fc-col']:contains('" + diaEvento + "/')";
        }
        seletorCaixaDia = jQuery(seletor);

        var offsetDia = jQuery(seletorCaixaDia).offset().left;
        var offsetCaixa = jQuery(".miniDisponibilidade").offset().left;
        var offsetFinal = offsetDia - offsetCaixa;
        jQuery(this).css("left", (offsetFinal + 1) + "px");

        //jQuery(this).css({top:((jQuery(this).position().top)+102)+"px"});

        // Acha o professor do item

        /*if (diasDisponibilidadeCount[diaEvento.valueOf()] == undefined) {
         diasDisponibilidadeCount[diaEvento.valueOf()] = 1;
         } else {
         diasDisponibilidadeCount[diaEvento.valueOf()] = diasDisponibilidadeCount[diaEvento.valueOf()] + 1;
         }*/

        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("tipoCod");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);



        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(this).css("margin-left", ((margemEsquerda * 3)) + "px");
        } else {
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(this).css("margin-left", ((margemEsquerda * 3)) + "px");
        }
        //console.log(classeProfessorCodigo + ", index: " + arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) + ", Margem: " + ((margemEsquerda * 3)) + "px");
        //jQuery(this).addClass("cor" + margemEsquerda);
    });

    //Semana Por Professor
    jQuery(".miniDisponibilidade.miniDispProfs .fc-view-agendaWeek .disponibilidade").each(function() {
        // Acha o dia do item
        jQuery(this).css("background-color", "black !important");
        var diaEvento = jQuery(this).attr("class");
        var indexDia = diaEvento.indexOf("dia");
        diaEvento = (diaEvento.substr(indexDia + 3, 2)).valueOf();

        // seta objeto outro mes ou esse mes
        var seletorCaixaDia;
        var seletor;

        if (diaEvento < 10) {
            seletor = "th[class*='fc-col']:contains('0" + diaEvento + "/')";
        } else {
            seletor = "th[class*='fc-col']:contains('" + diaEvento + "/')";
        }
        seletorCaixaDia = jQuery(seletor);

        var offsetDia = jQuery(seletorCaixaDia).offset().left;
        var offsetCaixa = jQuery(".miniDisponibilidade").offset().left;
        var offsetFinal = offsetDia - offsetCaixa;
        jQuery(this).css("left", (offsetFinal + 1) + "px");

        // Acha o professor do item

        if (diasDisponibilidadeCount[diaEvento.valueOf()] == undefined) {
            diasDisponibilidadeCount[diaEvento.valueOf()] = 1;
        } else {
            diasDisponibilidadeCount[diaEvento.valueOf()] = diasDisponibilidadeCount[diaEvento.valueOf()] + 1;
        }

        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("prof");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);
        var margemEsquerda; // = diasDisponibilidadeCount[diaEvento.valueOf()];

        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);

            var seletorLegenda = ".legendas .blocoLegenda." + classeProfessorCodigo;
            var corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(seletorLegenda).addClass("prof" + (corDispProf + 1));

            jQuery(this).css("margin-left", ((margemEsquerda * 3) - 6) + "px");
        } else {
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(this).css("margin-left", ((margemEsquerda * 3) - 6) + "px");
        }
        jQuery(this).addClass("prof" + arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo));
    });

    //Dia Por Evento
    var diaDisponibilidadeConta = 0;
    jQuery(".miniDisponibilidade.miniDispTipoEvento .fc-view-agendaDay .disponibilidade").each(function() {
        //console.log(this);
        // Acha o dia do item
        /*
         jQuery(this).css("background-color","black !important");
         var diaEvento = jQuery(this).attr("class");
         var indexDia = diaEvento.indexOf("dia");
         diaEvento = (diaEvento.substr(indexDia+3, 2)).valueOf();
         
         // seta objeto outro mes ou esse mes
         var seletorCaixaDia;
         var seletor;
         
         seletor = "th[class*='fc-col']:contains('"+diaEvento+"')";
         seletorCaixaDia = jQuery(seletor);
         
         /*var alturaDia = jQuery(seletorCaixaDia).height();
         jQuery(this).css("height",alturaDia);
         var offsetParent = jQuery(".fc-content").offset().left;
         var offsetDia = jQuery(seletorCaixaDia).offset().top;* /
         
         var offsetDia = jQuery(seletorCaixaDia).offset().left;
         var offsetCaixa = jQuery(".miniDisponibilidade").offset().left;
         var offsetFinal = offsetDia - offsetCaixa;
         jQuery(this).css("left",(offsetFinal+1)+"px");* /
         
         // Conta quantas disponibilidades tem hoje?
         
         if(diasDisponibilidadeCount[diaEvento.valueOf()] == undefined) {
         diasDisponibilidadeCount[diaEvento.valueOf()] = 1;
         } else {
         diasDisponibilidadeCount[diaEvento.valueOf()] = diasDisponibilidadeCount[diaEvento.valueOf()]+1;
         }*/

        // Acha o professor do item
        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("prof");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);

        //jQuery(this).css({top:((jQuery(this).position().top)+102)+"px"});

        var corDispProf;
        var margemEsquerda;
        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);

            var seletorLegenda = ".legendas .blocoLegenda." + classeProfessorCodigo;
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);

            diaDisponibilidadeConta++;

            jQuery(seletorLegenda).addClass("cor" + (corDispProf + 1));

            jQuery(this).css("left", (67 + (margemEsquerda * 3)) + "px");
        } else {
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(this).css("left", (67 + (margemEsquerda * 3)) + "px");
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
        }
        jQuery(this).addClass("cor" + (margemEsquerda + 1));
    });

    //Dia por Professor
    jQuery(".miniDisponibilidade.miniDispProfs .fc-view-agendaDay .disponibilidade").each(function() {
        // Acha o professor do item
        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("prof");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);

        var corDispProf;
        var margemEsquerda;
        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);

            var seletorLegenda = ".legendas .blocoLegenda." + classeProfessorCodigo;
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);

            diaDisponibilidadeConta++;

            jQuery(seletorLegenda).addClass("prof" + (corDispProf + 1));

            jQuery(this).css("left", (67 + (margemEsquerda * 3)) + "px");
        } else {
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(this).css("left", (67 + (margemEsquerda * 3)) + "px");
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
        }
        jQuery(this).addClass("prof" + (margemEsquerda + 1));
    });

    // Fix View editar disponibilidade
    //Semana
    jQuery("div:not(.miniDisponibilidade) .fc-view-agendaWeek .disponibilidade").each(function() {

        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("prof");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);
        var corDispProf;
        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) + 1;
            var seletorLegenda = ".legendas .blocoLegenda." + classeProfessorCodigo;
            jQuery(seletorLegenda).addClass("cor" + corDispProf);
        } else {
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) + 1;
            //jQuery(this).css("margin-left",((margemEsquerda*3)-6)+"px");
        }
        jQuery(this).addClass("cor" + corDispProf);
    });
    //Semana Por Professor disponibilidade
    jQuery("div:not(.miniDisponibilidade).miniDispProfs .fc-view-agendaWeek .disponibilidade").each(function() {
        // Acha o dia do item
        //jQuery(this).css("background-color","black !important");
        var diaEvento = jQuery(this).attr("class");
        var indexDia = diaEvento.indexOf("dia");
        diaEvento = (diaEvento.substr(indexDia + 3, 2)).valueOf();

        // Acha o professor do item

        if (diasDisponibilidadeCount[diaEvento.valueOf()] == undefined) {
            diasDisponibilidadeCount[diaEvento.valueOf()] = 1;
        } else {
            diasDisponibilidadeCount[diaEvento.valueOf()] = diasDisponibilidadeCount[diaEvento.valueOf()] + 1;
        }

        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("prof");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);

        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);

            var seletorLegenda = ".legendas .blocoLegenda." + classeProfessorCodigo;
            var corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(seletorLegenda).addClass("prof" + (corDispProf + 1));
        }

        jQuery(this).addClass("prof" + arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo));
    });
    //Dia
    jQuery("div:not(.miniDisponibilidade) .fc-view-agendaDay .disponibilidade").each(function() {

        // Acha o professor do item
        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("prof");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);

        var corDispProf;
        var margemEsquerda;
        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);

            var seletorLegenda = ".legendas .blocoLegenda." + classeProfessorCodigo;
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);

            diaDisponibilidadeConta++;

            jQuery(seletorLegenda).addClass("cor" + (corDispProf + 1));

            jQuery(this).css("left", (67 + (margemEsquerda * 3)) + "px");
        } else {
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(this).css("left", (67 + (margemEsquerda * 3)) + "px");
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
        }
        jQuery(this).addClass("cor" + (margemEsquerda + 1));
    });
    //Dia por Professor disponibilidade
    jQuery("div:not(.miniDisponibilidade).miniDispProfs .fc-view-agendaDay .disponibilidade").each(function() {
        // Acha o professor do item
        var professorEvento = jQuery(this).attr("class");
        var indexProfessor = professorEvento.indexOf("prof");
        var tamanhoProfessor = professorEvento.indexOf(" ", indexProfessor);
        var classeProfessorCodigo = professorEvento.substring(indexProfessor, tamanhoProfessor);

        var corDispProf;
        var margemEsquerda;
        if (arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo) === -1) {
            arrayProfessoresDisponibilidade.push(classeProfessorCodigo);

            var seletorLegenda = ".legendas .blocoLegenda." + classeProfessorCodigo;
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);

            diaDisponibilidadeConta++;

            jQuery(seletorLegenda).addClass("prof" + (corDispProf + 1));

            jQuery(this).css("left", (67 + (margemEsquerda * 3)) + "px");
        } else {
            margemEsquerda = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
            jQuery(this).css("left", (67 + (margemEsquerda * 3)) + "px");
            corDispProf = arrayProfessoresDisponibilidade.indexOf(classeProfessorCodigo);
        }
        jQuery(this).addClass("prof" + (margemEsquerda + 1));
    });
}

function corrigeAgendamentos() {
    listaTodosEventos = [];
    visaoDiaColunas = 1;

    // Limpa todos os dias
    jQuery("td.ui-widget-content > div").children(".fc-day-content").each(function() {
        jQuery(this).children(".fc-event").remove();
    });

    //Mes
    jQuery(".fc-view-month div.fc-event[class*='dia']:not(.disponibilidade)").each(function() {
        // Acha o dia do item
        var diaEvento = jQuery(this).attr("class");
        var indexDia = diaEvento.indexOf("dia");
        diaEvento = diaEvento.substr(indexDia + 3, 2);
        diaEvento = parseInt(diaEvento);

        var diaPlus = 0;

        if (jQuery(this).hasClass("mesA")) {
            if (listaTodosEventos[diaEvento] == undefined) {
                listaTodosEventos[diaEvento] = [];
            }
            listaTodosEventos[diaEvento].push(new Evento(this, diaEvento, (jQuery(this).position().top), 30, 0, 0));
            diaPlus = diaEvento;
        } else {
            if (diaEvento > 15) {
                diaPlus = diaEvento + 20;
            } else {
                diaPlus = diaEvento + 31;
            }
            if (listaTodosEventos[diaPlus] == undefined) {
                listaTodosEventos[diaPlus] = [];
            }
            listaTodosEventos[diaPlus].push(new Evento(this, diaPlus, (jQuery(this).position().top), 30, 0));
        }
        // seta objeto outro mes ou esse mes

        var seletorParaAppendar;
        var seletor;
        if (jQuery(this).hasClass("mesA")) {
            seletor = "td.ui-widget-content:not(.fc-other-month) > div:contains('" + diaEvento + "')";
            var definir = jQuery(seletor);
            if (definir.length > 1) {
                jQuery(seletor).each(function(key, val) {
                    if (jQuery(val).children(".fc-day-number").html() == diaEvento.toString()) {
                        seletor = jQuery(val);
                        return false;
                    } else {
                        return true;
                    }
                })
            }
            seletorParaAppendar = jQuery(seletor).children(".fc-day-content");
        } else {
            seletor = ".fc-other-month > div:contains('" + diaEvento + "')";
            //seletorCaixaDia = jQuery(seletor).parent(".fc-other-month");
            seletorParaAppendar = jQuery(seletor).children(".fc-day-content");
        }

        var isto = jQuery(this).detach();
        if (listaTodosEventos[diaPlus].length < 9) {
            jQuery(seletorParaAppendar).append(isto);
            jQuery(seletorParaAppendar).css({paddingLeft: (((listaTodosEventos[diaPlus].length) * 3) - 1) + "px", lineHeight: "10px", paddingRight: 0});
            jQuery(this).css({display: "inline-block", position: "static", width: "33%", margin: "0", overflow: "hidden", height: "20px"});
        }
        if ((listaTodosEventos[diaPlus].length % 2) == 0) {
            jQuery(this).addClass("clear");
        }
    });
    // Semana
    jQuery(".fc-view-agendaWeek div.fc-event[class*='dia']:not(.disponibilidade)").each(function() {
        // Acha o dia do item
        var diaEvento = jQuery(this).attr("class");

        var cod = diaEvento.split("cod");
        cod = cod[1];
        cod = cod.split(" ");
        cod = cod[0];

        var indexDia = diaEvento.indexOf("dia");
        diaEvento = diaEvento.substr(indexDia + 3, 2).valueOf();

        var seletor;

        if (diaEvento < 10) {
            seletor = "th[class*='fc-col']:contains('0" + diaEvento + "/')";
            diaEvento = diaEvento + 35;
            if (typeof (listaTodosEventos[diaEvento]) == "undefined") {
                listaTodosEventos[diaEvento] = [];
            }
        } else {
            seletor = "th[class*='fc-col']:contains('" + diaEvento + "/')";
            if (typeof (listaTodosEventos[diaEvento]) == "undefined") {
                listaTodosEventos[diaEvento] = [];
            }
        }

        seletorCaixaDia = jQuery(seletor);

        var offsetDia = jQuery(seletorCaixaDia).offset().left;
        var offsetCaixa = jQuery(".miniDisponibilidade").offset().left;
        var offsetFinal = offsetDia - offsetCaixa;
        var leftAtual = jQuery(this).position().left;
        jQuery(this).css("left", (offsetFinal + 1) + "px");
        var larguraAtual = jQuery(this).width();
        var novaLargura = (leftAtual - offsetFinal) + larguraAtual;
        jQuery(this).css("width", novaLargura + "px");

        /*if (listaTodosEventos.length == 0) {
         var offTop = jQuery(this).offset().top;
         window.scrollTo(0, offTop - 120);
         }*/

        var objetifica = new Evento(this, (new Date()).getDay(), (jQuery(this).position().top), (jQuery(this).height()), jQuery(this).position().left, (jQuery(this).width()), cod);

        listaTodosEventos[diaEvento].push(objetifica);

        var conflitos = [];
        if (listaTodosEventos[diaEvento].length > 1) {
            jQuery.each(listaTodosEventos[diaEvento], function(key, val) {
                if (val.cod != objetifica.cod) {
                    if ((val.topo <= objetifica.topo && objetifica.topo < (val.topo + val.altura))
                            || (val.topo <= (objetifica.topo + objetifica.altura) && (val.topo + val.altura) > (objetifica.topo + objetifica.altura))) {
                        if (jQuery(val.objeto).hasClass("processado")) {
                            conflitos.push(val);
                        }
                    }
                }
            });
        }

        jQuery.each(conflitos, function(key, val) {
            jQuery(val.objeto).css({width: (val.largura / 2) + "px"});
            var maisleft = val.largura / 2;
            jQuery(objetifica.objeto).css({left: ((objetifica.esquerda + maisleft) + 2) + "px", width: (objetifica.largura / 2) + "px"});
        });

        jQuery(this).addClass("processado");
    });

    // Dia
    jQuery(".fc-view-agendaDay div.fc-event:not(.disponibilidade)").each(function() {
        var cod = jQuery(this).attr("class");
        cod = cod.split("cod");
        cod = cod[1];
        cod = cod.split(" ");
        cod = cod[0];

        var contaTotalEventos = jQuery(".fc-view-agendaDay div.fc-event:not(.disponibilidade)");
        contaTotalEventos = contaTotalEventos.length;

        var larguraDias = jQuery(".fc-slot0 .ui-widget-content").width();

        //jQuery(this).css({width: larguraDias + "px", left: 70 + "px"});

        var objetifica = new Evento(this, (new Date()).getDay(), (jQuery(this).position().top), (jQuery(this).height()), jQuery(this).position().left, (jQuery(this).width()), cod);

        listaTodosEventos.push(objetifica);
        console.log("lista: " + listaTodosEventos.length + ", contaTotal: " + contaTotalEventos);
        if (listaTodosEventos.length == contaTotalEventos) {
            console.log("começou!");
            for (var z = 0;z<listaTodosEventos.length;z++) {
                console.log("avaliando evento: "+z);
                var evento = listaTodosEventos[z];
                var contaConflitos = 0;
                for(var y = 0;y<listaTodosEventos.length;y++) {
                    var teste = listaTodosEventos[y];
                    if (teste.mesmoIntervalo(evento)) {
                        contaConflitos++;
                        console.log("conflito");
                    }
                }
                if (contaConflitos > visaoDiaColunas) {
                    visaoDiaColunas = contaConflitos;
                }
            }
            var colunasEventos = [];
            for (var x = 0;x<listaTodosEventos.length;x++){
                var eventoA = listaTodosEventos[x];
                if (colunasEventos.length < 1) {
                    colunasEventos[0] = [];
                    colunasEventos[0].push(eventoA);
                    continue;
                } else {
                    var addColuna = 1;
                    for(var w = 0;w<colunasEventos.length;w++){
                        for(var v = 0;v<colunasEventos[w];v++) {
                            if (colunasEventos[w][v].mesmoIntervalo(eventoA)){
                                addColuna = w+1;
                            }
                        }
                    }
                    if (typeof (colunasEventos[addColuna]) == "undefined") {
                        colunasEventos[addColuna] = []
                    }
                    colunasEventos[addColuna].push(eventoA);
                }
            }

            var larguraColuna = (larguraDias/(colunasEventos.length - 1)) - 2;

            for(var u = 0;u<colunasEventos.length;u++){
                for (var t = 0;t<colunasEventos[u].length;t++){
                    jQuery(colunasEventos[u][t]).css({left:(2+(larguraColuna*u))+"px",width: larguraColuna+"px"});
                }
            }
        }

//        jQuery.each(conflitos, function(key, val) {
//            jQuery(val.objeto).css({width: (val.largura / 2) + "px"});
//            var maisleft = val.largura / 2;
//            jQuery(objetifica.objeto).css({left: ((objetifica.esquerda + maisleft) + 2) + "px", width: (objetifica.largura / 2) + "px"});
//        });
//
//        jQuery(this).addClass("processado");
    });

}
// Toggle BarraLateral
function agendaFull() {
    if (barraLateral) {
        jQuery(".menuLateral").empty();
        jQuery(barraLateral).prependTo(".menuLateral");
        jQuery(".menuLateral").addClass("span3");
        jQuery(".caixaPrincipal").addClass("span9");
        jQuery(".menuLateral, .caixaPrincipal").removeAttr("style")/*.reSchedule()*/;
        barraLateral = null;
    } else {
        barraLateral = jQuery(".contemMenuLateral").detach();
        jQuery(".menuLateral").removeClass("span3");
        jQuery(".caixaPrincipal").removeClass("span9");
        jQuery(".caixaPrincipal").css({width: "86%", height: "auto", display: "inline-block", verticalAlign: "top"});
        jQuery(".menuLateral").css({width: "5%", height: "600px", display: "inline-block", verticalAlign: "top", border: "none",
            backgroundColor: "transparent", padding: "15px 10px"})/*.reSchedule()*/;
        jQuery(".menuLateral").html("<i class='fa-icon-chevron-right' style='font-size: 40px;cursor: pointer' onclick='agendaFull();'/>");
    }
    //myschedule.resize();
}

// Rerender agenda
(function($) {
    $.fn.reSchedule = function() {
        reprocessarAgenda();
        return this;
    };
})(jQuery);

function girarModal() {
    jQuery(".frenteEditarEvento").toggleClass("girado");
    jQuery(".atrasEditarEvento").toggleClass("girado");
}

function mostrarDatepick() {
    var item = jQuery('.pickDataCampo');
    item.css('display', 'block').children('input').animate({opacity: 1}, 250, function() {
        item.children('input').focus();
    });
    item.children('input').on("blur", function() {
        item.children('input').animate({opacity: 0}, 250, function() {
            item.css('display', 'none');
        });
    });
}
//Objeto Evento
function Evento(objt, diaEvento, topo, altura, esquerda, largura, cod) {
    this.objeto = objt;
    this.dia = diaEvento;
    this.topo = topo;
    this.altura = altura;
    this.esquerda = esquerda;
    this.largura = largura;
    this.cod = cod;
}
Evento.prototype.mesmoInicio = function(eventoB) {
    return (this.topo === eventoB.topo);
};
Evento.prototype.mesmoIntervalo = function(eventoB) {
    var rodape = this.topo + this.altura;
    var rodapeB = eventoB.topo + eventoB.altura;

    if (this.topo >= eventoB.topo && rodape <= rodapeB) {
        return true;
    } else if (this.topo < eventoB.topo && this.topo < rodapeB && rodape >= rodapeB) {
        return true;
    } else if (this.topo > eventoB.topo && eventoB.topo < rodape && rodape <= rodapeB) {
        return true;
    } else {
        return false;
    }

};
/* Overrides Primefaces */
PrimeFaces.widget.Dialog.prototype.applyFocus = function() {
    var firstInput = this.jq.find(':not(:submit):not(:button):input:visible:enabled:first');
    if (!firstInput.hasClass('hasDatepicker')) {
        //firstInput.focus();
    }
}

function scrollToFirst() {
    var minTop = 0;
    jQuery(".fc-event-inner .fc-event-skin").each(function() {
        var offTop = jQuery(this).offset().top;
        if (offTop > 0 && minTop === 0) {
            minTop = offTop;
        } else if (offTop > 0 && offTop < minTop) {
            minTop = offTop;
        }
    });
    if (minTop > 0) {
        window.scrollTo(0, minTop - 120);
//        console.log("OFF TOP -> " + minTop);
        return false;
    }
}