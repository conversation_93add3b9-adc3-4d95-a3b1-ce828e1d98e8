/* line 7, ../sass/lightbox.sass */
body:after {
    /*content: url(../img/close.png) url(../img/loading.gif) url(../img/prev.png) url(../img/next.png);*/
    display: none;
}

/* line 11, ../sass/lightbox.sass */
.lightboxOverlay {
    position: absolute;
    top: -105px;
    left: 0;
    bottom: 0;
    z-index: 9999;
    background-color: black;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
    opacity: 0.8;
    display: none;
}

/* line 20, ../sass/lightbox.sass */
.lightbox {
    position: absolute;
    left: 0;
    width: 100%;
    z-index: 10000;
    text-align: center;
    line-height: 0;
    font-weight: normal;
}

/* line 28, ../sass/lightbox.sass */
.lightbox .lb-image {
    display: block;
    height: auto;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    border-radius: 3px;
    max-width: none !important;
}

/* line 32, ../sass/lightbox.sass */
.lightbox a img {
    border: none;
}

/* line 35, ../sass/lightbox.sass */
.lb-outerContainer {
    position: relative;
    background-color: white;
    *zoom: 1;
    width: 250px;
    height: 250px;
    margin: 0 auto;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
}

/* line 38, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/utilities/general/_clearfix.scss */
.lb-outerContainer:after {
    content: "";
    display: table;
    clear: both;
}

/* line 44, ../sass/lightbox.sass */
.lb-container {
    padding: 4px;
}

/* line 47, ../sass/lightbox.sass */
.lb-loader {
    position: absolute;
    top: 43%;
    left: 0%;
    height: 25%;
    width: 100%;
    text-align: center;
    line-height: 0;
}

/* line 56, ../sass/lightbox.sass */
.lb-cancel {
    display: block;
    width: 32px;
    height: 32px;
    margin: 0 auto;
    /*background: url(../img/loading.gif) no-repeat;*/
}

/* line 63, ../sass/lightbox.sass */
.lb-nav {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 10;
}

/* line 71, ../sass/lightbox.sass */
.lb-container > .nav {
    left: 0;
}

/* line 74, ../sass/lightbox.sass */
.lb-nav a {
    outline: none;
}

/* line 77, ../sass/lightbox.sass */
.lb-prev, .lb-next {
    width: 49%;
    height: 100%;
    cursor: pointer;
    /* Trick IE into showing hover */
    display: block;
}

/* line 84, ../sass/lightbox.sass */
.lb-prev {
    left: 0;
    float: left;
    height: 100%;
    line-height: 100%;
    font-size: 72px;
    text-align: left;
    padding: 45% 0 0 15px;
    color: rgba(0,0,0,.8);
}

/* line 87, ../sass/lightbox.sass */
.lb-prev:hover {
    text-decoration: none;
    color: rgba(0,0,0,.6);
    text-shadow: 0 0 5px #000;
    /*background: url(../img/prev.png) left 48% no-repeat;*/
}

/* line 90, ../sass/lightbox.sass */
.lb-next {
    right: 0;
    float: right;
    height: 100%;
    line-height: 100%;
    font-size: 72px;
    text-align: right;
    padding: 45% 15px 0 0;
    color: rgba(0,0,0,.8);
}

/* line 93, ../sass/lightbox.sass */
.lb-next:hover {
    text-decoration: none;
    color: rgba(0,0,0,.6);
    text-shadow: 0 0 5px #000;
    /*background: url(../img/next.png) right 48% no-repeat;*/
}

/* line 96, ../sass/lightbox.sass */
.lb-dataContainer {
    margin: 0 auto;
    padding-top: 5px;
    *zoom: 1;
    width: 100%;
    -moz-border-radius-bottomleft: 4px;
    -webkit-border-bottom-left-radius: 4px;
    border-bottom-left-radius: 4px;
    -moz-border-radius-bottomright: 4px;
    -webkit-border-bottom-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

/* line 38, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/utilities/general/_clearfix.scss */
.lb-dataContainer:after {
    content: "";
    display: table;
    clear: both;
}

/* line 103, ../sass/lightbox.sass */
.lb-data {
    padding: 0 4px;
    color: #bbbbbb;
}

/* line 106, ../sass/lightbox.sass */
.lb-data .lb-details {
    width: 85%;
    float: left;
    text-align: left;
    line-height: 1.1em;
}

/* line 111, ../sass/lightbox.sass */
.lb-data .lb-caption {
    font-size: 13px;
    font-weight: bold;
    line-height: 1em;
}

/* line 115, ../sass/lightbox.sass */
.lb-data .lb-number {
    display: block;
    clear: left;
    padding-bottom: 1em;
    font-size: 12px;
    color: #999999;
}

/* line 121, ../sass/lightbox.sass */
.lb-data .lb-close {
    display: block;
    float: right;
    width: 30px;
    height: 30px;
    /*background: url(../img/close.png) top right no-repeat;*/
    text-align: right;
    outline: none;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=70);
    opacity: 0.7;
    color: #999999;
}

/* line 130, ../sass/lightbox.sass */
.lb-data .lb-close:hover {
    cursor: pointer;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
    opacity: 1;
    text-decoration: none;
}
