/****** <PERSON><PERSON>s *********/
@font-face {
    font-family:'Oxygen';
    src:url(#{resource['font/OxygenRegular.ttf']});
}
@font-face {
    font-family:'Oxygen Mono';
    src:url(#{resource['font/OxygenMono.ttf']});
}
* {
    font-family: "Oxygen";
}
/***  Fix Navegadores CSS ***/
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
/* Geral */
html {
    width: 100%;
    height: 100%;
}
body {
    background-color: #f0eded;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    margin: 0;
    padding: 0;
    background-image: url(#{resource['imagens/fundo_add.png']});
background-repeat: repeat-x;
background-position: center center;
}
a:active,
span[class*="btn"]:active {
    opacity: 0.75 !important;
}
.topo {
    background-color: #f0eded;
    position: absolute;
    top: 0;
    left: 20px;
    right: 20px;
    border-bottom: 1px solid #9b9b9b;
    box-shadow: 0 1px 0 0 #fff;
    padding: 20px 0;
}
.topo img:not(:first-child) {
    float: right;
}
.passos {
    transition-property: width, height, top, right;
    transition-duration: 1s;
}
/* z index fix passos */
.passoA.passo1 {
    z-index: 10;
}
.passoA.passo2 {
    z-index: 9;
}
.passoA.passo3 {
    z-index: 8;
}
.passoA.passo4 {
    z-index: 7;
}
.passoA.passo5 {
    z-index: 6;
}
.passoC.passo1 {
    z-index: 6;
}
.passoC.passo2 {
    z-index: 7;
}
.passoC.passo3 {
    z-index: 8;
}
.passoC.passo4 {
    z-index: 9;
}
.passoC.passo5 {
    z-index: 10;
}
.passoPequeno {
    width: 89px;
    height: 112px;
    border: solid 1px #9b9b9b;
    border-radius: 6px;
    background-color: white;
    padding-top: 20px;
    overflow: hidden;
}
.passoGrande {
    width: 329px;
    height: 462px;
    border: solid 1px #9b9b9b;
    border-radius: 6px;
    background-color: white;
    padding-top: 20px;
    overflow: visible;
}

.passoListaClientes {
    width: 440px !important;
}

.passoGrande .numero {
    display: none;
}
.passoPequeno .numero {
    display: block;
    font-family: "Oxygen";
    font-size: 72px;
    color: #363636;
    width: 100%;
    background-color: #f1eded;
    text-align: center;
    margin: 0;
    padding: 0;
}
.passoPequeno .conteudo {
    display: none;
}
.conteudo {
    overflow: hidden;
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
.passoPequeno .fotoAlunoTopoM {
    opacity: 0 !important;
}
.fotoAlunoTopoM {
    z-index: 3;
    position: absolute;
    top: -76px;
    left: 25%;
    border-radius: 86px;
    border: 10px solid #f0eded;
    width: 157px;
    height: 153px;
    opacity: 0;
    /*transition: opacity 0.25s;*/
}
.passo1.passoGrande .fotoAlunoTopoM {
    opacity: 1;
}
.conteudo .fotoAlunoTopoW {
    z-index: 1;
    position: absolute;
    top: -96px;
    left: calc(25% - 20px);
    border-radius: 106px;
    border: 30px solid #fff;
}
.conteudo .fotoAlunoTopo {
    z-index: 2;
    position: absolute;
    top: -76px;
    left: 25%;
    border-radius: 86px;
    border: 10px solid #f0eded;
    box-shadow: 0 0 0 1px #9b9b9b;
}
.conteudo .conteudoMain {
    margin: 20px 0 0 0;
    display: block;
    font-family: "Oxygen";
    color: #363636;
    width: 100%;
    background-color: #f1eded;
    text-align: center;
    position: absolute;
    padding-top: 110px;
    top: 0;
    bottom: 20px;
}
.conteudo .conteudoMain .inputBusca {
    width: 70%;
    border: 1px solid #9b9b9b;
    border-radius: 2px;
    padding: 8px 15px;
    color: #9b9b9b;
    background-color: #f1eded;
    margin: 4px 0;
    box-shadow: none;
    background-image: none;
    font-size: 12px;
}
.conteudo .conteudoMain .btn-buscar {
    display: block;
    margin: 0 auto;
    margin-top: 5px;
    width: 70%;
    padding: 5px;
    border-radius: 2px;
    text-align: center;
    color: white;
    background-color: #9b9b9b;
    font-size: 24px;
    text-decoration: none;
    font-weight: normal;
    cursor: pointer;
}
.conteudo .conteudoMain .btn-buscar:hover {
    background-color: #363636;
}
.passoA {
    position: absolute;
    right: 20px;
    top: calc(50% - 66px);
}
.passoB {
    position: absolute;
    right: calc(50% - 164px);
    top: calc(50% - 231px);
    z-index: 15;
}
.passoC {
    position: absolute;
    right: calc(100% - 109px);
    top: calc(50% - 66px);
}
.passoC.passo1.stack2 {
    right: calc(100% - 99px);
    top: calc(50% - 96px);
}
.passoC.passo2.stack2 {
    right: calc(100% - 129px);
    top: calc(50% - 36px);
}
.passo2 {
    overflow: hidden;
}
.tabelaAlunos thead {
    display: none !important;
}
.tabelaAlunos tbody ,
.tabelaAlunos tbody tr,
.tabelaAlunos tbody tr td {
    margin: 0;
    border: 0;
}
.tabelaAlunos tbody tr:nth-child(odd) {
    background-color: #dfdfdf;
}
.tabelaAlunos tbody tr:nth-child(even) {
    background-color: #efefef;
}
.tabelaAlunos tbody tr:hover {
    background-color: #d0d0d0;
}
.tabelaAlunos tbody tr td a {
    width: 100%;
    height: 40px;
    text-decoration: none !important;
    color: #363636;
    display: block;
    line-height: 40px;
}
.passo3,
.QRContem {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -o-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition: width 1s, height 1s, top 1s, right 1s,-webkit-transform .5s;
    -moz-transition: width 1s, height 1s, top 1s, right 1s,-moz-transform .5s;
    -o-transition: width 1s, height 1s, top 1s, right 1s,-o-transform .5s;
    transition:width 1s, height 1s, top 1s, right 1s, transform .5s;
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -o-transform-style: preserve-3d;
    transform-style: preserve-3d;
}
.passo3.girado,
.QRContem.girado {
    -webkit-transform: perspective(600px) rotateY( 180deg );
    -moz-transform: perspective(600px) rotateY( 180deg );
    -o-transform: perspective(600px) rotateY( 180deg );
    transform: perspective(600px) rotateY( 180deg );
}
.conteudo .conteudoMain .inputSelectProf {
    width: 80%;
    border: 1px solid #9B9B9B;
    margin-top: 15px;
    width: 262px;
}

.ui-selectonemenu-list-item {
    font-size: 11px;
}
.ui-selectonemenu-label {
    font-size: 11px;
    margin: 0 0 0 0 !important;
}
.ui-selectonemenu-items-wrapper {
    width: 262px;
}

.inputSelectProf .ui-selectonemenu-trigger {
    width: 16px;
    height: 32px !important;
    padding: 3px 3px !important;
    background: none #9b9b9b;
    border-radius: 0;
    margin: 0 0 0 0;
}
.inputSelectProf .ui-selectonemenu-trigger:hover {
    background-color: #aaa;
}
.inputSelectProf .ui-selectonemenu-label {
    display: block;
    border: medium none;
    white-space: nowrap;
    overflow: hidden;
    font-weight: normal;
    width: 100%;
    float: left;
    background-color: #F1EDED;
    font-size: 14px;
    text-shadow: none;
    color: #9b9b9b;
    padding: 4px 0 4px 20px;
    text-align: left;
}
/* Bendito Chrome! */
@media all and (-webkit-min-device-pixel-ratio:0) and (min-resolution: .001dpcm) {
    .inputSelectProf .ui-selectonemenu-label {
        padding: 6px 6px 6px 20px;
    }
}
.passo3 .botaoQR {
    padding: 0 8px;
    border-radius: 2px;
    background-color: #dbdbdb;
    display: inline-block;
    margin: 5px 3px;
}
.QRContem .conteudoMain {
    padding: 20px 0 0 0;
}
.QRContem .conteudoMain img {
    width: 90%;
    margin: 0 auto;
}
.QRContem .conteudoMain a {
    font-size: 22px;
    cursor: pointer;
}
@media screen and (max-height: 900px) {
    body {
        background-position: left 58%;
        overflow-y: hidden;
        bottom: -100px;
    }
    .passoGrande {
        height: 50%;
    }
    .passoB {
        top: 25%;
    }
}

@media screen and (orientation: landscape) and (max-height: 600px) {
    body {
        background-position: left 69%;
        overflow-y: hidden;
        bottom: -200px;
    }
}