.ui-accordion-content.ui-helper-reset.ui-widget-content{
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}
.ui-accordion-content.ui-helper-reset.ui-widget-content.ui-helper-hidden{
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}
.menuLateral .semHeaderColunas table tbody tr td a{
    padding: 10px 10px 10px 10px;
    text-align: left; 
}
.displayNone{
    display: none !important;

}
.displayBlock{
    display: block !important;

}
/* Largura da barra de rolagem */
.painelListaLateral::-webkit-scrollbar {
    width: 10px;
}

/* Fundo da barra de rolagem */
.painelListaLateral::-webkit-scrollbar-track-piece {
    background-color: #EEE;
    border-left: 1px solid #CCC
}

/* Cor do indicador de rolagem */
.painelListaLateral::-webkit-scrollbar-thumb:vertical,
.painelListaLateral::-webkit-scrollbar-thumb:horizontal {
    background-color: #797878
}

/* Cor do indicador de rolagem - ao passar o mouse */
.painelListaLateral::-webkit-scrollbar-thumb:vertical:hover,
.painelListaLateral::-webkit-scrollbar-thumb:horizontal:hover {
    background-color: #797878
}

.internoMenuLateral > select,
.internoMenuLateral > span,
.internoMenuLateral > input {
    margin: 10px 19px;
    width: calc(100% - 52px);
    border: 1px solid #ccc;
}
.btn-preto{
    background-color: #333 !important;
}
.margin-20{
    margin: 14px 10px 1px 1px !important; 
}
.complemento{ 
    text-transform: uppercase !important;
}
.complemento::-webkit-input-placeholder { /* WebKit browsers */
    text-transform: none;
}
.complemento:-moz-placeholder{ /* Mozilla Firefox 4 to 18 */
    text-transform: none;
}
.complemento::-moz-placeholder{ /* Mozilla Firefox 19+ */
    text-transform: none;
}
.complemento:-ms-input-placeholder{ /* Internet Explorer 10+ */
    text-transform: none;
}

.tituloPreto{
    font-weight: bold; 
    font-size: 1.14em; 
}
.rodapeBotoes{
    float: right; 
    width: 100%;
}
.margin-right-zero{
    margin-right: 0px !important;
}

input[id$="popupButtonDataNascimento_input"]{
    width: calc(38% - 14px); 
    margin-right: 2%; 
    float: left;
}
.painelFotoAluno{
    width: 15%; 
    float: left;
    margin-right: 2%;
}
.painelMaiorEsquerda{
    width: 83%; 
    float: left; 
}
.margin-left-dez{
    margin-left: 10px;
}
.bottomCadastro{
    margin-bottom: 20px !important;
}
.topCadastro{
    margin-top: 20px !important;
}
.painelInferior{
    float: left; 
    width: 100%; 
    margin-top: 20px; 
    min-height: 115px;
}

.ui-chkbox .ui-chkbox-box{
    float: left;
}

.labelForm{
    float: left; 
    font-weight: bold; 
    margin-top: 5px;
}
.comboAuto{
    width: auto;
}

.floatEsquerda{
    float: left;
}
.noMargin{
    margin: 0 !important;
}