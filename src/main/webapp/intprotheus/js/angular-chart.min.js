/*!
 * angular-chart.js - An angular.js wrapper for Chart.js
 * http://jtblin.github.io/angular-chart.js/
 * Version: 1.0.0-beta1
 *
 * Copyright 2016 <PERSON>
 * Released under the BSD-2-Clause license
 * https://github.com/jtblin/angular-chart.js/blob/master/LICENSE
 */
!function(t){"use strict";if("object"==typeof exports)module.exports=t("undefined"!=typeof angular?angular:require("angular"),"undefined"!=typeof Chart?Chart:require("chart.js"));else if("function"==typeof define&&define.amd)define(["angular","chart"],t);else{if("undefined"==typeof angular||"undefined"==typeof Chart)throw new Error("Chart.js library needs to included, see http://jtblin.github.io/angular-chart.js/");t(angular,Chart)}}(function(t,r){"use strict";function a(){var a={responsive:!0},e={Chart:r,getOptions:function(r){var e=r&&a[r]||{};return t.extend({},a,e)}};this.setOptions=function(r,e){return e?void(a[r]=t.extend(a[r]||{},e)):(e=r,void(a=t.extend(a,e)))},this.$get=function(){return e}}function e(a,e){function o(t,r,e){var n=w(t,r);if(p(r)&&$(t,r,e,n)){var o=e[0],c=o.getContext("2d");r.chartGetColor=C(r);var i=y(t,r);F(r),r.chart=new a.Chart(c,{type:t,data:i,options:n}),r.$emit("chart-create",r.chart),D(o,r)}}function c(t,r){return t&&r&&t.length&&r.length?Array.isArray(t[0])?t.length===r.length&&t.every(function(t,a){return t.length===r[a].length}):r.reduce(i,0)>0?t.length===r.length:!1:!1}function i(t,r){return t+r}function u(r,a,e){var n=null;return function(o){var c=r.chart.getElementsAtEvent||r.chart.getPointsAtEvent;if(c){var i=c.call(r.chart,o);e!==!1&&t.equals(n,i)!==!1||(n=i,r[a](i,o))}}}function h(e,n){for(var o=t.copy(n.chartColors||a.getOptions(e).chartColors||r.defaults.global.colors),c=o.length<n.chartData.length;o.length<n.chartData.length;)o.push(n.chartGetColor());return c&&(n.chartColors=o),o.map(l)}function l(t){return"object"==typeof t&&null!==t?t:"string"==typeof t&&"#"===t[0]?f(v(t.substr(1))):s()}function s(){var t=[d(0,255),d(0,255),d(0,255)];return f(t)}function f(t){return{backgroundColor:g(t,.2),pointBackgroundColor:g(t,1),pointHoverBackgroundColor:g(t,.8),borderColor:g(t,1),pointBorderColor:"#fff",pointHoverBorderColor:g(t,1)}}function d(t,r){return Math.floor(Math.random()*(r-t+1))+t}function g(t,r){return n?"rgb("+t.join(",")+")":"rgba("+t.concat(r).join(",")+")"}function v(t){var r=parseInt(t,16),a=r>>16&255,e=r>>8&255,n=255&r;return[a,e,n]}function p(t){return t.chartData&&t.chartData.length}function C(t){return"function"==typeof t.chartGetColor?t.chartGetColor:s}function y(t,r){var a=h(t,r);return Array.isArray(r.chartData[0])?b(r.chartLabels,r.chartData,r.chartSeries||[],a,r.chartDatasetOverride):m(r.chartLabels,r.chartData,a,r.chartDatasetOverride)}function b(r,a,e,n,o){return{labels:r,datasets:a.map(function(r,a){var c=t.extend({},n[a],{label:e[a],data:r});return o&&o.length>=a&&t.merge(c,o[a]),c})}}function m(r,a,e,n){var o={labels:r,datasets:[{data:a,backgroundColor:e.map(function(t){return t.pointBackgroundColor}),hoverBackgroundColor:e.map(function(t){return t.backgroundColor})}]};return n&&t.merge(o.datasets[0],n),o}function w(r,e){return t.extend({},a.getOptions(r),e.chartOptions)}function D(r,a){r.onclick=a.chartClick?u(a,"chartClick",!1):t.noop,r.onmousemove=a.chartHover?u(a,"chartHover",!0):t.noop}function B(t,r){Array.isArray(r.chartData[0])?r.chart.data.datasets.forEach(function(r,a){r.data=t[a]}):r.chart.data.datasets[0].data=t,r.chart.update(),r.$emit("chart-update",r.chart)}function A(t){return!t||Array.isArray(t)&&!t.length||"object"==typeof t&&!Object.keys(t).length}function $(t,r,a,n){return n.responsive&&0===a[0].clientHeight?(e(function(){o(t,r,a)},50,!1),!1):!0}function F(t){t.chart&&(t.chart.destroy(),t.$emit("chart-destroy",t.chart))}return function(r){return{restrict:"CA",scope:{chartGetColor:"=?",chartType:"=",chartData:"=?",chartLabels:"=?",chartOptions:"=?",chartSeries:"=?",chartColors:"=?",chartClick:"=?",chartHover:"=?",chartDatasetOverride:"=?"},link:function(a,e){function i(t,n){if(!t||!t.length||Array.isArray(t[0])&&!t[0].length)return void F(a);var i=r||a.chartType;if(i)return a.chart&&c(t,n)?B(t,a):void o(i,a,e)}function u(n,c){if(!A(n)&&!t.equals(n,c)){var i=r||a.chartType;i&&o(i,a,e)}}function h(r,n){A(r)||t.equals(r,n)||o(r,a,e)}n&&window.G_vmlCanvasManager.initElement(e[0]),a.$watch("chartData",i,!0),a.$watch("chartSeries",u,!0),a.$watch("chartLabels",u,!0),a.$watch("chartOptions",u,!0),a.$watch("chartColors",u,!0),a.$watch("chartDatasetOverride",u,!0),a.$watch("chartType",h,!1),a.$on("$destroy",function(){F(a)}),a.$on("$resize",function(){a.chart&&a.chart.resize()})}}}}r.defaults.global.multiTooltipTemplate="<%if (datasetLabel){%><%=datasetLabel%>: <%}%><%= value %>",r.defaults.global.tooltips.mode="label",r.defaults.global.elements.line.borderWidth=2,r.defaults.global.elements.rectangle.borderWidth=2,r.defaults.global.legend.display=!1,r.defaults.global.colors=["#97BBCD","#DCDCDC","#F7464A","#46BFBD","#FDB45C","#949FB1","#4D5360"];var n="object"==typeof window.G_vmlCanvasManager&&null!==window.G_vmlCanvasManager&&"function"==typeof window.G_vmlCanvasManager.initElement;return n&&(r.defaults.global.animation=!1),t.module("chart.js",[]).provider("ChartJs",a).factory("ChartJsFactory",["ChartJs","$timeout",e]).directive("chartBase",["ChartJsFactory",function(t){return new t}]).directive("chartLine",["ChartJsFactory",function(t){return new t("line")}]).directive("chartBar",["ChartJsFactory",function(t){return new t("bar")}]).directive("chartHorizontalBar",["ChartJsFactory",function(t){return new t("horizontalBar")}]).directive("chartRadar",["ChartJsFactory",function(t){return new t("radar")}]).directive("chartDoughnut",["ChartJsFactory",function(t){return new t("doughnut")}]).directive("chartPie",["ChartJsFactory",function(t){return new t("pie")}]).directive("chartPolarArea",["ChartJsFactory",function(t){return new t("polarArea")}]).directive("chartBubble",["ChartJsFactory",function(t){return new t("bubble")}]).name});
//# sourceMappingURL=angular-chart.min.js.map
