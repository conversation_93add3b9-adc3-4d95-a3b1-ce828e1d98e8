<!--
Thanks for wanting to report an issue you've found in angular-chart.js. 

Plese note that issues or feature requests for Chart.js (e.g. new chart type, new axis, etc.) 
need to be opened on Chart.js issues tracker: https://github.com/nnnick/Chart.js/issues.

For general questions about usage, please use [http://stackoverflow.com/](http://stackoverflow.com/)
as you will be more likely to get an appropriate answer.
 
Please check if the issue exists before creating a new one. 
While opening an issue please provide a jsbin template or equivalent 
to reproduce the issue. 

-->

### Overview

Describe the issue. What is the issue and what did you expect?

Please make sure to review and check all of these items:

- [ ] Use latest version of the library
- [ ] Make sure you've included all the dependencies e.g Chart.js, angular, css file
- [ ] Include a repro case, see below.


### Step to reproduce

**Ensure you add a link to a plunker, jsbin, or equivalent.** 
Here is a [jsbin template](http://jsbin.com/dufibi/3/edit?html,js,output) for convenience.


