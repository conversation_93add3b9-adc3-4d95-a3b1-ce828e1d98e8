<!-- Thanks for taking the time to submit a pull request for this project. Please ensure the following 
     before opening a pull request as best as you can. -->

### Description of change

<!-- Please provide a description of the change here. Indicate which issue it's referring to. -->

### Pull Request check-list

- [ ] Run `gulp test` to ensure there are no linting, or style issues and all tests pass.
- [ ] Squash your commits into a few commits only.
- [ ] Make sure the commit message is short, concise and descriptive of the issues you're fixing.
- [ ] Avoid mixing up multiple issues and/or features, open one pull request for each issue.
- [ ] Have you updated the documentation and / or examples?
- [ ] Have you included a new test?
