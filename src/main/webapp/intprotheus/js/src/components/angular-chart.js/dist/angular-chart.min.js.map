{"version": 3, "sources": ["angular-chart.min.js"], "names": ["factory", "exports", "module", "angular", "require", "Chart", "define", "amd", "ChartJsProvider", "options", "ChartJs", "getOptions", "type", "typeOptions", "extend", "this", "setOptions", "customOptions", "$get", "ChartJsFactory", "$timeout", "canUpdateChart", "newVal", "oldVal", "length", "Array", "isArray", "every", "element", "index", "reduce", "sum", "carry", "val", "getEventHandler", "scope", "chart", "action", "triggerOnlyOnChange", "lastState", "evt", "atEvent", "getPointsAtEvent", "getBarsAtEvent", "getSegmentsAtEvent", "activePoints", "call", "equals", "$apply", "getColours", "notEnoughColours", "colours", "copy", "defaults", "global", "data", "push", "getColour", "map", "convertColour", "colour", "hexToRgb", "substr", "getRandomColour", "getRandomInt", "fillColor", "rgba", "strokeColor", "pointColor", "pointStrokeColor", "pointHighlightFill", "pointHighlightStroke", "min", "max", "Math", "floor", "random", "alpha", "usingExcanvas", "join", "concat", "hex", "bigint", "parseInt", "r", "g", "b", "getDataSets", "labels", "series", "datasets", "item", "i", "label", "getData", "value", "color", "highlight", "setLegend", "elem", "$parent", "parent", "$oldLegend", "find", "legend", "generateLegend", "replaceWith", "append", "updateChart", "values", "for<PERSON>ach", "dataset", "points", "bars", "dataItem", "j", "segments", "segment", "update", "$emit", "isEmpty", "Object", "keys", "isResponsive", "responsive", "destroy<PERSON>hart", "destroy", "restrict", "chartType", "click", "hover", "chartData", "chartLabels", "chartOptions", "chartSeries", "chartColours", "chartLegend", "chartClick", "chartHover", "link", "<PERSON><PERSON><PERSON>", "fromName", "to<PERSON>ame", "$watch", "reset<PERSON><PERSON>", "createChart", "clientHeight", "container", "cvs", "ctx", "getContext", "onclick", "noop", "<PERSON><PERSON><PERSON><PERSON>", "deprecated", "attr", "console", "env", "warn", "log", "document", "createElement", "className", "append<PERSON><PERSON><PERSON>", "window", "G_vmlCanvasManager", "initElement", "$on", "multiTooltipTemplate", "animation", "provider", "directive"], "mappings": "CAAC,SAAUA,GACT,YACuB,iBAAZC,SAETC,OAAOD,QAAUD,EACI,mBAAZG,SAA0BA,QAAUC,QAAQ,WAClC,mBAAVC,OAAwBA,MAAQD,QAAQ,aACrB,kBAAXE,SAAyBA,OAAOC,IAEjDD,QAAQ,UAAW,SAAUN,GAG7BA,EAAQG,QAASE,QAEnB,SAAUF,EAASE,GACnB,YAyCA,SAASG,KACP,GAAIC,MACAC,GACFL,MAAOA,EACPM,WAAY,SAAUC,GACpB,GAAIC,GAAcD,GAAQH,EAAQG,MAClC,OAAOT,GAAQW,UAAWL,EAASI,IAOvCE,MAAKC,WAAa,SAAUJ,EAAMK,GAEhC,MAAMA,QAMNR,EAAQG,GAAQT,EAAQW,OAAOL,EAAQG,OAAaK,KALlDA,EAAgBL,OAChBH,EAAUN,EAAQW,OAAOL,EAASQ,MAOtCF,KAAKG,KAAO,WACV,MAAOR,IAIX,QAASS,GAAgBT,EAASU,GAsIhC,QAASC,GAAgBC,EAAQC,GAC/B,MAAID,IAAUC,GAAUD,EAAOE,QAAUD,EAAOC,OACvCC,MAAMC,QAAQJ,EAAO,IAC5BA,EAAOE,SAAWD,EAAOC,QAAUF,EAAOK,MAAM,SAAUC,EAASC,GACjE,MAAOD,GAAQJ,SAAWD,EAAOM,GAAOL,SACxCD,EAAOO,OAAOC,EAAK,GAAK,EAAIT,EAAOE,SAAWD,EAAOC,QAAS,GAE3D,EAGT,QAASO,GAAKC,EAAOC,GACnB,MAAOD,GAAQC,EAGjB,QAASC,GAAiBC,EAAOC,EAAOC,EAAQC,GAC9C,GAAIC,GAAY,IAChB,OAAO,UAAUC,GACf,GAAIC,GAAUL,EAAMM,kBAAoBN,EAAMO,gBAAkBP,EAAMQ,kBACtE,IAAIH,EAAS,CACX,GAAII,GAAeJ,EAAQK,KAAKV,EAAOI,EACnCF,MAAwB,GAASnC,EAAQ4C,OAAOR,EAAWM,MAAkB,IAC/EN,EAAYM,EACZV,EAAME,GAAQQ,EAAcL,GAC5BL,EAAMa,YAMd,QAASC,GAAYrC,EAAMuB,GAMzB,IALA,GAAIe,IAAmB,EACnBC,EAAUhD,EAAQiD,KAAKjB,EAAMgB,SAC/BzC,EAAQC,WAAWC,GAAMuC,SACzB9C,EAAMgD,SAASC,OAAOH,SAEjBA,EAAQ3B,OAASW,EAAMoB,KAAK/B,QACjC2B,EAAQK,KAAKrB,EAAMsB,aACnBP,GAAmB,CAKrB,OADIA,KAAkBf,EAAMgB,QAAUA,GAC/BA,EAAQO,IAAIC,GAGrB,QAASA,GAAeC,GACtB,MAAsB,gBAAXA,IAAkC,OAAXA,EAAwBA,EACpC,gBAAXA,IAAqC,MAAdA,EAAO,GAAmBH,EAAUI,EAASD,EAAOE,OAAO,KACtFC,IAGT,QAASA,KACP,GAAIH,IAAUI,EAAa,EAAG,KAAMA,EAAa,EAAG,KAAMA,EAAa,EAAG,KAC1E,OAAOP,GAAUG,GAGnB,QAASH,GAAWG,GAClB,OACEK,UAAWC,EAAKN,EAAQ,IACxBO,YAAaD,EAAKN,EAAQ,GAC1BQ,WAAYF,EAAKN,EAAQ,GACzBS,iBAAkB,OAClBC,mBAAoB,OACpBC,qBAAsBL,EAAKN,EAAQ,KAIvC,QAASI,GAAcQ,EAAKC,GAC1B,MAAOC,MAAKC,MAAMD,KAAKE,UAAYH,EAAMD,EAAM,IAAMA,EAGvD,QAASN,GAAMN,EAAQiB,GACrB,MAAIC,GAEK,OAASlB,EAAOmB,KAAK,KAAO,IAE5B,QAAUnB,EAAOoB,OAAOH,GAAOE,KAAK,KAAO,IAKtD,QAASlB,GAAUoB,GACjB,GAAIC,GAASC,SAASF,EAAK,IACzBG,EAAKF,GAAU,GAAM,IACrBG,EAAKH,GAAU,EAAK,IACpBI,EAAa,IAATJ,CAEN,QAAQE,EAAGC,EAAGC,GAGhB,QAASC,GAAaC,EAAQjC,EAAMkC,EAAQtC,GAC1C,OACEqC,OAAQA,EACRE,SAAUnC,EAAKG,IAAI,SAAUiC,EAAMC,GACjC,MAAOzF,GAAQW,UAAWqC,EAAQyC,IAChCC,MAAOJ,EAAOG,GACdrC,KAAMoC,OAMd,QAASG,GAASN,EAAQjC,EAAMJ,GAC9B,MAAOqC,GAAO9B,IAAI,SAAUmC,EAAOD,GACjC,MAAOzF,GAAQW,UAAWqC,EAAQyC,IAChCC,MAAOA,EACPE,MAAOxC,EAAKqC,GACZI,MAAO7C,EAAQyC,GAAGzB,YAClB8B,UAAW9C,EAAQyC,GAAGrB,yBAK5B,QAAS2B,GAAWC,EAAM/D,GACxB,GAAIgE,GAAUD,EAAKE,SACfC,EAAaF,EAAQG,KAAK,gBAC1BC,EAAS,iBAAmBpE,EAAMqE,iBAAmB,iBACrDH,GAAW9E,OAAQ8E,EAAWI,YAAYF,GACzCJ,EAAQO,OAAOH,GAGtB,QAASI,GAAaxE,EAAOyE,EAAQ1E,EAAOgE,GACtC1E,MAAMC,QAAQS,EAAMoB,KAAK,IAC3BnB,EAAMsD,SAASoB,QAAQ,SAAUC,EAASnB,IACvCmB,EAAQC,QAAUD,EAAQE,MAAMH,QAAQ,SAAUI,EAAUC,GAC3DD,EAASnB,MAAQc,EAAOjB,GAAGuB,OAI/B/E,EAAMgF,SAASN,QAAQ,SAAUO,EAASzB,GACxCyB,EAAQtB,MAAQc,EAAOjB,KAG3BxD,EAAMkF,SACNnF,EAAMoF,MAAM,SAAUnF,GAClBD,EAAMqE,QAA2B,UAAjBrE,EAAMqE,QAAoBN,EAAUC,EAAM/D,GAGhE,QAASoF,GAASzB,GAChB,OAASA,GACNtE,MAAMC,QAAQqE,KAAYA,EAAMvE,QACf,gBAAVuE,KAAwB0B,OAAOC,KAAK3B,GAAOvE,OAGvD,QAASmG,GAAc/G,EAAMuB,GAC3B,GAAI1B,GAAUN,EAAQW,UAAWT,EAAMgD,SAASC,OAAQ5C,EAAQC,WAAWC,GAAOuB,EAAM1B,QACxF,OAAOA,GAAQmH,WAGjB,QAASC,GAAazF,EAAOD,GACtBC,IACLA,EAAM0F,UACN3F,EAAMoF,MAAM,UAAWnF,IA7RzB,MAAO,UAAgBxB,GACrB,OACEmH,SAAU,KACV5F,OACEoB,KAAM,KACNiC,OAAQ,KACR/E,QAAS,KACTgF,OAAQ,KACRtC,QAAS,KACTM,UAAW,KACXuE,UAAW,IACXxB,OAAQ,IACRyB,MAAO,KACPC,MAAO,KAEPC,UAAW,KACXC,YAAa,KACbC,aAAc,KACdC,YAAa,KACbC,aAAc,KACdC,YAAa,IACbC,WAAY,KACZC,WAAY,MAEdC,KAAM,SAAUxG,EAAOgE,GASrB,QAASyC,GAAUC,EAAUC,GAC3B3G,EAAM4G,OAAOF,EAAU,SAAUvH,GACT,mBAAXA,KACXa,EAAM2G,GAAUxH,KA6CpB,QAAS0H,GAAY1H,EAAQC,GAC3B,IAAIiG,EAAQlG,KACRnB,EAAQ4C,OAAOzB,EAAQC,GAA3B,CACA,GAAIyG,GAAYpH,GAAQuB,EAAM6F,SACxBA,IAINiB,EAAYjB,IAGd,QAASiB,GAAarI,GACpB,GAAI+G,EAAa/G,EAAMuB,IAAmC,IAAzBgE,EAAK,GAAG+C,cAAiD,IAA3BC,EAAUD,aACvE,MAAO9H,GAAS,WACd6H,EAAYrI,IACX,IAAI,EAET,IAAMuB,EAAMoB,MAAUpB,EAAMoB,KAAK/B,OAAjC,CACAW,EAAMsB,UAAuC,kBAApBtB,GAAMsB,UAA2BtB,EAAMsB,UAAYM,CAC5E,IAAIZ,GAAUF,EAAWrC,EAAMuB,GAC3BiH,EAAMjD,EAAK,GAAIkD,EAAMD,EAAIE,WAAW,MACpC/F,EAAO9B,MAAMC,QAAQS,EAAMoB,KAAK,IAClCgC,EAAYpD,EAAMqD,OAAQrD,EAAMoB,KAAMpB,EAAMsD,WAActC,GAC1D2C,EAAQ3D,EAAMqD,OAAQrD,EAAMoB,KAAMJ,GAChC1C,EAAUN,EAAQW,UAAWJ,EAAQC,WAAWC,GAAOuB,EAAM1B,QAIjEoH,GAAazF,EAAOD,GACpBC,EAAQ,GAAI1B,GAAQL,MAAMgJ,GAAKzI,GAAM2C,EAAM9C,GAC3C0B,EAAMoF,MAAM,SAAUnF,GAGtBgH,EAAIG,QAAUpH,EAAM8F,MAAQ/F,EAAgBC,EAAOC,EAAO,SAAS,GAASjC,EAAQqJ,KACpFJ,EAAIK,YAActH,EAAM+F,MAAQhG,EAAgBC,EAAOC,EAAO,SAAS,GAAQjC,EAAQqJ,KAEnFrH,EAAMqE,QAA2B,UAAjBrE,EAAMqE,QAAoBN,EAAUC,EAAM/D,IAGhE,QAASsH,GAAYC,GACnB,GAAuB,mBAAZC,UAAwD,SAA7BlJ,EAAQC,aAAakJ,IAAgB,CACzE,GAAIC,GAA+B,kBAAjBF,SAAQE,KAAsBF,QAAQE,KAAOF,QAAQG,GAChE5H,GAAMwH,IACXG,EAAKhH,KAAK8G,QAAS,6FACiBD,EAAMA,IApGhD,GAAIvH,GAAO+G,EAAYa,SAASC,cAAc,MAC9Cd,GAAUe,UAAY,kBACtB/D,EAAKO,YAAYyC,GACjBA,EAAUgB,YAAYhE,EAAK,IAEvBrB,GAAesF,OAAOC,mBAAmBC,YAAYnE,EAAK,KAE7D,OAAQ,SAAU,UAAW,SAAU,UAAW,SAAU,QAAS,SAASW,QAAQ4C,GASvFd,EAAS,YAAa,QACtBA,EAAS,cAAe,UACxBA,EAAS,eAAgB,WACzBA,EAAS,cAAe,UACxBA,EAAS,eAAgB,WACzBA,EAAS,cAAe,UACxBA,EAAS,aAAc,SACvBA,EAAS,aAAc,SAIvBzG,EAAM4G,OAAO,OAAQ,SAAUzH,EAAQC,GACrC,IAAMD,IAAYA,EAAOE,QAAWC,MAAMC,QAAQJ,EAAO,MAASA,EAAO,GAAGE,OAE1E,WADAqG,GAAazF,EAAOD,EAGtB,IAAI6F,GAAYpH,GAAQuB,EAAM6F,SAC9B,IAAMA,EAEN,MAAI5F,IAASf,EAAeC,EAAQC,GAC3BqF,EAAYxE,EAAOd,EAAQa,EAAOgE,OAE3C8C,GAAYjB,KACX,GAEH7F,EAAM4G,OAAO,SAAUC,GAAY,GACnC7G,EAAM4G,OAAO,SAAUC,GAAY,GACnC7G,EAAM4G,OAAO,UAAWC,GAAY,GACpC7G,EAAM4G,OAAO,UAAWC,GAAY,GAEpC7G,EAAM4G,OAAO,YAAa,SAAUzH,EAAQC,GACtCiG,EAAQlG,IACRnB,EAAQ4C,OAAOzB,EAAQC,IAC3B0H,EAAY3H,KAGda,EAAMoI,IAAI,WAAY,WACpB1C,EAAazF,EAAOD,QAnJ9B9B,EAAMgD,SAASC,OAAOsE,YAAa,EACnCvH,EAAMgD,SAASC,OAAOkH,qBAAuB,6DAE7CnK,EAAMgD,SAASC,OAAOH,SACpB,UACA,UACA,UACA,UACA,UACA,UACA,UAGF,IAAI2B,GAAqD,gBAA9BsF,QAAOC,oBACF,OAA9BD,OAAOC,oBAC0C,kBAA1CD,QAAOC,mBAAmBC,WAInC,OAFIxF,KAAezE,EAAMgD,SAASC,OAAOmH,WAAY,GAE9CtK,EAAQD,OAAO,eACnBwK,SAAS,UAAWlK,GACpBR,QAAQ,kBAAmB,UAAW,WAAYmB,IAClDwJ,UAAU,aAAc,iBAAkB,SAAUxJ,GAAkB,MAAO,IAAIA,MACjFwJ,UAAU,aAAc,iBAAkB,SAAUxJ,GAAkB,MAAO,IAAIA,GAAe,WAChGwJ,UAAU,YAAa,iBAAkB,SAAUxJ,GAAkB,MAAO,IAAIA,GAAe,UAC/FwJ,UAAU,cAAe,iBAAkB,SAAUxJ,GAAkB,MAAO,IAAIA,GAAe,YACjGwJ,UAAU,iBAAkB,iBAAkB,SAAUxJ,GAAkB,MAAO,IAAIA,GAAe,eACpGwJ,UAAU,YAAa,iBAAkB,SAAUxJ,GAAkB,MAAO,IAAIA,GAAe,UAC/FwJ,UAAU,kBAAmB,iBAAkB,SAAUxJ,GAAkB,MAAO,IAAIA,GAAe", "file": "angular-chart.min.js", "sourcesContent": ["(function (factory) {\n  'use strict';\n  if (typeof exports === 'object') {\n    // Node/CommonJS\n    module.exports = factory(\n      typeof angular !== 'undefined' ? angular : require('angular'),\n      typeof Chart !== 'undefined' ? Chart : require('chart.js'));\n  }  else if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(['angular', 'chart'], factory);\n  } else {\n    // Browser globals\n    factory(angular, Chart);\n  }\n}(function (angular, Chart) {\n  'use strict';\n\n  Chart.defaults.global.responsive = true;\n  Chart.defaults.global.multiTooltipTemplate = '<%if (datasetLabel){%><%=datasetLabel%>: <%}%><%= value %>';\n\n  Chart.defaults.global.colours = [\n    '#97BBCD', // blue\n    '#DCDCDC', // light grey\n    '#F7464A', // red\n    '#46BFBD', // green\n    '#FDB45C', // yellow\n    '#949FB1', // grey\n    '#4D5360'  // dark grey\n  ];\n\n  var usingExcanvas = typeof window.G_vmlCanvasManager === 'object' &&\n    window.G_vmlCanvasManager !== null &&\n    typeof window.G_vmlCanvasManager.initElement === 'function';\n\n  if (usingExcanvas) Chart.defaults.global.animation = false;\n\n  return angular.module('chart.js', [])\n    .provider('ChartJs', ChartJsProvider)\n    .factory('ChartJsFactory', ['ChartJs', '$timeout', ChartJsFactory])\n    .directive('chartBase', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory(); }])\n    .directive('chartLine', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Line'); }])\n    .directive('chartBar', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Bar'); }])\n    .directive('chartRadar', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Radar'); }])\n    .directive('chartDoughnut', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Doughnut'); }])\n    .directive('chartPie', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Pie'); }])\n    .directive('chartPolarArea', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('PolarArea'); }]);\n\n  /**\n   * Wrapper for chart.js\n   * Allows configuring chart js using the provider\n   *\n   * angular.module('myModule', ['chart.js']).config(function(ChartJsProvider) {\n   *   ChartJsProvider.setOptions({ responsive: true });\n   *   ChartJsProvider.setOptions('Line', { responsive: false });\n   * })))\n   */\n  function ChartJsProvider () {\n    var options = {};\n    var ChartJs = {\n      Chart: Chart,\n      getOptions: function (type) {\n        var typeOptions = type && options[type] || {};\n        return angular.extend({}, options, typeOptions);\n      }\n    };\n\n    /**\n     * Allow to set global options during configuration\n     */\n    this.setOptions = function (type, customOptions) {\n      // If no type was specified set option for the global object\n      if (! customOptions) {\n        customOptions = type;\n        options = angular.extend(options, customOptions);\n        return;\n      }\n      // Set options for the specific chart\n      options[type] = angular.extend(options[type] || {}, customOptions);\n    };\n\n    this.$get = function () {\n      return ChartJs;\n    };\n  }\n\n  function ChartJsFactory (ChartJs, $timeout) {\n    return function chart (type) {\n      return {\n        restrict: 'CA',\n        scope: {\n          data: '=?',\n          labels: '=?',\n          options: '=?',\n          series: '=?',\n          colours: '=?',\n          getColour: '=?',\n          chartType: '=',\n          legend: '@',\n          click: '=?',\n          hover: '=?',\n\n          chartData: '=?',\n          chartLabels: '=?',\n          chartOptions: '=?',\n          chartSeries: '=?',\n          chartColours: '=?',\n          chartLegend: '@',\n          chartClick: '=?',\n          chartHover: '=?'\n        },\n        link: function (scope, elem/*, attrs */) {\n          var chart, container = document.createElement('div');\n          container.className = 'chart-container';\n          elem.replaceWith(container);\n          container.appendChild(elem[0]);\n\n          if (usingExcanvas) window.G_vmlCanvasManager.initElement(elem[0]);\n\n          ['data', 'labels', 'options', 'series', 'colours', 'legend', 'click', 'hover'].forEach(deprecated);\n          function aliasVar (fromName, toName) {\n            scope.$watch(fromName, function (newVal) {\n              if (typeof newVal === 'undefined') return;\n              scope[toName] = newVal;\n            });\n          }\n          /* provide backward compatibility to \"old\" directive names, by\n           * having an alias point from the new names to the old names. */\n          aliasVar('chartData', 'data');\n          aliasVar('chartLabels', 'labels');\n          aliasVar('chartOptions', 'options');\n          aliasVar('chartSeries', 'series');\n          aliasVar('chartColours', 'colours');\n          aliasVar('chartLegend', 'legend');\n          aliasVar('chartClick', 'click');\n          aliasVar('chartHover', 'hover');\n\n          // Order of setting \"watch\" matter\n\n          scope.$watch('data', function (newVal, oldVal) {\n            if (! newVal || ! newVal.length || (Array.isArray(newVal[0]) && ! newVal[0].length)) {\n              destroyChart(chart, scope);\n              return;\n            }\n            var chartType = type || scope.chartType;\n            if (! chartType) return;\n\n            if (chart && canUpdateChart(newVal, oldVal))\n              return updateChart(chart, newVal, scope, elem);\n\n            createChart(chartType);\n          }, true);\n\n          scope.$watch('series', resetChart, true);\n          scope.$watch('labels', resetChart, true);\n          scope.$watch('options', resetChart, true);\n          scope.$watch('colours', resetChart, true);\n\n          scope.$watch('chartType', function (newVal, oldVal) {\n            if (isEmpty(newVal)) return;\n            if (angular.equals(newVal, oldVal)) return;\n            createChart(newVal);\n          });\n\n          scope.$on('$destroy', function () {\n            destroyChart(chart, scope);\n          });\n\n          function resetChart (newVal, oldVal) {\n            if (isEmpty(newVal)) return;\n            if (angular.equals(newVal, oldVal)) return;\n            var chartType = type || scope.chartType;\n            if (! chartType) return;\n\n            // chart.update() doesn't work for series and labels\n            // so we have to re-create the chart entirely\n            createChart(chartType);\n          }\n\n          function createChart (type) {\n            if (isResponsive(type, scope) && elem[0].clientHeight === 0 && container.clientHeight === 0) {\n              return $timeout(function () {\n                createChart(type);\n              }, 50, false);\n            }\n            if (! scope.data || ! scope.data.length) return;\n            scope.getColour = typeof scope.getColour === 'function' ? scope.getColour : getRandomColour;\n            var colours = getColours(type, scope);\n            var cvs = elem[0], ctx = cvs.getContext('2d');\n            var data = Array.isArray(scope.data[0]) ?\n              getDataSets(scope.labels, scope.data, scope.series || [], colours) :\n              getData(scope.labels, scope.data, colours);\n            var options = angular.extend({}, ChartJs.getOptions(type), scope.options);\n\n            // Destroy old chart if it exists to avoid ghost charts issue\n            // https://github.com/jtblin/angular-chart.js/issues/187\n            destroyChart(chart, scope);\n            chart = new ChartJs.Chart(ctx)[type](data, options);\n            scope.$emit('create', chart);\n\n            // Bind events\n            cvs.onclick = scope.click ? getEventHandler(scope, chart, 'click', false) : angular.noop;\n            cvs.onmousemove = scope.hover ? getEventHandler(scope, chart, 'hover', true) : angular.noop;\n\n            if (scope.legend && scope.legend !== 'false') setLegend(elem, chart);\n          }\n\n          function deprecated (attr) {\n            if (typeof console !== 'undefined' && ChartJs.getOptions().env !== 'test') {\n              var warn = typeof console.warn === 'function' ? console.warn : console.log;\n              if (!! scope[attr]) {\n                warn.call(console, '\"%s\" is deprecated and will be removed in a future version. ' +\n                  'Please use \"chart-%s\" instead.', attr, attr);\n              }\n            }\n          }\n        }\n      };\n    };\n\n    function canUpdateChart (newVal, oldVal) {\n      if (newVal && oldVal && newVal.length && oldVal.length) {\n        return Array.isArray(newVal[0]) ?\n        newVal.length === oldVal.length && newVal.every(function (element, index) {\n          return element.length === oldVal[index].length; }) :\n          oldVal.reduce(sum, 0) > 0 ? newVal.length === oldVal.length : false;\n      }\n      return false;\n    }\n\n    function sum (carry, val) {\n      return carry + val;\n    }\n\n    function getEventHandler (scope, chart, action, triggerOnlyOnChange) {\n      var lastState = null;\n      return function (evt) {\n        var atEvent = chart.getPointsAtEvent || chart.getBarsAtEvent || chart.getSegmentsAtEvent;\n        if (atEvent) {\n          var activePoints = atEvent.call(chart, evt);\n          if (triggerOnlyOnChange === false || angular.equals(lastState, activePoints) === false) {\n            lastState = activePoints;\n            scope[action](activePoints, evt);\n            scope.$apply();\n          }\n        }\n      };\n    }\n\n    function getColours (type, scope) {\n      var notEnoughColours = false;\n      var colours = angular.copy(scope.colours ||\n        ChartJs.getOptions(type).colours ||\n        Chart.defaults.global.colours\n      );\n      while (colours.length < scope.data.length) {\n        colours.push(scope.getColour());\n        notEnoughColours = true;\n      }\n      // mutate colours in this case as we don't want\n      // the colours to change on each refresh\n      if (notEnoughColours) scope.colours = colours;\n      return colours.map(convertColour);\n    }\n\n    function convertColour (colour) {\n      if (typeof colour === 'object' && colour !== null) return colour;\n      if (typeof colour === 'string' && colour[0] === '#') return getColour(hexToRgb(colour.substr(1)));\n      return getRandomColour();\n    }\n\n    function getRandomColour () {\n      var colour = [getRandomInt(0, 255), getRandomInt(0, 255), getRandomInt(0, 255)];\n      return getColour(colour);\n    }\n\n    function getColour (colour) {\n      return {\n        fillColor: rgba(colour, 0.2),\n        strokeColor: rgba(colour, 1),\n        pointColor: rgba(colour, 1),\n        pointStrokeColor: '#fff',\n        pointHighlightFill: '#fff',\n        pointHighlightStroke: rgba(colour, 0.8)\n      };\n    }\n\n    function getRandomInt (min, max) {\n      return Math.floor(Math.random() * (max - min + 1)) + min;\n    }\n\n    function rgba (colour, alpha) {\n      if (usingExcanvas) {\n        // rgba not supported by IE8\n        return 'rgb(' + colour.join(',') + ')';\n      } else {\n        return 'rgba(' + colour.concat(alpha).join(',') + ')';\n      }\n    }\n\n    // Credit: http://stackoverflow.com/a/11508164/1190235\n    function hexToRgb (hex) {\n      var bigint = parseInt(hex, 16),\n        r = (bigint >> 16) & 255,\n        g = (bigint >> 8) & 255,\n        b = bigint & 255;\n\n      return [r, g, b];\n    }\n\n    function getDataSets (labels, data, series, colours) {\n      return {\n        labels: labels,\n        datasets: data.map(function (item, i) {\n          return angular.extend({}, colours[i], {\n            label: series[i],\n            data: item\n          });\n        })\n      };\n    }\n\n    function getData (labels, data, colours) {\n      return labels.map(function (label, i) {\n        return angular.extend({}, colours[i], {\n          label: label,\n          value: data[i],\n          color: colours[i].strokeColor,\n          highlight: colours[i].pointHighlightStroke\n        });\n      });\n    }\n\n    function setLegend (elem, chart) {\n      var $parent = elem.parent(),\n          $oldLegend = $parent.find('chart-legend'),\n          legend = '<chart-legend>' + chart.generateLegend() + '</chart-legend>';\n      if ($oldLegend.length) $oldLegend.replaceWith(legend);\n      else $parent.append(legend);\n    }\n\n    function updateChart (chart, values, scope, elem) {\n      if (Array.isArray(scope.data[0])) {\n        chart.datasets.forEach(function (dataset, i) {\n          (dataset.points || dataset.bars).forEach(function (dataItem, j) {\n            dataItem.value = values[i][j];\n          });\n        });\n      } else {\n        chart.segments.forEach(function (segment, i) {\n          segment.value = values[i];\n        });\n      }\n      chart.update();\n      scope.$emit('update', chart);\n      if (scope.legend && scope.legend !== 'false') setLegend(elem, chart);\n    }\n\n    function isEmpty (value) {\n      return ! value ||\n        (Array.isArray(value) && ! value.length) ||\n        (typeof value === 'object' && ! Object.keys(value).length);\n    }\n\n    function isResponsive (type, scope) {\n      var options = angular.extend({}, Chart.defaults.global, ChartJs.getOptions(type), scope.options);\n      return options.responsive;\n    }\n\n    function destroyChart(chart, scope) {\n      if(! chart) return;\n      chart.destroy();\n      scope.$emit('destroy', chart);\n    }\n  }\n}));\n"], "sourceRoot": "/source/"}