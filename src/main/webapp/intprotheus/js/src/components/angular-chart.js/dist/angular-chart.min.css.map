{"version": 3, "sources": ["angular-chart.css"], "names": [], "mappings": "AAAc,W,CAAd,a,CAKkB,gB,CAJN,Y,CACC,W,CAEC,iB,CADF,a,CAIV,oB,CACA,c,CACA,iB,CAEA,uB,CAEA,oB,CAEA,c,CAGe,c,CAAjB,gB,CAKqB,mB,CAJN,e,CACC,c,CAEC,oB,CADF,gB,CAIb,oB,CACA,kB,CACA,iB,CACA,iB,CACA,iB,CACA,wB,CACA,iB,CACA,c,CAEiB,gB,CAAnB,kB,CAKuB,qB,CAJN,iB,CACC,gB,CAEC,sB,CADF,kB,CAIf,a,CACA,iB,CACA,M,CACA,K,CACA,U,CACA,W,CACA,iB", "file": "angular-chart.min.css", "sourcesContent": [".chart-legend,\n.bar-legend,\n.line-legend,\n.pie-legend,\n.radar-legend,\n.polararea-legend,\n.doughnut-legend {\n  list-style-type: none;\n  margin-top: 5px;\n  text-align: center;\n  /* NOTE: Browsers automatically add 40px of padding-left to all lists, so we should offset that, otherwise the legend is off-center */\n  -webkit-padding-start: 0;\n  /* Webkit */\n  -moz-padding-start: 0;\n  /* Mozilla */\n  padding-left: 0;\n  /* IE (handles all cases, really, but we should also include the vendor-specific properties just to be safe) */\n}\n.chart-legend li,\n.bar-legend li,\n.line-legend li,\n.pie-legend li,\n.radar-legend li,\n.polararea-legend li,\n.doughnut-legend li {\n  display: inline-block;\n  white-space: nowrap;\n  position: relative;\n  margin-bottom: 4px;\n  border-radius: 5px;\n  padding: 2px 8px 2px 28px;\n  font-size: smaller;\n  cursor: default;\n}\n.chart-legend-icon,\n.bar-legend-icon,\n.line-legend-icon,\n.pie-legend-icon,\n.radar-legend-icon,\n.polararea-legend-icon,\n.doughnut-legend-icon {\n  display: block;\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 20px;\n  height: 20px;\n  border-radius: 5px;\n}\n"], "sourceRoot": "/source/"}