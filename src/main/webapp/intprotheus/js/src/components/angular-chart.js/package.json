{"name": "angular-chart.js", "version": "0.10.2", "description": "An angular.js wrapper for Chart.js", "main": "dist/angular-chart.js", "directories": {"example": "examples"}, "scripts": {"codeclimate": "./node_modules/codeclimate-test-reporter/bin/codeclimate.js < coverage/lcov.info", "docker": "docker build -t angular-chart.js . && docker run --rm -it -v `pwd`/coverage:/src/coverage angular-chart.js", "test": "gulp check"}, "author": "<PERSON>-<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/jtblin/angular-chart.js.git"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"chai": "^3.4.1", "chai-string": "^1.1.1", "codeclimate-test-reporter": "^0.3.1", "cp": "^0.2.0", "gm": "^1.17.0", "gulp": "^3.9.0", "gulp-bump": "^1.0.0", "gulp-csso": "^1.0.1", "gulp-git": "^1.6.0", "gulp-gzip": "^1.2.0", "gulp-istanbul": "^0.10.3", "gulp-istanbul-report": "^0.0.1", "gulp-jscs": "^3.0.2", "gulp-jshint": "^1.9.2", "gulp-less": "^3.0.3", "gulp-mocha-phantomjs": "^0.11.0", "gulp-rename": "^1.2.0", "gulp-rimraf": "^0.2.0", "gulp-sequence": "^0.4.1", "gulp-shell": "^0.5.1", "gulp-sourcemaps": "^1.0.0", "gulp-spawn-mocha": "^2.0.1", "gulp-tar": "^1.5.0", "gulp-uglify": "^1.4.2", "imgur-node-api": "^0.1.0", "jshint-stylish": "^2.0.1", "mkdirp": "^0.5.0", "mocha": "^2.1.0", "mocha-phantomjs-istanbul": "^0.0.2", "sinon": "^1.12.2", "sinon-chai": "^2.7.0", "testatic": "^0.1.0", "tmp-sync": "^1.1.0", "webpack": "^1.13.0", "webshot": "^0.18.0"}, "dependencies": {"angular": "1.x", "chart.js": "^1.1.1"}}