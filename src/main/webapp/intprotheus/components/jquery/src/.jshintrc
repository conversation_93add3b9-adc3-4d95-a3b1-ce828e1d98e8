{
	"boss": true,
	"curly": true,
	"eqeqeq": true,
	"eqnull": true,
	"expr": true,
	"immed": true,
	"noarg": true,
	"quotmark": "double",
	"undef": true,
	"unused": true,

	"sub": true,

	// Support: IE < 10, Android < 4.1
	// The above browsers are failing a lot of tests in the ES5
	// test suite at http://test262.ecmascript.org.
	"es3": true,

	"globals": {
		"window": true,
		"JSON": false,

		"jQuery": true,
		"define": true,
		"module": true,
		"noGlobal": true
	}
}
