<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <h:panelGroup styleClass="span9 offset3 caixaPrincipal" id="planoSucesso"
                      style="padding: 20px; width: 90%; margin-left: 3%;">
            <h4>Planos de sucesso - Integrantes</h4>
            <br/>
            <p:commandButton value="Cadastrar" style="float: right; margin-bottom: 20px;" styleClass="btn btn-primary"
                             action="#{integrantesCSControle.novo}"/>

            <h:panelGroup layout="block" styleClass="form-group col-md-10">
                <p:dataTable id="tblPlanoSucesso" value="#{integrantesCSControle.customerSuccesses}" var="cs">
                    <p:column>
                        <f:facet name="header">
                            <h:outputText value="Nome"/>
                        </f:facet>
                        <p:commandLink value="#{cs.nome}" action="#{integrantesCSControle.alterar(cs)}"/>
                    </p:column>
                    <p:column>
                        <f:facet name="header">
                            <h:outputText value="Telefone profissional"/>
                        </f:facet>
                        <p:commandLink value="#{cs.telefone}" action="#{integrantesCSControle.alterar(cs)}"/>
                    </p:column>
                    <p:column>
                        <f:facet name="header">
                            <h:outputText value="URL"/>
                        </f:facet>
                        <p:commandLink value="#{cs.url}" action="#{integrantesCSControle.alterar(cs)}"/>
                    </p:column>
                    <p:column>
                        <f:facet name="header">
                            <h:outputText value="Ativo"/>
                        </f:facet>
                        <p:commandLink rendered="#{cs.ativo}" value="Sim"
                                       action="#{integrantesCSControle.alterar(cs)}"/>
                        <p:commandLink rendered="#{!cs.ativo}" value="Não"
                                       action="#{integrantesCSControle.alterar(cs)}"/>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>
        </h:panelGroup>
    </ui:define>
</ui:decorate>
</html>
