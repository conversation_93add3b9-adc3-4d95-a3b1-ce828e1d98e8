<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <h:panelGroup styleClass="span12 caixaPrincipal" id="planoSucesso"
                      style="padding: 20px; width: 100%; margin-left: 0">
            <h4>Planos de sucesso</h4>
            <br/>
            <p:commandButton value="Cadastrar" style="float: right; margin-bottom: 20px;" styleClass="btn btn-primary"
                             action="#{ModeloPlanoSucessoControle.novoPlano}"/>
            <h:panelGroup id="pnlPlanoSucesso" layout="block" styleClass="form-group col-md-10">
                <p:dataTable id="tblPlanoSucesso" value="#{ModeloPlanoSucessoControle.modelos}" var="modelo">
                    <p:column width="30%">
                        <f:facet name="header">
                            <h:outputText value="Nome do Plano"/>
                        </f:facet>
                        <h:commandLink value="#{modelo.nome}" action="#{ModeloPlanoSucessoControle.alterar(modelo)}"/>
                    </p:column>
                    <p:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição"/>
                        </f:facet>
                        <h:panelGroup id="pnlDescricao" layout="block">
                            <h:outputText value="#{modelo.descricao_Apresentar}" title="#{modelo.descricao}"/>

                            <p:commandLink
                                    actionListener="#{ModeloPlanoSucessoControle.alterarApresentacaoDescricao(modelo)}"
                                    rendered="#{!modelo.apresentarTodaDescricao}"
                                    update=":fmLay:pnlPlanoSucesso" value="..."/>
                        </h:panelGroup>
                    </p:column>
                    <p:column>
                        <f:facet name="header">
                            <h:outputText value="Duração"/>
                        </f:facet>
                        <h:outputText value="#{modelo.duracao} dias"/>
                    </p:column>
                </p:dataTable>
            </h:panelGroup>
        </h:panelGroup>
    </ui:define>
</ui:decorate>
</html>
