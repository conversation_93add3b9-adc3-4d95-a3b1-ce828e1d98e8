<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions">
<f:metadata>
    <f:event type="preRenderView" listener="#{SuperControl.validaAdmin}"/>
</f:metadata>
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <script src="#{request.contextPath}/resources/js/amcharts/amcharts.js"></script>
        <script src="#{request.contextPath}/resources/js/amcharts/pie.js"></script>
        <link type="text/css" href="#{request.contextPath}/resources/js/amcharts/plugins/export/export.css"
              rel="stylesheet"/>

        <h:panelGroup styleClass="span12 offset3 caixaPrincipal" id="enotasPanel"
                      style="padding: 20px; width: 100%; margin-left: 1%;">
            <h4 style="margin-left: auto">SINCRONIZAÇÃO ENOTAS / METABASE</h4>
            <br/>
            <div class="col-md-12 divForm">
                <p:commandLink action="#{EnotasControle.sincronizarEmpresa}" id="sincNotaEmpresa" name="sincNotaEmpresa"
                               value="Sincronizar Empresas"
                               styleClass="btn btn-primary btn-sm"
                               title="Esta ação irá sincronizar todas as empresas integradas ao Enotas."/>

                <p:commandLink action="#{EnotasControle.sincronizarNotas}" id="sincTodasNotas" name="sincTodasNotas"
                               value="Sincronizar Notas"
                               styleClass="btn btn-primary btn-sm"
                               title="Esta ação irá sincronizar todas as notas de todas as empresas integradas ao Enotas"/>

                <p:commandLink action="#{EnotasControle.sincronizarTudo}" id="sincTudo" name="sincTudo"
                               value="Sincronizar tudo (isso pode demorar)"
                               styleClass="btn btn-danger btn-sm" style="float: right;"
                               title="Esta ação irá sincronizar todos os dados das empresas, cidades, certificados e notas e poderá demorar um pouco para finalizar."/>
            </div>
        </h:panelGroup>
    </ui:define>
</ui:decorate>
</html>
