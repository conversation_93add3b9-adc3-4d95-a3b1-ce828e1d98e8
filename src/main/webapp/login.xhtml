<!DOCTYPE html>
<html lang="en"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
      xmlns:pt="http://xmlns.jcp.org/jsf/passthrough"
      xmlns:ui="http://java.sun.com/jsf/facelets">

    <h:head>
        <meta charset="utf-8"/>
        <title>OAMD - Login</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <meta name="description" content=""/>
        <meta name="author" content=""/>

        <!-- Le styles -->
        <h:outputStylesheet library="css" name="bootstrap.css"/>
        <h:outputStylesheet library="css" name="font-awesome.min.css"/>
        <script src="https://www.google.com/recaptcha/api.js?hl=pt-BR"></script>
        <style type="text/css">
            body {
                padding-top: 40px;
                padding-bottom: 40px;
                background-color: #f5f5f5;
            }

            .form-signin {
                max-width: 300px;
                padding: 19px 29px 29px;
                margin: 0 auto 20px;
                background-color: #fff;
                border: 1px solid #e5e5e5;
                -webkit-border-radius: 5px;
                -moz-border-radius: 5px;
                border-radius: 5px;
                -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.05);
                -moz-box-shadow: 0 1px 2px rgba(0,0,0,.05);
                box-shadow: 0 1px 2px rgba(0,0,0,.05);
            }
            .form-signin .form-signin-heading,
            .form-signin .checkbox {
                margin-bottom: 10px;
            }
            .form-signin input[type="text"],
            .form-signin input[type="password"] {
                font-size: 16px;
                height: auto;
                margin-bottom: 15px;
                padding: 7px 9px;
            }

        </style>
        <h:outputStylesheet library="css" name="bootstrap-responsive.css"/>

        <link rel="apple-touch-icon-precomposed" sizes="144x144" href='#{resource["imagens/apple-touch-icon.png"]}'/>
        <link rel="apple-touch-icon-precomposed" sizes="114x114" href='#{resource["imagens/apple-touch-icon.png"]}'/>
        <link rel="apple-touch-icon-precomposed" sizes="72x72" href='#{resource["imagens/apple-touch-icon.png"]}'/>
        <link rel="apple-touch-icon-precomposed" sizes="57x57" href='#{resource["imagens/apple-touch-icon.png"]}'/>
        <link rel="shortcut icon" type="image/png" href='#{resource["imagens/favicon.ico"]}' />
    </h:head>
    <h:body>
        <div style="vertical-align: middle;" class="container">

            <h:form class="form-signin">
                <p:remoteCommand global="false" name="saveParams" action="#{SuperControl.saveParams()}" update="pnlRecaptcha"/>
                <script type="text/javascript">
                    var rootPath = "#{SuperControl.getViewProp('rootPath')}";
                    var ipExistente = "#{SuperControl.ip}";
                    var heightScreenClient = screen.availHeight;
                    var widthScreenClient = screen.availWidth;
                    console.log('Existing: ' + ipExistente);
                    if ((ipExistente == '') || (ipExistente == 'N/C')) {
                        urlGetIp = "http://jsonip.com/callback=?";
                        rtype = "jsonp";
                        var protocolo = document.location.protocol.toString();
                        var endereco = document.location.host.toString();
                        if ((endereco.indexOf("pactosolucoes.com.br", 0) !== -1)
                                || endereco.indexOf("localhost", 0) !== -1) {
                            urlGetIp = "http://app.pactosolucoes.com.br/ip/v2.php";
                            rtype = "text";
                            if (protocolo.indexOf("https", 0) != -1) {
                                urlGetIp = "https://app.pactosolucoes.com.br/ip/v2.php";
                            }
                        }
                        $.ajax({
                            type: "GET",
                            url: urlGetIp,
                            dataType: rtype,
                            success: function(valor) {
                                if (rtype === "jsonp") {
                                    ipExistente = valor.ip;
                                } else {
                                    ipExistente = valor;
                                }
                                console.log('captured: ' + ipExistente);
                                saveParams([{name: 'ip', value: ipExistente}, {name: 'heightScreen', value: heightScreenClient},
                                    {name: 'widthScreen', value: widthScreenClient}]);
                            }
                        });
                    }
                </script>
                <p:dialog modal="true" id="panelCarregando"                                                                  
                          styleClass="panelCarregando"                      
                          showHeader="false"
                          closeOnEscape="false"
                          widgetVar="carregando"
                          draggable="false"
                          closable="false"
                          maximizable="false"
                          minimizable="false"
                          resizable="false"
                          width="210"
                          minWidth="220">
                    <i class="fa-icon-spin fa-icon-refresh fa-icon-2x" style="vertical-align: middle;" />
                    <h:outputText style="font-weight: bold; vertical-align: middle; margin-left: 10px; font-size: 14px;" value="Por favor, aguarde..."/>
                </p:dialog>

                <p:ajaxStatus style="width:64px;height:64px;position:fixed;right:5px;bottom:5px"                          
                              onstart="carregando.show();carregando.jq.addClass('panelCarregando');"
                              oncomplete="carregando.hide();"
                              onerror="carregando.hide();"/>
                <ui:fragment rendered="#{LoginControle.imgLogin == null or empty LoginControle.imgLogin}">
                    <h2 class="form-signin-heading">OAMD - Login</h2>
                </ui:fragment>
                
                <h:panelGroup style="width: 100%; text-align: center; display: block;"
                     rendered="#{LoginControle.imgLogin != null and not empty LoginControle.imgLogin}">
                    <p:graphicImage library="imagens"
                                style="width: 70%"
                                name="#{LoginControle.imgLogin}"/>
                </h:panelGroup>
                

                <p:inputText id="usernameOAMD" autocomplete="true"
                             value="#{LoginControle.userName}"
                             styleClass="input-block-level"
                             />

                <p:password id="pwdOAMD" autocomplete="true"
                            value="#{LoginControle.senha}"
                            styleClass="input-block-level"/>

                <h:panelGroup id="pnlRecaptcha" layout="block" styleClass="inputs" style="margin-bottom: 10px;">
                    <h:panelGroup layout="block" rendered="#{LoginControle.enableCaptcha}">
                        <div class="g-recaptcha" data-sitekey="6Lce9-EcAAAAAFEHzw998J7pEEyt_amDTLIudWDd"></div>
                    </h:panelGroup>
                </h:panelGroup>


                <p:commandButton action="#{LoginControle.logar}" styleClass="btn btn-primary"
                                 value="Entrar"
                                 />
                <br/>

                <p:growl id="growl" life="3000" autoUpdate="true" globalOnly="true" sticky="true" />


            </h:form>

        </div>
    </h:body>
</html>
