<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui"
      xmlns:fn="http://java.sun.com/jsp/jstl/functions"
      xmlns:c="http://java.sun.com/jsp/jstl/core">
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="CSS">
        <style>
            .gridRecente .ui-datagrid-data {
                width: auto !important;
            }

            .gridRecente .ui-datagrid-content {
                border: none !important;
            }

            .tabelaUsuarios table tbody tr td a {
                padding: 8px;
            }

            .ui-menu .ui-menu-dynamic .ui-widget .ui-widget-content .ui-corner-all ui-helper-clearfix .ui-shadow {
                z-index: 3000 !important;
            }

            .tabelaUsuarios table tbody tr:nth-child(odd) {
                background-color: #FFFFFF !important;
            }

            .tabelaUsuarios table tbody tr:nth-child(odd):hover {
                background-color: #CBCBCB !important;
            }

            .ui-autocomplete-panel li {
                float: none !important;
            }

            .color-box {
                width: 20px;
                height: 20px;
                display: inline-block;
                background-color: #ffffff;
                position: center;
            }
            .semBorda tr, .semBorda td {
                border: none !important;
            }

            .ui-state-highlight{
                color: black!important;
                background: #ffc465 !important;
            }

            .btn-style{
                background: #999999!important;
                color: black!important;
            }
            .btn-style:hover{
                color: black;
                background: #ffc465!important;
            }
            .ui-widget-header{
                background: white!important;
            }

            .ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active{
                background-color: #32CD32!important;
            }
        </style>

    </ui:define>
    <ui:define name="conteudo">

        <p:panel id="panelDialoigo" style="border: none!important;">
            <p:toolbar style="background: white!important; border: none!important;">
                <p:toolbarGroup align="left">
                </p:toolbarGroup>

                <p:toolbarGroup align="right">

                    <p:commandButton value="Cancelar" actionListener="#{ aplicativoEmpresaControle.cancelar() }"
                                     styleClass="btn-style" style="padding: 5px; background: #FF0000!important; color: white!important;"
                                     title="Cancelar cadastro"
                                     process="@this" update="panelDialoigo"
                                     rendered="#{!aplicativoEmpresaControle.editando}"
                    />
                    <p:commandButton value="Cancelar" actionListener="#{ aplicativoEmpresaControle.buscarChave() }"
                                     styleClass="btn-style" style="padding: 5px; background: #FF0000!important; color: white!important;"
                                     title="Cancelar cadastro e voltar para a tela de edição. "
                                     process="@this" update="panelDialoigo"
                                     rendered="#{aplicativoEmpresaControle.editando}"
                    />

                    <p:commandButton value="Adicionar" actionListener="#{ aplicativoEmpresaControle.salvar() }"
                                     styleClass="btn-style" style="padding: 5px 20px 5px 20px;margin-left: 20px; background: #0000CD!important; color: white!important;"
                                     title="Salvar dados e adicionar Chave"
                                     rendered="#{aplicativoEmpresaControle.editando}"
                                     process="@this,panelDialoigo" update="panelDialoigo"/>

                </p:toolbarGroup>
            </p:toolbar>

            <p:panel id="panelTable" style="margin-top: 20px!important;" rendered="#{!aplicativoEmpresaControle.editando}">
                <p:outputPanel>
                    <h:outputText value="Buscar: " />
                    <p:inputText id="globalFilter" style="width:600px" value="#{aplicativoEmpresaControle.nomeEmpresa}">
                        <p:ajax event="keyup" process="@this" listener="#{aplicativoEmpresaControle.filterEmpresa}" update=":fmLay:tableChaves" />
                    </p:inputText>
                </p:outputPanel>

                <p:dataTable value="#{ aplicativoEmpresaControle.empresasModal }"  var="empresa"
                             rowKey="#{empresa.chave}" paginatorPosition="top"
                             selection="#{ aplicativoEmpresaControle.empresaSelect }" id="tableChaves"
                             paginator="true" rowsPerPageTemplate="5,10" rows="10" widgetVar="dgTable">

                    <p:column headerText="Empresa" width="500" filterBy="#{empresa.visualName}" >
                        <p:outputLabel value="#{ empresa.visualName }"/>
                    </p:column>

                    <p:column selectionMode="single" style="width:16px;text-align:center"/>

                    <f:facet name="footer">
                        <p:commandButton process="tableChaves" styleClass="btn-style" title="Editar dados de localização da empresa selecionada."
                                         actionListener="#{aplicativoEmpresaControle.editar()}"
                                         style="padding: 5px 50px 5px 50px;" update=":fmLay:panelDialoigo, :fmLay:dataList" icon="ui-icon-plus" />
                    </f:facet>

                </p:dataTable>

            </p:panel>

            <p:panel  style="margin-top: 20px!important;" rendered="#{aplicativoEmpresaControle.editando}">
                <p:panelGrid columns="2" styleClass="semBorda" style="width: 100%!important;">
                    <f:facet name="header">
                        <h3>Dados de Localização</h3>
                        <p:outputLabel value="#{aplicativoEmpresaControle.empresaSelect.visualName}"/>
                        <p:outputLabel value="#{aplicativoEmpresaControle.empresaSelect.chave}"/>
                    </f:facet>

                        <p:outputLabel value="Latitude" for="latitude"/>
                        <p:inputText value="#{aplicativoEmpresaControle.empresaAppUnificado.latitude}" id="latitude" />

                        <p:outputLabel value="Longitude" for="longitude"/>
                        <p:inputText value="#{aplicativoEmpresaControle.empresaAppUnificado.longitude}" id="longitude" />

                        <p:outputLabel value="Endereço" for="endereco"/>
                        <p:inputTextarea value="#{aplicativoEmpresaControle.empresaAppUnificado.endereco}" id="endereco"
                                         rows="8" cols="500" style="width: 70%!important;"/>
                </p:panelGrid>
            </p:panel>

            <p:panel id="dataList" style="border: none!important; "
                     rendered="#{not empty aplicativoEmpresaControle.listAdd and !aplicativoEmpresaControle.editando}">

                <p:commandButton value="Concluir" actionListener="#{ aplicativoEmpresaControle.concluir() }"
                                 styleClass="btn-style" style="padding: 5px 20px 5px 20px;margin-left: 20px;
                                 background: #32CD32!important; color: white!important; float: right; margin-top: 10px"
                                 title="Concluir e voltar para tela de Edição de aplicativo"
                                 rendered="#{not empty aplicativoEmpresaControle.listAdd}"
                                 process="@this" />

                <p:dataTable value="#{aplicativoEmpresaControle.listAdd}" var="empresa" type="ordered">
                    <f:facet name="header">
                        <h3>Empresas adicionadas</h3>
                    </f:facet>
                    <p:column headerText="Empresa">
                        <p:outputLabel value="#{empresa.nome}"/>
                    </p:column>
                    <p:column width="50">
                        <p:commandButton actionListener="#{aplicativoEmpresaControle.removerEmpresa(empresa)}"
                                         update=":fmLay:dataList"  value="X" style="float: right; margin-top: 5px; color: red"/>
                    </p:column>

                </p:dataTable>
            </p:panel>
        </p:panel>
    </ui:define>
</ui:decorate>
</html>