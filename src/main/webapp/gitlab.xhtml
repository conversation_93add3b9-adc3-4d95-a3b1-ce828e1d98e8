<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:p="http://primefaces.org/ui"
>
<f:metadata>
    <f:event type="preRenderView" listener="#{SuperControl.validaAdmin}"/>
</f:metadata>
<ui:decorate template="/template/layout.xhtml">
    <ui:define name="conteudo">
        <script src="#{request.contextPath}/resources/js/amcharts/amcharts.js"></script>
        <script src="#{request.contextPath}/resources/js/amcharts/pie.js"></script>
        <link type="text/css" href="#{request.contextPath}/resources/js/amcharts/plugins/export/export.css"
              rel="stylesheet"/>

        <h:panelGroup styleClass="span12 offset3 caixaPrincipal" id="enotasPanel"
                      style="padding: 20px; width: 100%; margin-left: 1%;">
            <h4 style="margin-left: auto">PAINÉL DE CONTROLE - GITLAB</h4>

            <p:accordionPanel>
                <p:tab title="Consultar status dos Projetos">
                    <h:panelGrid columns="3" cellpadding="7" styleClass="mb-3">
                        <p:outputLabel for="projetoConsultar" value="Projeto:"/>
                        <p:selectOneMenu id="projetoConsultar" value="#{GitlabControle.project}" required="true"
                                         label="Projeto">
                            <f:selectItems value="#{GitlabControle.projectsList}"/>

                        </p:selectOneMenu>

                        <p:message for="projetoConsultar" display="icon"/>

                    </h:panelGrid>

                    <p:commandLink styleClass="btn btn-primary" action="#{GitlabControle.consultarProjetos}"
                                   update="@form" ajax="true">
                        <i class="fa-icon-check"/> Consultar
                    </p:commandLink>

                    <h:panelGroup id="tblProjectsStatus" style="margin-top: 12px">
                        <p:dataTable value="#{GitlabControle.projectStatus}" var="project"
                                     rendered="#{GitlabControle.apresentarTabelaProjetos}">
                            <p:column>
                                <f:facet name="header">
                                    <h:outputText value="Projeto"/>
                                </f:facet>
                                <h:outputText value="#{project.project}"/>
                            </p:column>

                            <p:column>
                                <f:facet name="header">
                                    <h:outputText value="Status"/>
                                </f:facet>
                                <h:outputText value="#{project.status}"/>
                            </p:column>
                        </p:dataTable>
                    </h:panelGroup>

                </p:tab>

                <p:tab title="Release Tag">

                    <p:commandLink styleClass="btn btn-primary" action="#{GitlabControle.solicitarReleaseTag}"
                                   update="@form" ajax="true">
                        <i class="fa-icon-desktop"/>  Release Tag - ZW
                    </p:commandLink>

                    <h:panelGroup layout="block" id="infoReleaseTagControl" style="margin-top: 12px">
                        <h:outputLink value="#{GitlabControle.webUrl}"
                                      target="_blank"
                                      rendered="#{GitlabControle.apresentarWebUrlReleaseTag}">
                            Link para Release Tag
                        </h:outputLink>
                    </h:panelGroup>
                </p:tab>

                <p:tab title="Controlar Merge Requests">
                    <h:panelGrid columns="3" cellpadding="7" styleClass="mb-3">
                        <p:outputLabel for="projetoControl" value="Projeto:"/>
                        <p:selectOneMenu id="projetoControl" value="#{GitlabControle.projectBlock}" label="Projeto">
                            <f:selectItems value="#{GitlabControle.projectsList}"/>
                        </p:selectOneMenu>
                        <p:message for="projetoControl" display="icon"/>

                        <p:outputLabel for="acaoControlar" value="Ação:"/>
                        <p:selectOneRadio id="acaoControlar" value="#{GitlabControle.projectAction}" label="Ação">
                            <f:selectItem itemValue="Liberar" itemLabel="Liberar"/>
                            <f:selectItem itemValue="Bloquear" itemLabel="Bloquear"/>
                        </p:selectOneRadio>
                        <p:message for="acaoControlar" display="icon"/>

                    </h:panelGrid>


                    <p:commandLink styleClass="btn btn-primary" action="#{GitlabControle.solicitarAcao}"
                                   update="@form" ajax="true">
                        <i class="fa-icon-github"/> Atualizar
                    </p:commandLink>

                    <h:panelGroup layout="block" id="infoProjectControl" style="margin-top: 12px">
                        <h:outputText value="#{GitlabControle.projectControlStatus}"
                                      rendered="#{GitlabControle.apresentarProjectControlStatus}"/>
                    </h:panelGroup>
                </p:tab>

            </p:accordionPanel>

        </h:panelGroup>
    </ui:define>
</ui:decorate>
</html>
