package br.com.pacto.objeto;

import br.com.pacto.util.AlgoritmoCriptoEnum;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.spec.AlgorithmParameterSpec;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class Criptografia {

    public static final String ALGORITMO_AES = "AES";
    public static final String CHAVE_CRIPTO = "PZWMUSCCC";
    private static final Map<String, Integer> tamanhosChaves = new HashMap<>();
    private static final Base64.Encoder enc = Base64.getEncoder();
    private static final Base64.Decoder dec = Base64.getDecoder();

    static {
        tamanhosChaves.put(AlgoritmoCriptoEnum.ALGORITMO_TRIPLE_DES.getId(), AlgoritmoCriptoEnum.ALGORITMO_TRIPLE_DES.getTamanho());
        tamanhosChaves.put(AlgoritmoCriptoEnum.ALGORITMO_DES.getId(), AlgoritmoCriptoEnum.ALGORITMO_DES.getTamanho());
        tamanhosChaves.put(AlgoritmoCriptoEnum.ALGORITMO_BLOWFISH.getId(), AlgoritmoCriptoEnum.ALGORITMO_BLOWFISH.getTamanho());
        tamanhosChaves.put(AlgoritmoCriptoEnum.ALGORITMO_AES.getId(), AlgoritmoCriptoEnum.ALGORITMO_AES.getTamanho());
    }

    private Criptografia() {
    }


    public static String encryptMD5(String senha) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");

            md.update(senha.getBytes());
            BigInteger hash = new BigInteger(1, md.digest());
            String retornaSenha = hash.toString(16);
            return retornaSenha;
        } catch (NoSuchAlgorithmException ns) {
            ns.printStackTrace();
            return null;
        }
    }

    public static String encrypt(String text, String chave, AlgoritmoCriptoEnum algoritmo) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, InvalidKeyException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, UnsupportedEncodingException, InvalidKeySpecException {
        return encrypt(text, chave, algoritmo, true);
    }

    public static String encrypt(String text, String chave, AlgoritmoCriptoEnum algoritmo, boolean usarWorkaround) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, InvalidKeyException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, UnsupportedEncodingException, InvalidKeySpecException {
        SecretKey skeySpec = getSecretKey(chave, algoritmo.getId());
        AlgorithmParameterSpec paramSpec = new IvParameterSpec(new byte[algoritmo.getTamanho()]);
        Cipher cipher = Cipher.getInstance(algoritmo.getCbc());
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, paramSpec);
        String codigoCripto = enc.encodeToString(cipher.doFinal(text.getBytes()));
        //Workaround para contornar problemas de código criptografado em URLs
        if (usarWorkaround) {
            codigoCripto = codigoCripto.replace("+", "*");
        }
        codigoCripto = codigoCripto.replace("/", "@");
        return codigoCripto;
    }

    public static SecretKey getSecretKey(String chave, String algoritmo) {
        String keyString = chave;

        int tam = new Long(tamanhosChaves.get(algoritmo).toString()).intValue();
        byte[] keyB = new byte[tam];
        for (int i = 0; i < keyString.length() && i < keyB.length; i++) {
            keyB[i] = (byte) keyString.charAt(i);
        }

        return new SecretKeySpec(keyB, algoritmo);
    }

    public static String decrypt(String text, String chave, AlgoritmoCriptoEnum algoritmo) {
        return decrypt(text, chave, algoritmo, true);
    }

    public static String decrypt(String text, String chave, AlgoritmoCriptoEnum algoritmo, boolean usarWorkaround) {
        StringBuilder ret = new StringBuilder();
        try {
            SecretKey skeySpec = getSecretKey(chave, algoritmo.getId());
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(new byte[algoritmo.getTamanho()]);
            Cipher cipher = Cipher.getInstance(algoritmo.getCbc());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, paramSpec);
            //Workaround para contornar problemas de cï¿½digo criptografado em URLs
            if (usarWorkaround) {
                text = text.replace("*", "+");
            }
            text = text.replace("@", "/");

            byte[] b = dec.decode(text);

            ret.append(new String(cipher.doFinal(b)));
        } catch (Exception e) {
            return e.getMessage();
        }
        return ret.toString();
    }
}
