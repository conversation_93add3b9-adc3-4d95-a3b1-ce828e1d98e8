package br.com.pacto.security.impl;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dtos.PersonaDTO;
import br.com.pacto.security.exceptions.TokenExpiradoException;
import br.com.pacto.security.exceptions.TokenInvalidoException;
import br.com.pacto.security.interfaces.LeituraTokenServico;
import br.com.pacto.util.PropsService;
import br.com.pacto.util.UteisValidacao;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

public class LeituraTokenServiceImpl implements LeituraTokenServico {

    private final String EMITENTE = "aut_pacto";
    private final String CLAIM_CONTENT = "content";
    private final String PROP_CHAVE = "k";
    private JWTVerifier verificador;
    private Algorithm algoritimo;
    private static String secret = null;

    public void reload() {
        secret = null;
        algoritimo = Algorithm.HMAC256(secret());
        verificador = JWT.require(algoritimo).withIssuer(EMITENTE).build();
    }

    public String secret() {
        if (UteisValidacao.emptyString(secret)) {
            secret = Uteis.readLineByLineJava8(Aplicacao.getProp("AUTH_SECRET_PATH"));
        }
        if (UteisValidacao.emptyString(secret)) {
            Uteis.logarDebug("Secret está vazia!");
        }
        return secret;
    }

    @Override
    public PersonaDTO validarRecuperandoPersona(String token) throws TokenInvalidoException, TokenExpiradoException {
        this.algoritimo = Algorithm.HMAC256(secret());
        this.verificador = JWT.require(algoritimo).withIssuer(EMITENTE).build();

        DecodedJWT decodedJWT = validarToken(token);
        return new PersonaDTO(new JSONObject(getClaim(String.class, decodedJWT, CLAIM_CONTENT)));
    }

    public PersonaDTO obterChaveRequisicao(String token) throws TokenInvalidoException, TokenExpiradoException{
        DecodedJWT decodedJWT;
        try {
            decodedJWT = JWT.decode(token);
        } catch (Exception e) {
            throw new TokenInvalidoException(e);
        }
        String chave;
        PersonaDTO personaDTO = new PersonaDTO();
        try {
            JSONObject tokenDecode = getClaimOld(decodedJWT);
            chave = tokenDecode.getString("chave");
        }catch (Exception e){
            JSONObject content = new JSONObject(getClaim(String.class, decodedJWT, CLAIM_CONTENT));
            chave = content.optString(PROP_CHAVE);
        }
        personaDTO.setChave(chave);
        return personaDTO;
    }

    public JSONObject getClaimOld(DecodedJWT decodedJWT) {
        Map<String, Claim> claims = decodedJWT.getClaims();
        JSONObject result = new JSONObject();
        for (Map.Entry claim : claims.entrySet()) {
            result.put(claim.getKey().toString(), claims.get(claim.getKey()).as(String.class));
        }
        return result;
    }

    public DecodedJWT validarToken(String token) throws TokenInvalidoException, TokenExpiradoException {
        try {
            return verificador.verify(token);
        } catch (TokenExpiredException e) {
            throw new TokenExpiradoException(e);
        } catch (JWTVerificationException e) {
            throw new TokenInvalidoException(e);
        }
    }


    public <T> T getClaim(Class<T> tipo, DecodedJWT decodedJWT, String claimCode){
        Claim claim = decodedJWT.getClaims().get(claimCode);
        return claim == null ? null : claim.as(tipo);
    }
}
