package br.com.pacto.security.interfaces;

import br.com.pacto.security.dtos.PersonaDTO;
import br.com.pacto.security.exceptions.TokenExpiradoException;
import br.com.pacto.security.exceptions.TokenInvalidoException;
import com.auth0.jwt.interfaces.DecodedJWT;

/**
 * Contrato para as regras de negócio de um token
 *
 */
public interface LeituraTokenServico {


    /**
     * Através do TOKEN informado, recupera as informações do usuário
     *
     * @param token TOKEN gerado previamente pela API
     * @throws TokenInvalidoException Caso ocorra algum problema na validação ou na recuperação
     */
    PersonaDTO validarRecuperandoPersona(String token) throws TokenInvalidoException, TokenExpiradoException;

    PersonaDTO obterChaveRequisicao(String token) throws TokenInvalidoException, TokenExpiradoException;

    DecodedJWT validarToken(String token) throws TokenInvalidoException, TokenExpiradoException;

}
