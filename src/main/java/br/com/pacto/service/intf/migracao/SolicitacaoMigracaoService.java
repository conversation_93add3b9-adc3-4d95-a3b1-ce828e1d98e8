package br.com.pacto.service.intf.migracao;


import br.com.pacto.bean.migracao.enums.StatusSolicitacaoMigracao;
import br.com.pacto.bean.migracao.enums.TipoRegistroMigracao;
import br.com.pacto.bean.migracao.enums.TipoSolicitacaoMigracao;
import br.com.pacto.bean.migracao.modelo.ConfiguracaoImportacao;
import br.com.pacto.bean.migracao.modelo.SolicitacaoMigracao;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Descrição: Contrato para {@link br.com.pacto.service.impl.migracao.SolicitacaoMigracaoServiceImpl}
 * Projeto: NewOAMD
 *
 * <AUTHOR> - rafaeljose em 17/abr/2018 às 17:05
 * Pacto Soluções - Todos os direitos reservados
 */
public interface SolicitacaoMigracaoService {
    SolicitacaoMigracao criarSolicitacaoMigracao(ConfiguracaoImportacao configuracaoImportacao, TipoSolicitacaoMigracao tipoSolicitacaoMigracao, TipoRegistroMigracao tipoRegistroMigracao);
    void atualizarDescricao(SolicitacaoMigracao solicitacaoMigracao, String descricao);
    void atualizarStatus(SolicitacaoMigracao solicitacaoMigracao, StatusSolicitacaoMigracao status);
    void finalizar(SolicitacaoMigracao solicitacaoMigracao);
    boolean existeSolicitacaoEmAndamento(ConfiguracaoImportacao configuracaoImportacao);
    List<SolicitacaoMigracao> listar(ConfiguracaoImportacao configuracaoImportacao, TipoSolicitacaoMigracao tipo);
}
