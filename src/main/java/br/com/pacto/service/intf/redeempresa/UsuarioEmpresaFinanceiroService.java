package br.com.pacto.service.intf.redeempresa;

import br.com.pacto.bean.empresa.redeempresa.UsuarioEmpresaFinanceiro;

import java.util.List;

/**
 * Created by <PERSON> on 10/03/2017.
 */
public interface UsuarioEmpresaFinanceiroService {

    public static final String SERVICE_NAME = "UsuarioEmpresaFinanceiroService";

    public List<UsuarioEmpresaFinanceiro> obterEmpresasRedeUsuario(Integer usuarioRedeEmpresa) throws Exception;

    public UsuarioEmpresaFinanceiro obterPorUsuarioRede(Integer usuarioRedeEmpresa,String chave) throws Exception;

    public UsuarioEmpresaFinanceiro incluir(UsuarioEmpresaFinanceiro obj) throws Exception;

    public void deletePorUsuarioRede(Integer codigoUsuarioRede) throws Exception;

    public UsuarioEmpresaFinanceiro update(UsuarioEmpresaFinanceiro obj) throws Exception;

    public void delete(UsuarioEmpresaFinanceiro obj) throws Exception;

    public String gerarSenhaTemporaria(String email, final Boolean enviarSMS) throws Exception;

    public String gravarNovaSenha(String email, String senha, String senhaAntiga) throws Exception;
}
