package br.com.pacto.service.intf.testeProducao;

import br.com.pacto.bean.testeProducao.StatusTesteDiario;
import br.com.pacto.bean.testeProducao.TesteServicoEnum;
import br.com.pacto.service.exception.ServiceException;
import java.util.Date;
import java.util.List;

/**
 * Created by fabio on 04/08/2016.
 */
public interface StatusTesteDiarioService {

    public static final String SERVICE_NAME = "statusTesteDiarioService";

    public StatusTesteDiario inserir(StatusTesteDiario object) throws ServiceException;

    public StatusTesteDiario alterarPorServicoEData(StatusTesteDiario object) throws ServiceException;

    public Boolean existeRegistroParaOServicoHoje(StatusTesteDiario object) throws ServiceException;

    public void eliminarStatusComMaisDeTrintaDias() throws ServiceException;
    
    public List<StatusTesteDiario> listarComDataDeHoje();
    
    public List<StatusTesteDiario> listarTodos();
}
