package br.com.pacto.service.intf.infra;

import br.com.pacto.bean.empresa.InfoInfraDTO;
import br.com.pacto.controller.json.infra.dto.InfraStatusDTO;

/**
 * <AUTHOR>
 * @since 12/02/19
 */
public interface InfraStatusService {

    void registrarInfraStart(InfraStatusDTO infraStatusDTO) throws Exception;

    void registrarInfraStop(InfraStatusDTO infraStatusDTO) throws Exception;

    InfoInfraDTO findInfraByName(String name);
}
