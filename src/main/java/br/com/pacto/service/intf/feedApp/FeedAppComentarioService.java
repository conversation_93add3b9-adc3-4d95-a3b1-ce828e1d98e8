/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.feedApp;

import br.com.pacto.bean.feedApp.FeedAppComentario;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONArray;

import java.util.List;

/*
 * <AUTHOR>
 */
public interface FeedAppComentarioService {

    public static final String SERVICE_NAME = "FeedAppComentarioService";

    FeedAppComentario obterPorId(Integer id) throws ServiceException;

    FeedAppComentario inserir(FeedAppComentario object) throws ServiceException;

    FeedAppComentario alterar(FeedAppComentario object) throws ServiceException;

    void excluir(FeedAppComentario object) throws ServiceException;

}
