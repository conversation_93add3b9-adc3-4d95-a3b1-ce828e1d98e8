package br.com.pacto.service.intf.utilitarias;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.utilitarias.LogProcesso;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;

public interface LogProcessoService {

    LogProcesso inserir(LogProcesso logProcesso) throws ServiceException;

    LogProcesso inserir(Empresa e, int idEmpresa, Usuario usuario, String acao, String resultado, Date dataAlteracao) throws ServiceException;

    void obterPor(Empresa e) throws Exception;
}
