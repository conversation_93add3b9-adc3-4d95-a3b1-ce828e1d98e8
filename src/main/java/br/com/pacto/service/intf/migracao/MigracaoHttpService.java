package br.com.pacto.service.intf.migracao;

import br.com.pacto.bean.migracao.modelo.RegistroMigracao;
import br.com.pacto.bean.migracao.json.RespostaExportacaoRegistroMigracaoJSON;

import java.util.List;
import java.util.Map;

/**
 * Descrição: Contrato para {@link br.com.pacto.service.impl.migracao.MigracaoHttpServiceImpl}
 * Projeto: NewOAMD
 *
 * <AUTHOR> - rafaeljose em 24/abr/2018 às 15:57
 * Pacto Soluções - Todos os direitos reservados
 */
public interface MigracaoHttpService {
    RespostaExportacaoRegistroMigracaoJSON exportarRegistroMigracao(RegistroMigracao registroMigracao);
    RespostaExportacaoRegistroMigracaoJSON excluirRegistroMigracaoZW(RegistroMigracao registroMigracao);
}
