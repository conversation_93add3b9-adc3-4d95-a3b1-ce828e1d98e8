package br.com.pacto.service.intf.migracao;

import br.com.pacto.bean.migracao.modelo.ConfiguracaoImportacao;
import br.com.pacto.bean.migracao.modelo.RegistroMigracao;
import br.com.pacto.bean.migracao.enums.TipoRegistroMigracao;
import br.com.pacto.bean.migracao.modelo.SolicitacaoMigracao;
import java.util.List;

/**
 * Descrição: Contrato para {@link br.com.pacto.service.impl.migracao.MigracaoServiceImpl}
 * Projeto: NewOAMD
 *
 * <AUTHOR> - rafaeljose em 17/abr/2018 às 17:05
 * Pacto Soluções - Todos os direitos reservados
 */
public interface MigracaoService {
    void exportarRegistroMigracao(RegistroMigracao registroMigracao);
    void migrarRegistros(SolicitacaoMigracao solicitacaoMigracao);
    void excluirRegistroMigracao(RegistroMigracao registroMigracao);
    void excluirRegistros(List<RegistroMigracao> registrosMigracao, SolicitacaoMigracao solicitacaoMigracao);
}
