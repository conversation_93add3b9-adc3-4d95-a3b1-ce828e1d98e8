package br.com.pacto.service.intf.migracao;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.migracao.modelo.ConfiguracaoImportacao;

/**
 * Descrição: Expõe as funcionalidades do serviço {@link br.com.pacto.service.impl.migracao.ConfiguracaoImportacaoServiceImpl}
 * Projeto: NewOAMD
 *
 * <AUTHOR> - rafael<PERSON>e em 13/abr/2018 às 16:03
 * Pacto Soluções - Todos os direitos reservados
 */
public interface ConfiguracaoImportacaoService {
    ConfiguracaoImportacao consultar(Empresa empresa, Integer codigoEmpresa) throws Exception;
    void salvar(ConfiguracaoImportacao configuracaoImportacao) throws Exception;
}
