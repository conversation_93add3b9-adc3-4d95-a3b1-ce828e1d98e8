package br.com.pacto.service.intf.zw;

import br.com.pacto.bean.zw.TabelaZwJson;
import br.com.pacto.bean.zw.TipoTabelaZwJson;

import java.util.List;

/**
 * Created by ulisses on 10/11/2016.
 */
public interface TabelaZwJsonService {

    List<TabelaZwJson> consultar(String nomeTabela, TipoTabelaZwJson tipoTabela) throws Exception;
    void incluir(String jsonArrayTabelasZW, String nomeUsuario, TipoTabelaZwJson tipoTabela)throws Exception;
    void incluir(String json, String nomeTabela, String nomeUsuario, TipoTabelaZwJson tipoTabela)throws Exception;
    void excluirTodos(TipoTabelaZwJson tipoTabela)throws Exception;

}
