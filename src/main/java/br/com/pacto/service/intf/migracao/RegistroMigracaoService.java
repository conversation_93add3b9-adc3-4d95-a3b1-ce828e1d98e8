package br.com.pacto.service.intf.migracao;

import br.com.pacto.bean.migracao.dto.FiltroRegistroMigracao;
import br.com.pacto.bean.migracao.modelo.RegistroMigracao;

import java.util.List;

/**
 * Descrição: Contrato para {@link br.com.pacto.service.impl.migracao.RegistroMigracaoServiceImpl}
 * Projeto: NewOAMD
 *
 * <AUTHOR> - 19/abr/2018 às 15:59
 * Pacto Soluções - Todos os direitos reservados
 */
public interface RegistroMigracaoService {
    List<RegistroMigracao> listar(FiltroRegistroMigracao filtro);
    int contar(FiltroRegistroMigracao filtro);
    RegistroMigracao consultar(RegistroMigracao registroMigracao);
}
