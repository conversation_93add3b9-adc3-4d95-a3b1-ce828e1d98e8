/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.token;

import br.com.pacto.bean.token.Token;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/*
 * <AUTHOR>
 */
public interface TokenService {

    public static final String SERVICE_NAME = "TokenService";

    Token obterPorId(Integer id) throws ServiceException;

    Token inserir(Token object) throws ServiceException;

    Token inserirGerandoToken(Token object) throws ServiceException;

    Token alterar(Token object) throws ServiceException;

    void excluir(Token object) throws ServiceException;

    Token consultarPorToken(String token) throws ServiceException;

    void gerarToken(Token token) throws ServiceException;

}
