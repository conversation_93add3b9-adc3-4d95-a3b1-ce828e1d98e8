package br.com.pacto.service.intf.infoMigracao;

import br.com.pacto.bean.infoMigracao.InfoMigracaoLog;
import br.com.pacto.bean.infoMigracao.MigracaoRecurso;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;

public interface MigracaoRecursoService {

    public static final String SERVICE_NAME = "MigracaoRecursoService";

    MigracaoRecurso inserir(MigracaoRecurso object) throws ServiceException;

    public MigracaoRecurso save(MigracaoRecurso object) throws ServiceException;

    MigracaoRecurso obterPorId(Integer id) throws ServiceException;

    List<MigracaoRecurso> consultarTodos(Boolean ativo) throws ServiceException;

    Date obterDataMigracao(String chave, String recurso) throws Exception;

}
