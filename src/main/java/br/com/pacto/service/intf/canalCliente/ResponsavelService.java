package br.com.pacto.service.intf.canalCliente;

import br.com.pacto.bean.canalCliente.Responsavel;

import java.util.List;

public interface ResponsavelService {
    String SERVICE_NAME = "ResponsavelService";

    List consultarPorChave(String chave) throws Exception;

    Responsavel obterPorCodigo(Integer codigo) throws Exception;

    List<Responsavel> alterarResponsaveis(List<Responsavel> responsaveis) throws Exception;

    List<Responsavel> consultarPorEmpresaFinanceiro(Integer empresaFinanceiro);
}
