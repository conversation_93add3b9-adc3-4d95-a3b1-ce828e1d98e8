package br.com.pacto.service.intf.aplicativo;

import br.com.pacto.bean.aplicativo.Aplicativo;

import java.util.List;

/**
 * Serviço que trata os dados de negócio dos aplicativos
 *
 * <AUTHOR>
 * @since 22/09/2018
 */
public interface AplicativoService {

    /**
     * Calcula todos os aplicativos cadastrados
     *
     * @return A lista com todos os aplicativos
     */
    List<Aplicativo> obterTodos() throws Exception;


    List<Aplicativo> obterTodos(String nome) throws Exception;

    /**
     * Persiste um aplicativo no banco de dados
     *
     * @param aplicativo Aplicativo que será persistido
     * @throws Exception Caso ocorra algum problema na inserção do aplicativo
     */
    void salvar(Aplicativo aplicativo) throws Exception;

    /**
     * Remove um aplicativo da base de dados
     *
     * @param aplicativo Aplicativo que será removido
     * @throws Exception Caso ocorra algum problema ao remover o aplicativo
     */
    void excluir(Aplicativo aplicativo) throws Exception;

}
