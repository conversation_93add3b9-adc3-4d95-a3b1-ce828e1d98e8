package br.com.pacto.service.intf.aplicativo;

import br.com.pacto.bean.aplicativo.EmpresaAppUnificado;

/**
 * <AUTHOR> on 13/11/18.
 * @project OAMD_TRONCO
 */
public interface AplicativoEmpresaService {

    void salvar(EmpresaAppUnificado obj) throws Exception;

    EmpresaAppUnificado findById(Integer id) throws Exception;

    EmpresaAppUnificado findByChave(String chave) throws Exception;

    void update(EmpresaAppUnificado obj) throws Exception;
}
