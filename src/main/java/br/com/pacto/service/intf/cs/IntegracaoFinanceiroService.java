/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.cs;

import br.com.pacto.bean.cs.IntegracaoFinanceiro;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONArray;

/**
 * <AUTHOR>
 */
public interface IntegracaoFinanceiroService {
    String SERVICE_NAME = "IntegracaoFinanceiroService";

    void atualizarEmpresasFinanceiro(final JSONArray empresas) throws ServiceException;

    void atualizarAtivosVencidos(final String chave, final int codigoEmpresa,
                                 final int ativos, final int vencidos,
                                 int agregadores, int agregadores2, int agregadores3, int agregadores4, int agregadores5,
                                 int checkinsAgregadores) throws ServiceException;

    IntegracaoFinanceiro buscarPorChaveCodigoEmpresa(String chave, Integer codigoEmpresa) throws Exception;
}
