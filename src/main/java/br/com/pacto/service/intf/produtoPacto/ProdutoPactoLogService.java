package br.com.pacto.service.intf.produtoPacto;

import br.com.pacto.bean.produtoPacto.ProdutoPactoCompra;
import br.com.pacto.bean.produtoPacto.ProdutoPactoLog;
import br.com.pacto.controller.json.produtoPacto.EmpresaDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 09/06/2020
 */
public interface ProdutoPactoLogService {

    public static final String SERVICE_NAME = "ProdutoPactoLogService";

    void logBaixa(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, String operacao, String log);

    void logAlta(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, String operacao, String log);

    void log(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, String operacao, String log);

    List<ProdutoPactoLog> consultar(Boolean resolvido) throws ServiceException;
}
