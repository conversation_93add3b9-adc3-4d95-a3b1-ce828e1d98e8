/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.envioprotheus;

import br.com.pacto.bean.envioprotheus.EnvioProtheus;
import br.com.pacto.bean.envioprotheus.StatusEnvioEnum;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface EnvioProtheusService {
 
    public static final String SERVICE_NAME = "EnvioProtheusService";
    
    public EnvioProtheus incluir(EnvioProtheus envio) throws Exception;
    
    public List<EnvioProtheus> historico(Integer limit, StatusEnvioEnum ... status) throws Exception;
    
    public Integer obterNumeroEnvios(StatusEnvioEnum envio) throws Exception;
    
    public void alterarStatus(String chavedts, StatusEnvioEnum status, String msg) throws Exception;
}
