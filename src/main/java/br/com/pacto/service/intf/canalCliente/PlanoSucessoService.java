package br.com.pacto.service.intf.canalCliente;

import br.com.pacto.bean.canalCliente.PlanoSucesso;
import br.com.pacto.controller.json.canalCliente.PlanoSucessoDTO;
import br.com.pacto.controller.json.canalCliente.PlanoSucessoInicioDTO;
import br.com.pacto.controller.utils.PaginadorDTO;
import org.json.JSONObject;

import java.util.List;

public interface PlanoSucessoService {
    String SERVICE_NAME = "PlanoSucessoService";

    List consultarPorChave(String chave) throws Exception;

    List consultarTodos() throws Exception;

    PlanoSucesso obterPorCodigo(Integer codigo) throws Exception;

    List<PlanoSucesso> consultarPlanosSucessoRealizadosPorChave(String chave) throws Exception;

    PlanoSucesso iniciar(PlanoSucessoInicioDTO planoSucessoInicioDTO) throws Exception;

    IndicadorPlanoSucessoDTO obterIndicadorPlanosSucessoConcluidosPorChave(String chave, Integer meses);

    List<PlanoSucesso> consultarConcluidosPorChave(String chave, PaginadorDTO paginadorDTO, JSONObject filtros);

    List<PlanoSucesso> consultarConcluidosPorCodFinanceiro(Integer codFinanceiro, PaginadorDTO paginadorDTO, JSONObject filtros);

    List<PlanoSucesso> consultarEmAndamentoPorChave(String chave, PaginadorDTO paginadorDTO, JSONObject filtros);

    List<PlanoSucesso> consultarEmAndamentoPorCodFinanceiro(Integer codFinanceiro, PaginadorDTO paginadorDTO, JSONObject filtros);

    PlanoSucesso atualizar(Integer id, PlanoSucessoDTO planoSucessoDTO);

    void atualizarAndamento(PlanoSucesso planoSucesso);

    boolean deletarPlanoSucesso(Integer id, final String chave) throws Exception;
}
