package br.com.pacto.service.intf.infoMigracao;

import br.com.pacto.bean.infoMigracao.MigracaoRecursoFeedback;
import br.com.pacto.controller.json.infoMigracao.MigracaoRecursoFeedbackDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface MigracaoRecursoFeedbackService {

    public static final String SERVICE_NAME = "MigracaoRecursoFeedbackService";

    MigracaoRecursoFeedback inserir(MigracaoRecursoFeedback object) throws ServiceException;

    MigracaoRecursoFeedback obterPorId(Integer id) throws ServiceException;

    List<MigracaoRecursoFeedback> consultarTod<PERSON>(boolean somenteComFeedback) throws ServiceException;

    MigracaoRecursoFeedback feedback(String auth, MigracaoRecursoFeedbackDTO dto) throws ServiceException;

    Integer total() throws ServiceException;
}
