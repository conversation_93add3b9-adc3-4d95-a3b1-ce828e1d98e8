package br.com.pacto.service.intf.redeempresa;

import br.com.pacto.bean.empresa.redeempresa.RedeEmpresa;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.controller.json.empresa.FiltroPlanoRedeEmpresaJSON;
import br.com.pacto.dto.ConfiguracaoTreinoRedeEmpresaDTO;
import br.com.pacto.dto.PlanoRedeEmpresaDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.empresa.ClientDiscoveryDataDTO;

import java.util.Date;
import java.util.List;

/**
 * Created by Rafael on 10/03/2017.
 */
public interface RedeEmpresaService {

    static final String SERVICE_NAME = "redeEmpresaService";

    RedeEmpresa inserir(RedeEmpresa redeEmpresa) throws Exception;

    RedeEmpresa alterar(RedeEmpresa redeEmpresa) throws Exception;

    void excluir(RedeEmpresa redeEmpresa) throws Exception;

    RedeEmpresa obterPorId(Integer id) throws Exception;

    RedeEmpresa obterPorChaveRede(String chaveRede) throws Exception;

    List<RedeEmpresa> obterTodos() throws Exception;

    RedeEmpresa gravarCreditoRedeDCC(RedeEmpresa redeEmpresa,
                                     EmpresaJSON rede,
                                     Integer qtdCreditosAdd,
                                     String usuario,
                                     String justificativa,
                                     Boolean gerarCobrancaPacto,
                                     Double valortotal,
                                     Date dataPrimeiraParcela,
                                     Boolean gravarCreditos,
                                     Integer codEmpresaFinanceiroCobranca) throws Exception;

    String debitarDCCRede(final String chave, final Integer codigoEmpresa, final Integer debito) throws Exception;

    String addDCCRede(final String chave) throws Exception;

    List<RedeEmpresa> obterRedesApp(String nome) throws Exception;

    RedeEmpresa consultarPorChaveZWOAMD(String chaveZW) throws Exception;

    List<EmpresaFinanceiro> consultarUnidadesDaRedeOAMD(RedeEmpresa redeEmpresa) throws Exception;

    String limpaCacheRede() throws ServiceException;

    ClientDiscoveryDataDTO obterUrls(String key) throws ServiceException;

    ClientDiscoveryDataDTO obterUrls(final String key, boolean rede) throws ServiceException;

    Boolean integranteRedeEmpresa(String chave) throws ServiceException;

    List obterChavesPorChaveRede(String chaveRede) throws ServiceException;

    List<PlanoRedeEmpresaDTO> obterEmpresasReplicarPlano(String chaveOrigem, Integer planoOrigem, FiltroPlanoRedeEmpresaJSON filters, String token) throws ServiceException;

    List<ConfiguracaoTreinoRedeEmpresaDTO> obterEmpresasReplicarConfiguracaoTreino(String chaveOrigem, String filters) throws ServiceException;

}
