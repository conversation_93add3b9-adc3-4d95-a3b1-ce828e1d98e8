package br.com.pacto.service.intf.infoMigracao;

import br.com.pacto.bean.infoMigracao.InfoMigracaoLog;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface InfoMigracaoService {

    public static final String SERVICE_NAME = "InfoMigracaoService";

    InfoMigracaoLog inserir(InfoMigracaoLog object) throws ServiceException;

    InfoMigracaoLog obterPorId(Integer id) throws ServiceException;

    List<InfoMigracaoLog> consultarTodos() throws ServiceException;
}
