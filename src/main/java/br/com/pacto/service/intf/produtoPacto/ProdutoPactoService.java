package br.com.pacto.service.intf.produtoPacto;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.PremiumConfigs;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.bean.produtoPacto.ProdutoPacto;
import br.com.pacto.controller.json.produtoPacto.CompraDiretaSMSDTO;
import br.com.pacto.controller.json.produtoPacto.ComprarProdutoPactoDTO;
import br.com.pacto.controller.json.produtoPacto.EmpresaUsoProdutoPactoDTO;
import br.com.pacto.controller.json.produtoPacto.ProdutoPactoDTO;
import br.com.pacto.controller.json.produtoPacto.ResultadoCompraDTO;
import br.com.pacto.enums.TipoProdutoPacto;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProdutoPactoService {

    public static final String SERVICE_NAME = "ProdutoPactoService";

    ProdutoPacto inserir(ProdutoPacto object) throws ServiceException;

    ProdutoPacto alterar(ProdutoPacto object) throws ServiceException;

    void excluir(ProdutoPacto object) throws ServiceException;

    List<ProdutoPacto> obterTodos() throws ServiceException;

    ProdutoPacto obterPorId(Integer id) throws ServiceException;

    List<ProdutoPacto> consultar(TipoProdutoPacto tipo, Boolean ativo) throws ServiceException;

    ResultadoCompraDTO comprarProdutoPacto(ComprarProdutoPactoDTO compraDTO) throws ServiceException;

    EmpresaUsoProdutoPactoDTO verificarModulosRecursosUtilizados(Empresa empresaOAMD, EmpresaFinanceiro empFinan,
                                                                 Integer codigoFinanceiro, boolean consultarSaldoSMS) throws ServiceException;

    void processarStatusModuloRecurso(Integer codigoFinanceiro, ProdutoPacto produtoPacto,
                                      ProdutoPactoDTO dto, EmpresaUsoProdutoPactoDTO empresaDTO) throws ServiceException;

    PremiumConfigs configsPremium() throws Exception;
}
