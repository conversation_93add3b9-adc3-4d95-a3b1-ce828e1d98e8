package br.com.pacto.service.intf.produtoPacto;

import br.com.pacto.bean.produtoPacto.ProdutoPactoCupomDesconto;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ProdutoPactoCupomDescontoService {

    public static final String SERVICE_NAME = "produtoPactoCupomDescontoService";

    ProdutoPactoCupomDesconto inserir(ProdutoPactoCupomDesconto object) throws ServiceException;

    ProdutoPactoCupomDesconto alterar(ProdutoPactoCupomDesconto object) throws ServiceException;

    void excluir(ProdutoPactoCupomDesconto object) throws ServiceException;

    List<ProdutoPactoCupomDesconto> obterTodos(boolean consultarUtilizado) throws ServiceException;

    ProdutoPactoCupomDesconto obterPorId(Integer id) throws ServiceException;

    ProdutoPactoCupomDesconto obterPorCupomDesconto(String cupom) throws ServiceException;
}
