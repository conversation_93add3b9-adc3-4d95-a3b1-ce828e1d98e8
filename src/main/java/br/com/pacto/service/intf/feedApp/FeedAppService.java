/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.feedApp;

import br.com.pacto.bean.feedApp.FeedApp;
import br.com.pacto.controller.json.feedApp.FeedAppJSON;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;

/*
 * <AUTHOR>
 */
public interface FeedAppService {

    public static final String SERVICE_NAME = "FeedAppService";

    FeedApp obterPorId(Integer id) throws ServiceException;

    FeedApp inserir(FeedApp object) throws ServiceException;

    FeedApp alterar(FeedApp object) throws ServiceException;

    void excluir(FeedApp object) throws ServiceException;

    void excluirComRelacionamentos(FeedApp object) throws ServiceException;

    List<FeedApp> consultarPublicacoes(String chave, Integer index, Integer maxResults, boolean especifico, String email) throws ServiceException;

    Integer qtdComentariosPorFeedApp(FeedApp feedApp) throws ServiceException;

    Integer qtdLikesPorFeedApp(FeedApp feedApp) throws ServiceException;

    void preencherListaLikes(FeedApp feedApp) throws ServiceException;

    void preencherListaComentarios(FeedApp feedApp, Integer maxComments) throws ServiceException;

    List<FeedAppJSON> consultarPublicacoesNutri(Date dataInicio, Date dataFinal) throws ServiceException;

}
