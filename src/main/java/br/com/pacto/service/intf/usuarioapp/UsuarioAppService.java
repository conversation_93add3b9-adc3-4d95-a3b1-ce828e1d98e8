/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.usuarioapp;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.feed.usuarioapp.UsuarioApp;
import br.com.pacto.json.UsuarioAppBasicoJSON;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface UsuarioAppService {
    
    public static final String SERVICE_NAME = "UsuarioAppService";
    
    public UsuarioApp inserir(UsuarioApp object) throws ServiceException;

    public UsuarioApp obterPorEmail(String email) throws ServiceException;

    public UsuarioApp alterar(UsuarioApp object) throws ServiceException;
    
    public JSONArray obterListaPorChave(String chave) throws Exception;

    void alterarNomeFotoToken(UsuarioApp usuarioApp) throws ServiceException;
    
    public List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosZW(Empresa e,
            final String celular, final String email) throws ServiceException;
    public List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosByCelularZW(Empresa e, final String celular) throws ServiceException;

    public List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosByCelularApp(Empresa e, final String ddi,
            final String ddd, final String celular) throws ServiceException;

    List<UsuarioAppBasicoJSON> verificarDadosBasicosByCelularColaborador(Empresa e, final String ddi,
            final String ddd, final String celular) throws ServiceException;

    void executeNativeSQL(String sql) throws ServiceException;
    
    UsuarioApp gerarUsuario(final String ctx, final String email, String cpf, String telefone, String dataNascimento, String senha) throws ServiceException;

    List<UsuarioApp> descobrirUsuarioV3(String parametro) throws ServiceException;

    Boolean obterSituacaoUsuario(String key, Empresa empresa, Integer codEmpresa, Integer codUsuario);

    List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosByCelularTreinoIndepApp(Empresa e, String ddi, String ddd,
                                                                                      String celular) throws ServiceException;

    String gerarUsuarioTreinoIndependente(Empresa e, String email, String senha, Integer codigoColaborador, Integer codigoCliente);

    List<UsuarioAppBasicoJSON> verificarUsuarioEmail(Empresa e, String email) throws ServiceException;

    List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosByEmailTreinoIndepApp(Empresa e, String email) throws ServiceException;

}
