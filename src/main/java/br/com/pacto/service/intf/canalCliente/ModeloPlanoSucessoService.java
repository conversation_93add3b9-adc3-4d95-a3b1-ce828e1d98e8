package br.com.pacto.service.intf.canalCliente;

import br.com.pacto.bean.canalCliente.ModeloPlanoSucesso;
import br.com.pacto.controller.utils.PaginadorDTO;
import org.json.JSONObject;

import java.util.List;

public interface ModeloPlanoSucessoService {
    public static final String SERVICE_NAME = "ModeloPlanoSucessoService";

    List<ModeloPlanoSucesso> consultarPlanosSucessoPorChave(String chave) ;

    List<ModeloPlanoSucesso> consultarPlanosSucessoPorChave(String chave, PaginadorDTO paginadorDTO, JSONObject filtros) throws Exception;

    List consultarTodos() throws Exception;

    ModeloPlanoSucesso incluir(ModeloPlanoSucesso plano) throws Exception;

    ModeloPlanoSucesso obterPorCodigo(Integer codigo);

    void excluir(ModeloPlanoSucesso modeloPlanoSucesso) throws Exception;

    ModeloPlanoSucesso atualizar(ModeloPlanoSucesso modeloPlanoSucesso) throws Exception;

    ModeloPlanoSucesso alterar(ModeloPlanoSucesso modelo) throws Exception;
}
