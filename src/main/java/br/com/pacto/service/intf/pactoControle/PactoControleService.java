package br.com.pacto.service.intf.pactoControle;

import org.json.JSONArray;
import org.json.JSONObject;

public interface PactoControleService {

    JSONArray consultarInfoFinanceiras(String key, Integer codEmpresa) throws Exception;

    String downloadBoleto(String key, Integer codigoBoleto, String dataVencimento) throws Exception;

    JSONArray solicitacoesConcluida(String key, String email) throws Exception;

    JSONArray solicitacoes<PERSON>m<PERSON><PERSON>(String key, String email) throws Exception;

    JSONObject consultarEstastisticasSolicitacoes(String key, String email) throws Exception;

    JSONObject consultarEstastisticasTicket(String key, String id) throws Exception;

    boolean checkFree();

    void toggleFreeApis(boolean free);
}
