/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.facilite;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.service.impl.facilite.EstatisticaFaciliteTO;
import br.com.pacto.service.impl.facilite.InfoParcelaFaciliteTO;
import br.com.pacto.service.impl.facilite.SituacaoRemessaFaciliteEnum;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface FaciliteService {
    
    public static final String SERVICE_NAME = "FaciliteService";
        
    public List<EstatisticaFaciliteTO> obterEmpresasFacilite() throws Exception;
    
    public EstatisticaFaciliteTO estatisticasFacilite(List<EstatisticaFaciliteTO> empresas, Date diaLimite) throws Exception;

    public List<InfoParcelaFaciliteTO> detalhar(EstatisticaFaciliteTO empresa, SituacaoRemessaFaciliteEnum situacao) throws Exception;
    
}
