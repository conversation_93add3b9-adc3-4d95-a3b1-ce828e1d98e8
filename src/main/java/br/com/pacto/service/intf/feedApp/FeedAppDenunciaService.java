/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.feedApp;

import br.com.pacto.bean.feedApp.FeedAppDenuncia;
import br.com.pacto.service.exception.ServiceException;

/*
 * <AUTHOR>
 */
public interface FeedAppDenunciaService {

    public static final String SERVICE_NAME = "FeedAppDenunciaService";

    FeedAppDenuncia obterPorId(Integer id) throws ServiceException;

    FeedAppDenuncia inserir(FeedAppDenuncia object) throws ServiceException;

    FeedAppDenuncia alterar(FeedAppDenuncia object) throws ServiceException;

    void excluir(FeedAppDenuncia object) throws ServiceException;

}
