package br.com.pacto.service.intf.canalCliente;

import br.com.pacto.bean.canalCliente.PlanoSucessoAcao;
import br.com.pacto.controller.json.canalCliente.PlanoSucessoAcaoDTO;
import br.com.pacto.controller.utils.PaginadorDTO;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import java.util.List;

public interface PlanoSucessoAcaoService {
    public static final String SERVICE_NAME = "PlanoSucessoAcaoService";

    List<PlanoSucessoAcao> consultarPorChave(String chave);

    List<PlanoSucessoAcao> consultarPorPlanoSucesso(final String chave, final Integer idPlanoSucesso, PaginadorDTO paginadorDTO, JSONObject filtros) throws ServiceException;

    PlanoSucessoAcao obterPorCodigo(Integer codigo) throws Exception;

    PlanoSucessoAcao alterar(Integer codigo, PlanoSucessoAcaoDTO planoSucessoAcaoDTO) throws Exception;

    PlanoSucessoAcao incluir(Integer idPlanoSucesso, PlanoSucessoAcaoDTO planoSucessoAcaoDTO) throws Exception;

    boolean deletar(Integer codigo) throws Exception;
}
