package br.com.pacto.service.intf.produtoPacto;

import br.com.pacto.bean.produtoPacto.ConfigProdutoPacto;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.configassinatura.PlanoDTO;
import br.com.pacto.service.impl.produtoPacto.ProdutoDTO;

import java.util.List;


/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 04/06/2020
 *
 */
public interface ConfigProdutoPactoService {

    String SERVICE_NAME = "ConfigProdutoPactoService";

    ConfigProdutoPacto salvar(ConfigProdutoPacto object, String usuario) throws ServiceException;

    List<EmpresaJSON> empresas(ConfigProdutoPacto config)throws ServiceException;

    List<PlanoDTO> planos(ConfigProdutoPacto config) throws ServiceException;

    List<ProdutoDTO> produtos(ConfigProdutoPacto config) throws ServiceException;

    ConfigProdutoPacto obterAtivo(String usuario) throws ServiceException;

}
