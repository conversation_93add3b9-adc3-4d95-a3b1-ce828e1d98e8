/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.feedApp;

import br.com.pacto.bean.feed.usuarioapp.UsuarioApp;
import br.com.pacto.bean.feedApp.FeedApp;
import br.com.pacto.bean.feedApp.FeedAppLike;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONArray;

import java.util.List;

/*
 * <AUTHOR>
 */
public interface FeedAppLikeService {

    public static final String SERVICE_NAME = "FeedAppLikeService";

    FeedAppLike obterPorId(Integer id) throws ServiceException;

    FeedAppLike inserir(FeedAppLike object) throws ServiceException;

    List<FeedAppLike> obterPorFeedAppUsuarioApp(FeedApp feedApp, UsuarioApp usuarioApp) throws ServiceException;

    FeedAppLike alterar(FeedAppLike object) throws ServiceException;

    void excluir(FeedAppLike object) throws ServiceException;

    boolean verificaUsuarioDeuLike(String email, FeedApp feedApp) throws ServiceException;

}
