package br.com.pacto.service.intf.produtoPacto;

import br.com.pacto.bean.produtoPacto.ProdutoPacto;
import br.com.pacto.bean.produtoPacto.ProdutoPactoAvaliacao;
import br.com.pacto.controller.json.produtoPacto.ProdutoPactoDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 10/06/2020
 */
public interface ProdutoPactoAvaliacaoService {

    public static final String SERVICE_NAME = "ProdutoPactoAvaliacaoService";

    ProdutoPactoAvaliacao inserir(ProdutoPactoAvaliacao object) throws ServiceException;

    ProdutoPactoAvaliacao alterar(ProdutoPactoAvaliacao object) throws ServiceException;

    void excluir(ProdutoPactoAvaliacao object) throws ServiceException;

    List<ProdutoPactoAvaliacao> obterTodos() throws ServiceException;

    ProdutoPactoAvaliacao obterPorId(Integer id) throws ServiceException;

    List<ProdutoPactoAvaliacao> consultar(ProdutoPacto produtoPacto, Integer limit) throws ServiceException;

    void preencherAvaliacoes(ProdutoPactoDTO dto) throws ServiceException;
}
