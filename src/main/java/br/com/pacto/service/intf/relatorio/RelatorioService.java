package br.com.pacto.service.intf.relatorio;

import br.com.pacto.dto.ClienteFrequenciaDTO;
import br.com.pacto.dto.ClientePaganteDTO;
import br.com.pacto.dto.ConsultaAtivosDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface RelatorioService {

    List<ClientePaganteDTO> obterPagantesSesi(final String chaveRede, final Integer ano, final Integer mes) throws ServiceException;

    List<ClienteFrequenciaDTO> obterFrequenciaSesi(final String chaveRede, final Integer ano, final Integer mes) throws ServiceException;

    List<ConsultaAtivosDTO> executarConsultaAtivosRede(final String chaveRede, final Integer ano, final Integer mes) throws ServiceException;
}
