package br.com.pacto.service.intf.utilitarias;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.utilitarias.LogBloqueio;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT on 29/06/2016
 */
public interface LogBloqueioService {

    LogBloqueio inserir(LogBloqueio object) throws ServiceException;

    LogBloqueio inserir(Empresa e, int idEmpresa, Usuario usuario, String acao, Date expiracao) throws ServiceException;

    LogBloqueio inserir(Empresa e, int idEmpresa, Usuario usuario, String acao, Date expiracao, boolean suspensao) throws ServiceException;

    void obterPor(Empresa e) throws Exception;
}
