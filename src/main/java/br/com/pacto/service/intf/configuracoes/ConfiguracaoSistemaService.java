/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.configuracoes;

import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracaoEnum;
import br.com.pacto.email.ConfigsEmail;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> on 02/02/2020.
 */
public interface ConfiguracaoSistemaService {

    public static final String SERVICE_NAME = "ConfiguracaoSistemaService";

    ConfiguracaoSistema inserir(ConfiguracaoSistema object) throws ServiceException;

    ConfiguracaoSistema obterPorId(Integer id) throws ServiceException;

    ConfiguracaoSistema alterar(ConfiguracaoSistema object) throws ServiceException;

    void refresh(ConfiguracaoSistema object) throws ServiceException;

    void excluir(ConfiguracaoSistema object) throws ServiceException;

    List<ConfiguracaoSistema> obterTodos() throws ServiceException;

    List<ConfiguracaoSistema> obterPorParam(String query, Map<String, Object> params) throws ServiceException;

    List<ConfiguracaoSistema> obterPorParam(String query, Map<String, Object> params, int max, int index) throws ServiceException;

    ConfiguracaoSistema obterObjetoPorParam(String query, Map<String, Object> params) throws ServiceException;

    ConfiguracaoSistema consultarPorConfiguracao(ConfiguracaoEnum cfg) throws ServiceException;

    String obterValorPorConfiguracao(ConfiguracaoEnum cfg) throws ServiceException;

    List<ConfiguracaoSistema> consultarPovoando() throws ServiceException;

    ConfigsEmail obterConfigEmailNoReply() throws Exception;

    ConfigsEmail obterConfigEmailPersonal() throws Exception;
}
