package br.com.pacto.service.intf.cs;

import br.com.pacto.bean.empresafinanceiro.CustomerSuccess;
import br.com.pacto.controller.utils.PaginadorDTO;
import br.com.pacto.dto.DetalheEmpresaDTO;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import java.util.List;

public interface CustomerSuccessService {

    List<CustomerSuccess> findAll() throws ServiceException;

    List<CustomerSuccess> findAll(PaginadorDTO paginadorDTO, JSONObject filtros) throws ServiceException;

    void save(CustomerSuccess obj) throws ServiceException;

    CustomerSuccess findById(Integer id) throws ServiceException;

    @Deprecated
    CustomerSuccess consultarPorDadosZWOAMD(String chaveZW, Integer codigoEmpresaZw) throws Exception;

    DetalheEmpresaDTO consultarDetalhesPorDadosZWOAMD(String chaveZW, Integer codigoEmpresaZw) throws Exception;
}
