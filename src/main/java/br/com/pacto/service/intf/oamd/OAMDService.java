/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.oamd;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.FlagSoftwareEnum;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.enums.ModuloBloqueioEnum;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface OAMDService {

    static final String SERVICE_NAME = "OAMDService";

    void carregarEmpresas(Empresa empresa, final Usuario u, boolean isTreino) throws ServiceException;

    int adicionarCreditoDCC(final Empresa e, final int quantidadeCreditos, final int idEmpresa) throws ServiceException;

    int removerCreditoDCC(final Empresa e, final int quantidadeCreditos, final int idEmpresa) throws ServiceException;

    String notificarEmpresaComBoletosNovos(final Empresa e, final int idEmpresa, final Usuario usuario) throws ServiceException;

    String removerNotificacaoEmpresaComBoletosNovos(final Empresa e, final int idEmpresa, Usuario usuario) throws ServiceException;

    String bloquearEmpresa(final Empresa e, final Date expiracao, final int idEmpresa, final Usuario usuario, final ModuloBloqueioEnum modulo) throws ServiceException;

    String desbloquearEmpresa(final Empresa e, final int idEmpresa, final Usuario usuario, final ModuloBloqueioEnum modulo, boolean removerSuspensao) throws ServiceException;

    String suspenderEmpresa(final Empresa e, final int idEmpresa, final Usuario usuario) throws ServiceException;

    void atualizarEmpresasFinanceiro(final JSONArray empresas) throws ServiceException;

    void atualizarAtivosVencidos(final String chave, final int codigoEmpresa,
                                 final int ativos, final int vencidos,
                                 int agregadores, int agregadores2, int agregadores3, int agregadores4, int agregadores5,
                                 int checkinsAgregadores) throws ServiceException;

    List<Empresa> consultarTodasEmpresas(List<Empresa> listaAtual, Usuario u) throws ServiceException;

    String bloquearCreditosDCC(final Empresa e, final Date expiracao, final int idEmpresa, final Usuario usuario) throws ServiceException;

    String desbloquearCreditosDCC(final Empresa e, final int idEmpresa, final Usuario usuario) throws ServiceException;

    String gravarPrePagoDCCLancarAgendamentoFinan(final Empresa e, final int idEmpresa, final int qtdCredito, final Integer qtdParcelas, final Double valorTotal, final boolean gerarNota, final String nomeUsuarioOAMD, final String justificativa, final boolean gerarCobrancaFinanceiro) throws ServiceException;

    String gravarPosPagoDCCLancarAgendamentoFinan(final Empresa e, final int idEmpresa, final int qtdCredito, final Integer tipoCobrancaPacto, final Integer qtdParcelas, final Double valorTotal, final boolean gerarNota, final String nomeUsuarioOAMD, final String justificativa, final boolean gerarCobrancaFinanceiro) throws ServiceException;

    String alterarInformacoesEmpresaCobrancaPacto(final Empresa e, final int idEmpresa, final Integer tipoCobrancaPacto, final boolean gerarCobrancaAutomaticaPacto,
                                                  final Integer qtdDiasFechamentoCobrancaPacto, final Double valorCreditoPacto, final boolean gerarNotaFiscalCobrancaPacto,
                                                  final Integer qtdParcelasCobrancaPacto, final Integer qtdCreditoRenovarPrePagoCobrancaPacto, final String nomeUsuarioOAMD,
                                                  final Integer diaVencimentoCobrancaPacto, boolean empresaResponsavelCobrancaPacto, Boolean cobrarCreditoPactoBoleto) throws ServiceException;

    String descobrirVersaoPostgres(final Empresa e, final String hostBD, final Usuario u) throws ServiceException;

    void desativarBancoRenomeando(final Empresa e, final FlagSoftwareEnum flagSoft, final Usuario u) throws ServiceException;

    void removerEmpresaOAMD(final Empresa e, final FlagSoftwareEnum flagSoft, boolean principal, final Usuario u) throws ServiceException;

    void removerEmpresaOAMDPrincipal(final Empresa e, final Usuario u) throws ServiceException;

    void adicionarLogRemocaoEmpresaOAMDPrincipal(Empresa e, final Usuario u, List<String> logRemocao) throws ServiceException;

    String doConsulta(final Empresa e, final String op, final String sql, final Usuario usuario) throws ServiceException;

    String doConsulta(final Empresa e, final String op, final String sql, final Usuario usuario, boolean bancoTreino) throws ServiceException;

    String realizarLiberacaoAppGestor(final Empresa e, final int codigoEmpresa);

    String executarProcessoLatitudeLongitude(final Empresa e, final EmpresaJSON empresaJSON, final int idEmpresa, Usuario usuario) throws ServiceException;

    String habilitarAcademia(EmpresaJSON empresaJSON) throws ServiceException;

    void atualizarCodigoEmpresaFinanceiroZW(EmpresaFinanceiro ef) throws ServiceException;

    Integer qtdAtualBonusCreditoPacto(final Empresa e, final Integer empresa) throws Exception;

    String adicionarBonusCreditoPacto(final Empresa e, final Integer empresa, Integer qtdBonus, String observacao, String usuario) throws Exception;

    void alterarRecursoPadrao(final Empresa e, final Integer idEmpresa, String tipoInfoMigracaoEnum,
                              Usuario usuarioOAMD, String tipoOperacao) throws ServiceException;

    JSONObject obterRecursoPadrao(final Empresa e, final Integer idEmpresa) throws ServiceException;

    void alterarRecursoPadraoAutomatico(final Empresa e, final Integer idEmpresa, boolean processarAgora,
                                        String tipoInfoMigracaoEnum, Usuario usuarioOAMD) throws ServiceException;

}
