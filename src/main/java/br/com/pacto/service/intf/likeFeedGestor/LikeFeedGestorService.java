package br.com.pacto.service.intf.likeFeedGestor;

import br.com.pacto.bean.empresa.FeedGestor;

import java.util.List;

/**
 * <AUTHOR> Siqueira 31/01/2019
 */
public interface LikeFeedGestorService {

    void salvar(FeedGestor FeedGestor) throws Exception;

    void altera(FeedGestor FeedGestor) throws Exception;

    void getAll() throws Exception;

    FeedGestor findId(Integer id, String chave, String guid) throws Exception;


    List<FeedGestor> getLikes(String chave, Integer codigoUsuario) throws Exception;
}
