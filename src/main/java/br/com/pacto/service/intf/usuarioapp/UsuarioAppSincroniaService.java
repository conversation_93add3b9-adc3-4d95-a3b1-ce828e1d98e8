package br.com.pacto.service.intf.usuarioapp;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.feed.usuarioapp.UsuarioApp;
import br.com.pacto.bean.usuario.to.UsuarioAppSincronizarTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

public interface UsuarioAppSincroniaService {

    Empresa enfileirarUsuario(final String ctx, final String email, final String cpf,
                              final String telefone, final String dataNascimento, final String senha) throws ServiceException;

    ConcurrentLinkedQueue<UsuarioAppSincronizarTO> getUsuariosSincronizar();

    void organizarQueue(Queue<UsuarioAppSincronizarTO> filaDeTrabalho);

    ConcurrentHashMap<String, Empresa> getEmpresas();

    void preencherUsuario(String cpf, String telefone, String dataNascimento, String senha, UsuarioApp usuario, Empresa emp);
}
