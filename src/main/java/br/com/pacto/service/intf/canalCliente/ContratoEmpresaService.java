package br.com.pacto.service.intf.canalCliente;

import br.com.pacto.bean.empresa.ContratoEmpresa;
import br.com.pacto.controller.json.canalCliente.ResponsavelAssinaturaDTO;

import java.util.List;

public interface ContratoEmpresaService {
    public static final String SERVICE_NAME = "ContratoEmpresaService";

    List<ContratoEmpresa> consultarContratosPorChaveUsuarioCliente(String chave, Boolean adminAcademia);

    List<ContratoEmpresa> consultarContratosPorChaveUsuarioPacto(String chave, Boolean usuarioPacto);

    ContratoEmpresa obterContrato(Integer codigo);

    ContratoEmpresa assinar(Integer codigo, ResponsavelAssinaturaDTO responsavel);
}
