/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.feed;

import br.com.pacto.bean.feed.FeedGestao;
import br.com.pacto.bean.feed.FeedGestaoHistorico;
import br.com.pacto.bean.feed.PaginaInicialFeed;
import br.com.pacto.bean.feed.TotalizadoresEstatisticasTO;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface FeedGestaoService {

    public static final String SERVICE_NAME = "FeedGestaoService";

    public FeedGestao inserir( FeedGestao object) throws ServiceException;

    public FeedGestao obterPorId( Integer id) throws ServiceException;

    public FeedGestao alterar( FeedGestao object) throws ServiceException;

    public void excluir( FeedGestao object) throws ServiceException;

    public List<FeedGestao> obterTodos() throws ServiceException;

    public List<FeedGestao> obterPorParam( String query, Map<String, Object> params)
            throws ServiceException;

    public List<FeedGestao> obterPorParam( String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public FeedGestao obterObjetoPorParam( String query, Map<String, Object> params)
            throws ServiceException;
    
    public List<FeedGestao> obterVigentes() throws ServiceException;
    
    public PaginaInicialFeed inserirPaginalInicial( PaginaInicialFeed object) throws ServiceException;
    
    public PaginaInicialFeed alterarPaginalInicial( PaginaInicialFeed object) throws ServiceException;
    
    public PaginaInicialFeed consultarPaginalInicial() throws ServiceException;
    
    public void gravarHistorico(FeedGestaoHistorico historico) throws ServiceException;
    
    public TotalizadoresEstatisticasTO estatisticas(FeedGestao feed);
    

}
