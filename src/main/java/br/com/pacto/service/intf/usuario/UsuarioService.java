package br.com.pacto.service.intf.usuario;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuario.UsuarioZW;
import br.com.pacto.controller.pg4dev.dtos.Pg4devWebhookDTO;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import org.json.JSONObject;

public interface UsuarioService {
    
    static final String SERVICE_NAME = "usuarioService";

    Usuario inserir(Usuario object) throws ServiceException;

    Usuario alterar(Usuario object, String senha) throws ServiceException;
    
    void excluir(Usuario object) throws ServiceException;

    Usuario validarUsuario(final String userName, final String password,
            boolean somenteUserName) throws ServiceException;

    Usuario validarUsuarioPermissao(final String userName, final String password, final String permissao) throws ServiceException;
    
    List<Usuario> obterTodos() throws ServiceException;
    
    Usuario obterPorId(Integer id) throws ServiceException;
    
    UsuarioZW validarUsuarioZW(Empresa empresa, final String chave, final String userName, final String password, String roboControle, String userOamd)
            throws Exception;

    UsuarioZW validarUsuarioTR(final String chave, final String userName, final String password, String roboControle, String userOamd)
            throws Exception;

    boolean permitePactoPay(Empresa empresa, UsuarioZW usuario);
    
    Usuario prepararAmbiente(JSONObject json) throws ServiceException;
    
    List<Perfil> obterPerfis() throws ServiceException;

    Usuario obterPorUsername(String username) throws ServiceException;

    Usuario findByEmail(String email) throws ServiceException;
    Usuario webhook(Pg4devWebhookDTO pg4dev) throws ServiceException;
}
