package br.com.pacto.service.intf.premium;

import br.com.pacto.bean.premium.ResultadoVitio;
import br.com.pacto.json.ResultadoVitioJSON;

public interface ResultadoVitioService {

    boolean existeAceiteEmpresa(String chave) throws Exception;

    boolean existeRespostaVitioUsuario(String chave, Integer codUsuario) throws Exception;

    ResultadoVitio salvar(ResultadoVitioJSON resultadoVitioJSON) throws Exception;
}
