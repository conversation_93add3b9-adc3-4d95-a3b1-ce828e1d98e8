package br.com.pacto.service.intf.redeempresa;

import br.com.pacto.bean.empresa.redeempresa.UsuarioRedeEmpresa;
import br.com.pacto.bean.usuario.Usuario;
import org.json.JSONObject;

/**
 * Created by <PERSON> on 10/03/2017.
 */
public interface UsuarioRedeEmpresaService {

    public static final String SERVICE_NAME = "UsuarioRedeEmpresaService";

    void gerarUsuarioRedeEmpresa(String key,String email, Usuario u) throws Exception;

    JSONObject descobrirUsuarioApp(String telefone) throws Exception;

    JSONObject descobrirUsuarioApp(String email,String senha) throws Exception;

    JSONObject descobrirUsuarioApp(String key,String email,String senha) throws Exception;

    JSONObject obterUrlRedirect(String key,String email,String senha,Integer codigoEmpresa, Boolean deslogar) throws Exception;

    UsuarioRedeEmpresa descobrirEmpresaEmail(String email) throws Exception;

    JSONObject descobrirEmpresasUsuarioAppV2(String key, String email, String senha) throws Exception;

    JSONObject descobrirEmpresasUsuarioAppV3(String key, String email, String senha) throws Exception;

    void gerarUsuarioRedeEmpresaV2(String key,String email) throws Exception;

    void gerarUsuarioRedeEmpresaV3(String key, String email, String cpf, String telefone, String dataNascimento, String senha, String empFinanceiro) throws Exception;

    void gerarUsuarioRedeEmpresaV4(String key, String email, String cpf, String telefone, String dataNascimento, String senha, String empresas) throws Exception;
}
