package br.com.pacto.service.intf.creditoPacto;

import br.com.pacto.bean.creditoPacto.CreditoPacto;
import br.com.pacto.bean.creditoPacto.CreditoPactoEmpresa;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * <AUTHOR> on 25/09/2020.
 */
public interface CreditoPactoEmpresaService {

    public static final String SERVICE_NAME = "CreditoPactoEmpresaService";

    CreditoPactoEmpresa inserir(CreditoPactoEmpresa object) throws ServiceException;

    CreditoPactoEmpresa alterar(CreditoPactoEmpresa object) throws ServiceException;

    void excluir(CreditoPactoEmpresa object) throws ServiceException;

    void excluirPorCreditoPacto(CreditoPacto creditoPacto) throws ServiceException;

    void excluirPorEmpresaFinanceiro(EmpresaFinanceiro empresaFinanceiro) throws ServiceException;

    List<CreditoPactoEmpresa> obterTodos() throws ServiceException;

    CreditoPactoEmpresa obterPorId(Integer id) throws ServiceException;

    List<CreditoPactoEmpresa> consultar(CreditoPacto creditoPacto) throws ServiceException;

    CreditoPactoEmpresa consultarPorEmpresaFinanceiro(EmpresaFinanceiro empresaFinanceiro) throws ServiceException;
}
