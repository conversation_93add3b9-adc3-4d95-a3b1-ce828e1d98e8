package br.com.pacto.service.intf.pixWebhook;

import br.com.pacto.bean.token.PixWebhook;
import br.com.pacto.dao.intf.pixWebhook.PixWebhookDao;
import br.com.pacto.service.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PixWebhookServiceImpl implements PixWebhookServiceInterface {

    @Autowired
    private PixWebhookDao pixWebhookDao;

    @Override
    public PixWebhook consultarPorChaveECodigo(String chaveZW, int codPixWebhookZW) throws Exception {
        try {
            return pixWebhookDao.findObjectByAttributes(new String[]{"chave", "codigo"}, new Object[]{chaveZW, codPixWebhookZW}, "codigo");
        } catch (Exception e) {
        }
        return null;
    }

    @Override
    public PixWebhook alterar(PixWebhook object) throws Exception {
        try {
            return pixWebhookDao.update(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

}
