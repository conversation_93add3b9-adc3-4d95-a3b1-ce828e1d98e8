/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.gympass;

import br.com.pacto.bean.gympass.GymPassBooking;
import br.com.pacto.service.exception.ServiceException;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 23/03/2020
 */
public interface GymPassBookingService {

    static final String SERVICE_NAME = "GymPassBookingService";

    void inserir(GymPassBooking object) throws ServiceException;

    void alterar(GymPassBooking object) throws ServiceException;

    GymPassBooking obterPorId(Integer codigo) throws ServiceException;

    void enviarTreinoWeb(GymPassBooking obj);
}
