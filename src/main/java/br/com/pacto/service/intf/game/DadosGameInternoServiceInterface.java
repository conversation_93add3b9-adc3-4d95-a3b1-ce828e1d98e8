package br.com.pacto.service.intf.game;

import java.util.Date;

/**
 * <AUTHOR>
 * <p>
 * Interface que define todos os serviços para a importação de dados
 * independentemente da fonte de dados(UpdateServlet e SQLSever financeiro)
 */
public interface DadosGameInternoServiceInterface {

//    public void buscarEmpresaAlunoAtivo();

//    public void buscarFinanceiro();

    void buscarEmpresaFormaPagamento(Date data, String token);

    void atualizaCodigoEmpresaFinanceiro() throws Exception;

//    public void buscaTotalTransacoesEnviadas();

//    public void buscaTotalTransacoesAceitas();
}
