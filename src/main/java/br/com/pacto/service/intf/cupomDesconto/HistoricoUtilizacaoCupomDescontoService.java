package br.com.pacto.service.intf.cupomDesconto;

import br.com.pacto.bean.campanhaCupomDesconto.CampanhaCupomDesconto;
import br.com.pacto.bean.cupomDesconto.CupomDesconto;
import br.com.pacto.bean.cupomDesconto.HistoricoUtilizacaoCupomDesconto;
import br.com.pacto.bean.cupomDesconto.TipoConsultaCupomDescontoEnum;

import java.util.List;

public interface HistoricoUtilizacaoCupomDescontoService {

    void incluir(CupomDesconto cupomDesconto, int contrato)throws Exception;

    void informarEstorno(int contrato, String chaveZW)throws Exception;

    List<HistoricoUtilizacaoCupomDesconto> consultarHistoricoCupom(CampanhaCupomDesconto campanhaCupomDescontoVO, String chaveZW, Integer empresaZW, TipoConsultaCupomDescontoEnum tipoConsultaCupomDescontoEnum , String numeroCupom)throws Exception;
}
