package br.com.pacto.service.intf.produtoPacto;

import br.com.pacto.bean.produtoPacto.ProdutoPacto;
import br.com.pacto.bean.produtoPacto.ProdutoPactoCompra;
import br.com.pacto.bean.produtoPacto.ProdutoPactoCupomDesconto;
import br.com.pacto.enums.TipoProdutoPacto;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ProdutoPactoCompraService {

    public static final String SERVICE_NAME = "ProdutoPactoCompraService";

    ProdutoPactoCompra inserir(ProdutoPactoCompra object) throws ServiceException;

    ProdutoPactoCompra alterar(ProdutoPactoCompra object) throws ServiceException;

    void excluir(ProdutoPactoCompra object) throws ServiceException;

    List<ProdutoPactoCompra> obterTodos() throws ServiceException;

    ProdutoPactoCompra obterPorId(Integer id) throws ServiceException;

    Long qtdQtdPorCupomDescontoSucesso(ProdutoPactoCupomDesconto cupom, Boolean sucesso) throws ServiceException;

    Long qtdQtdPorProdutoPacto(ProdutoPacto produtoPacto) throws ServiceException;

    List<ProdutoPactoCompra> consultar(ProdutoPacto produto, TipoProdutoPacto tipoProdutoPacto, Boolean sucesso) throws ServiceException;

    boolean jaComprou(String chave, Integer codigoFinanceiro, ProdutoPacto produto, ProdutoPacto produtoDiferente,
                      TipoProdutoPacto tipoProdutoPacto, Boolean sucesso) throws ServiceException;
}
