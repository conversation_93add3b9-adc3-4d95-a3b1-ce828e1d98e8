/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.empresafinanceiro;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.InfoInfraEnum;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiroJSON;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.controller.json.empresafinanceiro.OamdNichoDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface EmpresaFinanceiroService {

    public static final String SERVICE_NAME = "EmpresaFinanceiroService";

    List<EmpresaFinanceiro> consultarEmpresasRede(Integer rede) throws Exception;

    List<Empresa> consultarEmpresas() throws Exception;

    EmpresaFinanceiro obterPorChave(String key) throws Exception;

    List<EmpresaJSON> consultarEmpresasRede(String key) throws Exception;

    List<EmpresaFinanceiro> consultarTodas() throws Exception;

    int consultarEmpresaFinanceiroGrupo() throws Exception;

    Map<String, EmpresaFinanceiro> obterMapaEmpresas() throws Exception;

    List<EmpresaFinanceiro> obterEmpresasCodigoFinanceiro() throws Exception;

    List<EmpresaFinanceiro> obterPorCnpj(String cnpj) throws Exception;

    List<EmpresaFinanceiro> consultarEmpresasPorChave(String chave) throws Exception;

    EmpresaFinanceiro obterEmpresa(Integer codigoEmpresaZw, String chave) throws Exception;

    List<EmpresaFinanceiro> consultarPorNome(String nome);

    EmpresaFinanceiro obterPorCodigo(int codigo);

    List<EmpresaFinanceiro> obeterTodasEmpresasPlanoSucesso() throws Exception;

    String consultarNomeEmpresaPorChave(String chaveZW) throws Exception;

    EmpresaFinanceiro incluir(final EmpresaFinanceiro empresa) throws Exception;

    EmpresaFinanceiro alterar(final EmpresaFinanceiro empresa) throws Exception;

    void alterarChaveCodigoZw(final EmpresaFinanceiro empresa) throws Exception;

    void alterarDataExpiracao(String chave, Integer empresa, Date dataExpiracao) throws Exception;

    EmpresaFinanceiro findByCodFinanceiro(Integer codFinanceiro) throws ServiceException;

    @Deprecated
    void atualizarFinanceiro(EmpresaFinanceiroJSON empresaFinanceiro) throws ServiceException;

    void atualizarFinanceiroIOF(EmpresaFinanceiroJSON empresaFinanceiro) throws ServiceException;

    List<EmpresaFinanceiro> consultarEmpresasChaveZwEmpresaZw(String chaveZW, List<Integer> empresas) throws Exception;

    boolean processarNicho(OamdNichoDTO nichoDTO) throws Exception;

    int countActiveCompanyByInfra(InfoInfraEnum infoInfraEnum) throws Exception;

}
