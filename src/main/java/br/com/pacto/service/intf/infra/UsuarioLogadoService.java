/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.infra;

import br.com.pacto.bean.empresa.InfoInfraEnum;
import br.com.pacto.bean.infra.UsuarioLogado;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface UsuarioLogadoService {

    public static final String SERVICE_NAME = "UsuarioLogadoService";

    public UsuarioLogado inserir(UsuarioLogado object) throws ServiceException;

    public UsuarioLogado obterPorId(Integer id) throws ServiceException;

    public UsuarioLogado alterar(UsuarioLogado object) throws ServiceException;

    public void excluir(UsuarioLogado object) throws ServiceException;

    public List<UsuarioLogado> obterTodos() throws ServiceException;

    public List<UsuarioLogado> obterPorParam(String query, Map<String, Object> params)
            throws ServiceException;

    public List<UsuarioLogado> obterPorParam(String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public UsuarioLogado obterObjetoPorParam(String query, Map<String, Object> params)
            throws ServiceException;
    
    public void persistirDados(UsuarioLogado u) throws ServiceException;
    
    public void limparDados() throws ServiceException;
    
    public void persistirDadosInfra(InfoInfraEnum infra, final String serverName, final String dados);
    
    public void persistirDadosIpLocation();

    Long countByIpAndInfoInfra(String ip, InfoInfraEnum infoInfraEnum) throws Exception;

}
