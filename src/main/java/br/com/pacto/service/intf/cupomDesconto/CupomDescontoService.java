package br.com.pacto.service.intf.cupomDesconto;

import br.com.pacto.bean.campanhaCupomDesconto.CampanhaCupomDesconto;
import br.com.pacto.bean.cupomDesconto.CupomDesconto;
import br.com.pacto.bean.cupomDesconto.CupomDescontoWS;

import java.util.List;

public interface CupomDescontoService {

    List<CupomDesconto> incluirLoteCupom(CampanhaCupomDesconto campanhaCupomDescontoVO, Integer lote, Integer totalCupomGerar, String nomeCupomEspecifico)throws Exception;
    void gravarPremioAluno(CupomDesconto cupomDescontoVO)throws Exception;
    void gravarObservacaoProcessamento(CupomDesconto cupomDescontoVO)throws Exception;
    List<CupomDesconto> consultar(Integer idCampanhaCupomDesconto)throws Exception;
    Integer consultarTotalCupomDescontoJaUtilizado(Integer idCampanhaCupomDesconto)throws Exception;
    CupomDesconto consultarPorNumeroCupom(String numeroCupom, Integer codigoFavorecido) throws Exception;
    CupomDesconto validarCupomPortadorCupom(Integer codigoFavorecido, String numeroCupom, String nomePlano)throws Exception;
    List<CupomDescontoWS> consultarCupomDesconto(String keyRede, String listaCupom) throws  Exception;
    void cancelarUtilizacaoCupomDesconto(String numeroCupomDesconto, String chaveZW)throws Exception;
    void alterar(CupomDesconto cupomDesconto)throws Exception;
}
