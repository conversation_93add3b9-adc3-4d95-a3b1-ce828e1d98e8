package br.com.pacto.service.intf.campanhaCupomDesconto;

import br.com.pacto.bean.campanhaCupomDesconto.CampanhaCupomDesconto;
import br.com.pacto.bean.campanhaCupomDesconto.CampanhaCupomDescontoJSON;

import java.util.List;

public interface CampanhaCupomDescontoService {

    boolean existeCampanhaCupomDesconto(Integer codigoFavorecido, boolean somenteVigente, String descricaoPlano) throws Exception;

    CampanhaCupomDesconto incluir(CampanhaCupomDesconto campanhaCupomDesconto)throws Exception;

    CampanhaCupomDesconto alterar(CampanhaCupomDesconto campanhaCupomDesconto)throws Exception;

    void excluir(Integer id)throws Exception;

    CampanhaCupomDesconto consultarPorId(Integer id)throws Exception;

    List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, String chaveRedeEmpresa) throws Exception;

    String consultarCampanhaCupomDescontoJSON(String chaveRedeEmpresa)throws Exception;

    void gerarNovoLoteCupomDesconto(CampanhaCupomDesconto campanhaCupomDesconto, Integer quantidadeCupomGerar)throws Exception;

    CampanhaCupomDesconto consultarCampanhaPorNumeroCupom(String numeroCupomDesconto)throws Exception;

    List<CampanhaCupomDescontoJSON> findAllByRedeEmpresaOrIdFavorecido(String chaveRedeEmpresa, Integer idFavorecido, boolean somenteVigentes) throws Exception;
}
