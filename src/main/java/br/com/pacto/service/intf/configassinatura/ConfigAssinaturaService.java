package br.com.pacto.service.intf.configassinatura;

import br.com.pacto.bean.assinaturas.ConfigAssinatura;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.configassinatura.PlanoDTO;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface ConfigAssinaturaService {

    String SERVICE_NAME = "ConfigAssinaturaService";

    ConfigAssinatura salvar(ConfigAssinatura object) throws ServiceException;

    List<PlanoDTO> planos(ConfigAssinatura config) throws ServiceException;

    List<EmpresaJSON> empresas(ConfigAssinatura config)throws ServiceException;

    List<ConfigAssinatura> obterAti<PERSON>(String usuario) throws ServiceException;

    ConfigAssinatura pactoAtiva() throws ServiceException;

    ConfigAssinatura findById(Integer id) throws ServiceException;

}
