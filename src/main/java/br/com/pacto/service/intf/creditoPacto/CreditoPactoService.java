package br.com.pacto.service.intf.creditoPacto;

import br.com.pacto.bean.creditoPacto.CreditoPacto;
import br.com.pacto.enums.TipoProdutoPacto;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * <AUTHOR> on 25/09/2020.
 */
public interface CreditoPactoService {

    public static final String SERVICE_NAME = "CreditoPactoService";

    CreditoPacto inserir(CreditoPacto object) throws ServiceException;

    CreditoPacto alterar(CreditoPacto object) throws ServiceException;

    void excluir(CreditoPacto object) throws ServiceException;

    List<CreditoPacto> obterTodos() throws ServiceException;

    CreditoPacto obterPorId(Integer id) throws ServiceException;

    List<CreditoPacto> consultar(TipoProdutoPacto tipo, Boolean ativo) throws ServiceException;
}
