/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.token;

import br.com.pacto.bean.token.Token;
import br.com.pacto.dao.intf.token.TokenDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.token.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Random;

/*
 * <AUTHOR>
 */
@Service
public class TokenServiceImpl implements TokenService {

    @Autowired
    private TokenDao tokenDao;

    @Override
    public Token obterPorId(Integer id) throws ServiceException {
        try {
            return tokenDao.findObjectByAttribute("codigo", id);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Token inserir(Token object) throws ServiceException {
        try {
            return tokenDao.insert(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Token inserirGerandoToken(Token object) throws ServiceException {
        try {
            tokenDao.insert(object);
            gerarToken(object);
            return tokenDao.update(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Token alterar(Token object) throws ServiceException {
        try {
            return tokenDao.update(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void excluir(Token object) throws ServiceException {
        try {
            tokenDao.delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Token consultarPorToken(String token) throws ServiceException {
        try {
            return tokenDao.findObjectByAttribute("token", token);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    private static final Random gerador = new Random();
  
    public static Integer gerarPin() {
        return 100000 + gerador.nextInt(900000);
    }
    
    @Override
    public void gerarToken(Token token) throws ServiceException {
        try {
             /*
             * Solicitação do Ticket Assembla #10021
             * DecimalFormat df = new DecimalFormat("000000");
             * String tokenGerado = incluirLetrasAleatorio(df.format(token.getCodigo()));
             */
            final String tokenGerado = gerarPin().toString();
            token.setToken(tokenGerado);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private String incluirLetrasAleatorio(String token) {
        String[] alfabeto = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "P", "Q", "R", "S", "T", "U", "V", "Y", "W", "X", "Z"};
        Random random = new Random();

        String letra1 = alfabeto[random.nextInt(25)];
        String letra2 = alfabeto[random.nextInt(25)];
        String letra3 = alfabeto[random.nextInt(25)];
        return letra1 + letra2 + letra3 + token;
    }
}
