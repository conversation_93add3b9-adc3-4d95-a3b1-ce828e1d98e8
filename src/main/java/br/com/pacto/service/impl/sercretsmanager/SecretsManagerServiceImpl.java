package br.com.pacto.service.impl.sercretsmanager;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.service.intf.sercretsmanager.SecretsManagerService;
import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder;
import com.amazonaws.services.secretsmanager.model.DecryptionFailureException;
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest;
import com.amazonaws.services.secretsmanager.model.GetSecretValueResult;
import com.amazonaws.services.secretsmanager.model.InternalServiceErrorException;
import com.amazonaws.services.secretsmanager.model.InvalidRequestException;
import com.amazonaws.services.secretsmanager.model.ResourceNotFoundException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.InvalidParameterException;
import java.util.Base64;

@Service
@Qualifier(value = "secretsManagerService")
public class SecretsManagerServiceImpl implements SecretsManagerService {

    public String getAwsKeySecret(String secretName) {


        AWSSecretsManager client = AWSSecretsManagerClientBuilder.standard()
                .withRegion(Aplicacao.getProp(Aplicacao.AWS_REGION))
                .build();

        GetSecretValueRequest getSecretValueRequest = new GetSecretValueRequest().withSecretId(secretName);
        GetSecretValueResult getSecretValueResult;

        try {
            getSecretValueResult = client.getSecretValue(getSecretValueRequest);
        } catch (DecryptionFailureException e) {
            System.out.println("AWS Secret Manager key não pode descriptografar o texto protegido usando a chave KMS");
            throw e;
        } catch (InternalServiceErrorException e) {
            System.out.println("Erro na api do Secret Manager");
            throw e;
        } catch (InvalidParameterException e) {
            System.out.println("Parametros inválidos na request para Secret Manager");
            throw e;
        } catch (InvalidRequestException e) {
            System.out.println("Não é permitido obter o segredo no estado atual na api do Secret Manager");
            throw e;
        } catch (ResourceNotFoundException e) {
            System.out.println("O segredo solicitado para Secret Manager não existe");
            throw e;
        }

        if (getSecretValueResult.getSecretString() != null) {
            return getSecretValueResult.getSecretString();
        } else {
            return new String(Base64.getDecoder().decode(getSecretValueResult.getSecretBinary()).array());
        }
    }

}

