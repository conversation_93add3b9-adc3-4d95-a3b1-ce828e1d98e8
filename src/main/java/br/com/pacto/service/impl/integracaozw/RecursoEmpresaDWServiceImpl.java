package br.com.pacto.service.impl.integracaozw;

import br.com.pacto.bean.integracaozw.RecursoEmpresa;
import br.com.pacto.bean.integracaozw.RecursoEmpresaDW;
import br.com.pacto.dao.intf.integracaozw.RecursoEmpresaDWDao;
import br.com.pacto.service.intf.integracaozw.RecursoEmpresaDWService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class RecursoEmpresaDWServiceImpl implements RecursoEmpresaDWService {

    private RecursoEmpresaDWDao recursoEmpresaDWDao;

    public void incluir() throws Exception {
        RecursoEmpresaDW recursoEmpresaDW = new RecursoEmpresaDW();
        recursoEmpresaDWDao.insert(recursoEmpresaDW);
    }
}
