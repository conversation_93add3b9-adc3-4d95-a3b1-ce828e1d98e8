package br.com.pacto.service.impl.game;

import br.com.pacto.bean.cs.EmpresaFormaPagamento;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.dao.intf.cs.EmpresaFormaPagamentoDao;
import br.com.pacto.json.EmpresaFormaPagamentoJSON;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.HttpRequestUtil;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.empresafinanceiro.EmpresaFinanceiroService;
import br.com.pacto.service.intf.game.DadosGameInternoServiceInterface;
import br.com.pacto.service.intf.oamd.OAMDService;
import br.com.pacto.util.UteisValidacao;
import org.joda.time.Duration;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR>
 */
@Service
public class DadosGameInternoServiceImpl implements DadosGameInternoServiceInterface {

    Map<String, Object> pMesAno = new HashMap<String, Object>();
    Map<String, SimpleDateFormat> mapaFormat = new HashMap<String, SimpleDateFormat>();


    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private EmpresaFinanceiroService empresaFinanceiroService;
//    @Autowired
//    private EmpresaAlunoAtivoDao empresaAlunoAtivoDao;
//    @Autowired
//    private FinanceiroPactoDao financeiroPactoDao;
    @Autowired
    private EmpresaFormaPagamentoDao empresaFormaPagamentoDao;

    @Autowired
    private OAMDService oamdService;
//    @Autowired
//    private TransacoesAceitasDao transacoesAceitasDao;
//    @Autowired
//    private TransacoesEnviadasDao transacoesEnviadasDao;
    private String chaveBanco;
    private String nomeBanco;

    private Date dataConsulta;
    private Calendar calendar;

    public static String getSqlBuscarEmpresaAlunoAtivo() {
        return new StringBuilder()
                .append(" select")
                .append(" em.codigo as codigoEmpresa,")
                .append(" em.ativa, em.nome, em.razaosocial, em.cnpj, em.tokensms,")
                .append(" date_part('day', em.dataexpiracao)::integer as diaExpiracao,")
                .append(" date_part('month', em.dataexpiracao)::integer as mesExpiracao, ")
                .append(" date_part('year', em.dataexpiracao)::integer as anoExpiracao,")
                .append(" em.dataexpiracao, em.dataexpiracaocreditodcc,")
                .append(" es.sigla as UF,")
                .append(" es.descricao as estado,")
                .append(" c.nome as cidade,")
                .append(" (select count(*) from situacaoclientesinteticodw c where c.empresacliente = em.codigo and c.situacao  = 'AT' or c.situacaocontrato = 'VE') as clientesAtivos,")
                .append(" (select max(dthrentrada) from acessocliente ac inner join localacesso la on ac.localacesso = la.codigo and la.empresa = em.codigo) as dataUltimoAcesso,")
                .append(" CASE WHEN (em.dataexpiracao > CURRENT_DATE)::text = 'true' THEN 'VAI EXPIRAR'")
                .append(" WHEN (em.dataexpiracao <= CURRENT_DATE)::text = 'true' THEN 'EXPIRADO'")
                .append(" ELSE 'SEM DATA'")
                .append(" END as situacao")
                .append(" from empresa em")
                .append(" left join estado es on es.codigo = em.estado")
                .append(" left join cidade c on c.codigo = em.cidade")
                .toString();
    }

    private void initMesAno(Date consulta) {
        dataConsulta = consulta;

        calendar = Calendar.getInstance();
        calendar.setTime(dataConsulta);

        pMesAno.put("MES", calendar.get(Calendar.MONTH) + 1);
        pMesAno.put("ANO", calendar.get(Calendar.YEAR));
    }

//    @Override
//    public void buscarEmpresaAlunoAtivo() {
//        try {
//            Uteis.logar(null, "Started -> " + getClass().getSimpleName());
//            Map<String, EmpresaOAMDJSON> mapaParamConexoes = getEmpresaService().preencherMapaParamConexoes();
//            Set<String> keySet = mapaParamConexoes.keySet();
//
//            empresaAlunoAtivoDao.delete(empresaAlunoAtivoDao
//                    .findByParam("SELECT objs FROM EmpresaAlunoAtivo objs " +
//                            "WHERE EXTRACT(MONTH FROM dataconsulta) = :MES AND EXTRACT(YEAR FROM dataconsulta) =  :ANO", pMesAno));
//
//            for (String k : keySet) {
//                try {
//                    buscaTodasEmpresasSelectOne(mapaParamConexoes.get(k), k, getSqlBuscarEmpresaAlunoAtivo(), new TrataJson() {
//                        @Override
//                        public void exc(String json) {
//                            try {
//                                List<EmpresaAlunoAtivoJSON> listJson = EmpresaAlunoAtivoJSON.<EmpresaAlunoAtivoJSON>readList(json, EmpresaAlunoAtivoJSON.class);
//                                List<EmpresaAlunoAtivo> lista = new ArrayList<EmpresaAlunoAtivo>();
//
//                                for (EmpresaAlunoAtivoJSON obj : listJson) {
//                                    EmpresaAlunoAtivo registro = new EmpresaAlunoAtivo();
//                                    registro.setDataConsulta(dataConsulta);
//                                    registro.setDataExpiracao(formatDate(obj.getDataexpiracao(), "yyyy-MM-dd"));
//                                    registro.setRazaoSocial(obj.getRazaosocial());
//                                    registro.setUf(obj.getUf());
//                                    registro.setTokenSms(obj.getTokensms());
//                                    registro.setDiaExpiracao(obj.getDiaexpiracao());
//                                    registro.setDataUltAcesso(formatDate(obj.getDataultimoacesso(), "yyyy-MM-dd HH:mm:ss.SSS"));
//                                    registro.setCnpj(obj.getCnpj());
//                                    registro.setDataExpCreditoDcc(formatDate(obj.getDataexpiracaocreditodcc(), "dd-MM-yyyy HH:mm:ss"));
//                                    registro.setAnoExpiracao(obj.getAnoexpiracao());
//                                    registro.setCidade(obj.getCidade());
//                                    registro.setEstado(obj.getEstado());
//                                    registro.setMesExpiracao(obj.getMesexpiracao());
//                                    registro.setSequencia(obj.getSeq());
//                                    registro.setAtiva(obj.getAtiva());
//                                    registro.setCodigoEmpresa(obj.getCodigoempresa());
//                                    registro.setSituacao(obj.getSituacao());
//                                    registro.setNome(obj.getNome());
//                                    registro.setChave(chaveBanco);
//                                    registro.setNomeBanco(obj.getNomebanco());
//                                    registro.setClietesAtivos(obj.getClientesativos());
//                                    lista.add(registro);
//                                }
//
//                                empresaAlunoAtivoDao.insert(lista);
//                            } catch (Exception e) {
//                                Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//                            }
//                        }
//                    });
//                } catch (Exception ex) {
//                    Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, ex);
//                }
//            }
//        } catch (Exception e) {
//            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//        }
//    }

//    public void buscarFinanceiro() {
//        Connection conexao = null;
//        Statement smt = null;
//        try {
//            initMesAno();
//            Class.forName("org.firebirdsql.jdbc.FBDriver");
//            conexao = DriverManager.getConnection(
//                    "jdbc:firebirdsql://192.168.156.4:3050/C:\\PactoDados\\Financeiro\\FINANCEIROPACTO.FDB",
//                    "SYSDBA",
//                    "masterkey");
//            smt = conexao.createStatement();
//            InputStream in = getClass().getClassLoader().getResourceAsStream("consulta_financeiro.sql");
//            StringBuilder sql = new StringBuilder(Uteis.convertStreamToStringBuffer(in));
////            financeiroPactoDao.delete(financeiroPactoDao
////                    .findByParam("SELECT objs FROM FinanceiroPacto objs " +
////                            "WHERE EXTRACT(MONTH FROM dataconsulta) = :MES AND EXTRACT(YEAR FROM dataconsulta) =  :ANO", pMesAno));
//
//            ResultSet result = smt.executeQuery(sql.toString());
//            List<FinanceiroPacto> lista = new ArrayList<FinanceiroPacto>();
//
//            while (result.next()) {
//                FinanceiroPacto registro = new FinanceiroPacto();
//                registro.setCodigozw(result.getInt("codigozw"));
//                registro.setCategoria(result.getString("Categoria"));
//                registro.setDataConsulta(dataConsulta);
//                registro.setIdSubCategoria(result.getInt("id_SubCategoria"));
//                registro.setSubCategoria(result.getString("SubCategoria"));
//                registro.setClassificacao(result.getString("Classificacao"));
//                registro.setIdSubClassificacao(result.getInt("id_SubClassificacao"));
//                registro.setSubClassificacao(result.getString("SubClassificacao"));
//                registro.setGrupoFavorecido(result.getString("ds_grupofavorecido"));
//                registro.setGrupoTipo(result.getString("ds_grupotipo"));
//                registro.setD1r2(result.getString("D1R2"));
//                registro.setIdMesParcela(result.getInt("Id_MesmaParcela"));
//                registro.setValorItem(result.getBigDecimal("ValorItem"));
//                registro.setDescricaoItem(result.getString("DescricaoDoItem"));
//                registro.setDescricaoParcela(result.getString("DescricaoDaParcela"));
//                registro.setDthrLancamento(result.getTimestamp("dthr_lancamento"));
//                registro.setAnoLanc(result.getInt("Ano_Lanc"));
//                registro.setMesLanc(result.getInt("Mes_Lanc"));
//                registro.setDiaLanc(result.getInt("Dia_Lanc"));
//                registro.setDthrQuitacao(result.getTimestamp("dthr_quitacao"));
//                registro.setAnoQuit(result.getInt("Ano_Quit"));
//                registro.setMesQuit(result.getInt("Mes_Quit"));
//                registro.setDiaQuit(result.getInt("Dia_Quit"));
//                registro.setDthrVencimento(result.getTimestamp("dthr_vencimento"));
//                registro.setAnoVenc(result.getInt("Ano_Venc"));
//                registro.setMesVenc(result.getInt("Mes_Venc"));
//                registro.setDiaVenc(result.getInt("Dia_Venc"));
//                registro.setDataCompetencia(result.getTimestamp("DATACOMPETENCIA"));
//                registro.setAnoComp(result.getInt("Ano_Comp"));
//                registro.setMesComp(result.getInt("Mes_Comp"));
//                registro.setDiaComp(result.getInt("Dia_Comp"));
//                registro.setNrCheque(result.getString("nr_cheque"));
//                registro.setNrDocumento(result.getString("nr_documento"));
//                registro.setNrParcela(result.getInt("nr_parcela"));
//                registro.setIdParcela(result.getInt("ID_PARCELA"));
//                registro.setAgdPeriodico(result.getInt("AGD_PERIODICO"));
//                registro.setContaOrigem(result.getString("ContaOrigem"));
//                registro.setContaDestino(result.getString("ContaDestino"));
//                registro.setNoFavorecido(result.getString("NO_FAVORECIDO"));
//                registro.setEndereco(result.getString("ENDERECO"));
//                registro.setCidade(result.getString("CIDADE"));
//                registro.setEstado(result.getString("ESTADO"));
//                registro.setTelefone(result.getString("TELEFONE"));
//                registro.setAbreviacaoFavorecido(result.getString("AbreviacaoFavorecido"));
//                registro.setNomeFavorecidoEmpresa(result.getString("NO_FAVOREC_Empresa"));
//                registro.setPessoaDeContato(result.getString("PessoaDeContato"));
//                registro.setFax(result.getString("FAX"));
//                registro.setEmail(result.getString("EMAIL"));
//                registro.setDataAtualizacaoCadastro(result.getTimestamp("DataAtualizacaoCadastro"));
//                registro.setDataCadastro(result.getTimestamp("DATACADASTRO"));
//                registro.setAnoCadastro(result.getInt("Ano_Cad"));
//                registro.setMesCadastro(result.getInt("Mes_Cad"));
//                registro.setDiaCadastro(result.getInt("Dia_Cad"));
//                registro.setDataExpiracao(result.getTimestamp("Data_Expiracao"));
//                registro.setChaveZW(result.getString("CHAVEZW"));
//                registro.setCgc(result.getString("CGC"));
//                registro.setDataExpNovoCertificado(result.getTimestamp("DATA_EXPNOVO_CERTIFICADO"));
//                registro.setQuitado(result.getString("QUITADO"));
//                registro.setVencido(result.getString("VENCIDO"));
//                registro.setStatucCompetencia(result.getString("STATUSCOMPETENCIA"));
//                registro.setTranferencia(result.getString("TRANFERENCIA"));
//                registro.setPagaEmDia(result.getString("PAGAEMDIA"));
//                registro.setDiasAtraso(result.getInt("DIAS_ATRASO"));
//                registro.setDre(result.getString("DRE"));
//                registro.setEfeito(result.getString("EFEITO"));
//                registro.setDsEvento(result.getString("DS_EVENTO"));
//                registro.setTrimestre(result.getInt("TRIMESTRE"));
//                registro.setTipoCobranca(result.getString("TIPOCOBRANCA"));
//                registro.setMeta(result.getString("META"));
//                registro.setNumNFSE(result.getString("NUMNFSE"));
//                registro.setAnoNotaEmitida(result.getInt("Ano_NotaEmitida"));
//                registro.setMesNotaEmitida(result.getInt("Mes_NotaEmitida"));
//                registro.setDiaNotaEmitida(result.getInt("Dia_NotaEmitida"));
//                lista.add(registro);
//            }
//            financeiroPactoDao.insert(lista);
//        } catch (Exception e) {
//            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//        } finally {
//            try {
//                if (smt != null) {
//                    smt.close();
//                }
//                if (conexao != null) {
//                    conexao.close();
//                }
//            } catch (SQLException e) {
//                Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//            }
//        }
//    }

    @Override
    public void buscarEmpresaFormaPagamento(Date dataC, String token) {
        try {
            Uteis.logar(null, "Started -> " + getClass().getSimpleName());
            initMesAno(dataC);

            Map<String, Empresa> mapaParamConexoes = empresaService.obterMapaEmpresas();
            final Map<String, EmpresaFinanceiro> mapaEmpresasFinanceiro = empresaFinanceiroService.obterMapaEmpresas();

            Set<String> keySet = mapaParamConexoes.keySet();
            //APAGA TODOS DOS REGISTROS DO MÊS ATUAL DE MANEIRA A PERNACER
            //SÓ OS REGISTROS DA ULTIMA IMPORTACAO DO MÊS EM QUESTÃO.
            empresaFormaPagamentoDao.delete(empresaFormaPagamentoDao
                    .findByParam("SELECT objs FROM EmpresaFormaPagamento objs " +
                            "WHERE EXTRACT(MONTH FROM dataconsulta) = :MES AND EXTRACT(YEAR FROM dataconsulta) =  :ANO", pMesAno));

            for (String k : keySet) {
                Empresa empresa = mapaParamConexoes.get(k);
                if (empresa.getNomeBD().contains("bdfacili") ||
                        empresa.getNomeBD().contains("apptrial") ||
                        empresa.getNomeBD().contains("apppersonal")) {
                    continue;
                }

                final String empresaZW;
                final Boolean ativaOamd = empresa.getAtiva();
                final Boolean servidorWeb = empresa.getRoboControle().matches(".*zw(\\d{2})\\.pactosolucoes\\.com\\.br.*");
                try {
                    InputStream in = getClass().getClassLoader().getResourceAsStream("consulta_financeiro_forma_pagamento.sql");
                    StringBuilder sql = Uteis.convertStreamToStringBuffer(in);
                    String sqlcomparamentros = sql.toString();

                    Date dataPesquisaFinanceira = Uteis.somarCampoData(dataC, Calendar.MONTH, -1);
                    Date primeiroDiaMesFinanceiro = Uteis.obterPrimeiroDiaMes(dataPesquisaFinanceira);
                    Date ultimoDiaMesFinanceiro = Uteis.obterUltimoDiaMes(dataPesquisaFinanceira);

                    sqlcomparamentros = sqlcomparamentros.replaceAll("_datainicio", "'" + Uteis.getDataJDBC(primeiroDiaMesFinanceiro) + "'");
                    sqlcomparamentros = sqlcomparamentros.replaceAll("_datafim", "'" + Uteis.getDataJDBC(ultimoDiaMesFinanceiro) + "'");

                    //Desconsidera empresas de testes para métrica de ultimo acesso funcionar.
                    if (!empresa.getUsoTeste() && !empresa.getUsoInterno()) {
                        empresaZW = "PRODUCAO";
                    } else {
                        empresaZW = "TESTE";
                    }

                    if (!empresa.getModulos().contains("ZW")) {
                        in = getClass().getClassLoader().getResourceAsStream("consulta_financeiro_forma_pagamento_treino.sql");
                        sql = Uteis.convertStreamToStringBuffer(in);
                        sqlcomparamentros = sql.toString();
                    }

                    buscaTodasEmpresasSelectOne(empresa, k, sqlcomparamentros, new TrataJson() {
                        @Override
                        public void exc(final String json) {
                            try {
                                JSONArray obj = new JSONObject(json).getJSONArray("result");
                                List<EmpresaFormaPagamentoJSON> listJson = EmpresaFormaPagamentoJSON.readList(obj.toString(), EmpresaFormaPagamentoJSON.class);
                                if (listJson == null) {
                                    listJson = new ArrayList<>();
                                }
                                List<EmpresaFormaPagamento> lista = new ArrayList<>();

                                for (EmpresaFormaPagamentoJSON o : listJson) {
                                    EmpresaFormaPagamento empFormPag = new EmpresaFormaPagamento();
                                    empFormPag.setDataConsulta(dataConsulta);
                                    empFormPag.setConsultaMes(calendar.get(Calendar.MONTH) + 1);
                                    empFormPag.setConsultaAno(calendar.get(Calendar.YEAR));
                                    empFormPag.setChave(chaveBanco);
                                    empFormPag.setCodigo(o.getCodigo());
                                    empFormPag.setNomeBanco(nomeBanco);
                                    empFormPag.setCodigoEmpresa(o.getCodigoempresa());
                                    empFormPag.setAtiva(o.getAtiva());
                                    empFormPag.setAtivaOamd(ativaOamd);
                                    empFormPag.setNome(o.getNome());
                                    empFormPag.setRazaoSocial(o.getRazaosocial());
                                    empFormPag.setCnpj(o.getCnpj());
                                    empFormPag.setDataUltimoLogin(formatDate(o.getDataultimologin(), "yyyy-MM-dd HH:mm:ss.SSS"));

                                    empFormPag.setDataExpiracaoCreditoDcc(formatDate(o.getDataexpiracaocreditodcc(), "yyyy-MM-dd"));
                                    empFormPag.setUf(o.getUf());
                                    empFormPag.setEstado(o.getEstado());
                                    empFormPag.setCidade(o.getCidade());
                                    empFormPag.setClientesAtivos(o.getClientesativos());
                                    empFormPag.setDataUltimoAcesso(formatDate(o.getDataultimoacesso(), "yyyy-MM-dd HH:mm:ss.SSS"));
                                    empFormPag.setSituacao(o.getSituacao());
                                    empFormPag.setPeriodoInicio(formatDate(o.getPeriodoinicio(), "yyyy-MM-dd"));
                                    empFormPag.setPeriodoFim(formatDate(o.getPeriodofim(), "yyyy-MM-dd"));
                                    empFormPag.setUltimoLancamento(formatDate(o.getUltimolancamento(), "yyyy-MM-dd HH:mm:ss.SSS"));
                                    empFormPag.setQtdRecibos(o.getQtdrecibos());
                                    if (empresa.getModulos().contains("ZW")) {
                                        empFormPag.setValorFaturado(new BigDecimal(o.getValorfaturado()));
                                        empFormPag.setFaturadoCheque(new BigDecimal(o.getFaturadocheque()));
                                        empFormPag.setFaturadoAVista(new BigDecimal(o.getFaturadoavista()));
                                        empFormPag.setFaturadoCartoCredito(new BigDecimal(o.getFaturadocartocredito()));
                                        empFormPag.setFaturadoCartaoDebito(new BigDecimal(o.getFaturadocartaodebito()));
                                        empFormPag.setFaturadoBoleto(new BigDecimal(o.getFaturadoboleto()));
                                        empFormPag.setFaturadoContaCorrente(new BigDecimal(o.getFaturadocontacorrente()));
                                        empFormPag.setFaturadoPagamentoDigital(new BigDecimal(o.getFaturadopagamentodigital()));
                                    }
                                    empFormPag.setSequencia(o.getSeq());
                                    empFormPag.setCreditoDcc(o.getCreditodcc());
                                    empFormPag.setTipoCobrancaDcc(o.getTipocobrancadcc());
                                    if (empresa.getModulos().contains("ZW")) {
                                        empFormPag.setFaturadoCartaoCreditoNormal(new BigDecimal(o.getFaturadocartaocreditonormal()));
                                        empFormPag.setFaturadoCartaoCreditoDCC(new BigDecimal(o.getFaturadocartaocreditodcc()));
                                    }
                                    empFormPag.setEmpresazw(empresaZW);
                                    empFormPag.setServidorWeb(servidorWeb);
                                    empFormPag.setTemNFSe(o.getTemnfse());
                                    empFormPag.setPrimeiraNFSeEmitida(o.getPrimeiranfseemitida());
                                    empFormPag.setQteNFSeEmitidas(o.getQtenfseemitidas());
                                    empFormPag.setQteNFSeEmitidasUltimoMes(o.getQtenfseemitidasultimomes());
                                    empFormPag.setQtdAcessoMes(o.getQtdacessomes());

                                    EmpresaFinanceiro empresaFinanceiro = mapaEmpresasFinanceiro.get(chaveBanco + "-" + o.getCodigoempresa());
                                    if (empresaFinanceiro != null) {
                                        empFormPag.setCodigoFinanceiro(empresaFinanceiro.getCodigoFinanceiro());
                                        if (empresaFinanceiro.getRedeEmpresa() != null) {
                                            empFormPag.setRedeEmpresa(empresaFinanceiro.getRedeEmpresa().getNome());
                                        }
                                        empFormPag.setTipoEmpresa(empresaFinanceiro.getDetalheEmpresa().getTipoCliente());
                                        empFormPag.setInicioImplantacao(empresaFinanceiro.getDetalheEmpresa().getInicioImplantacao());
                                        empFormPag.setFinalImplantacao(empresaFinanceiro.getDetalheEmpresa().getFinalImplantacao());
                                        empFormPag.setNivelReceitaMensal(empresaFinanceiro.getDetalheEmpresa().getNivelReceitaMensal());
                                        empFormPag.setRenegociadoAte(empresaFinanceiro.getDetalheEmpresa().getRenegociadoAte());
                                        empFormPag.setCondicaoEspecial(empresaFinanceiro.getDetalheEmpresa().getCondicaoEspecial());

                                        if (empresaFinanceiro.getDataExpiracaoZw() != null) {
                                            empFormPag.setDiaExpiracao(Uteis.getDiaMesData(empresaFinanceiro.getDataExpiracaoZw()));
                                            empFormPag.setMesExpiracao(Uteis.getMesData(empresaFinanceiro.getDataExpiracaoZw()));
                                            empFormPag.setAnoExpiracao(Uteis.getAnoData(empresaFinanceiro.getDataExpiracaoZw()));
                                            empFormPag.setDataExpiracao(empresaFinanceiro.getDataExpiracaoZw());
                                        }

                                        if (empresaFinanceiro.getDataExpiracaoZw() == null && !UteisValidacao.emptyString(o.getDataexpiracao())) {
                                            empresaFinanceiro.setDataExpiracaoZw(formatDate(o.getDataexpiracao(), "yyyy-MM-dd"));
                                            empresaFinanceiroService.alterar(empresaFinanceiro);
                                        }
                                    }


                                    if (empFormPag.getDataExpiracaoCreditoDcc() != null) {
                                        Calendar cDccExp = Calendar.getInstance();
                                        cDccExp.setTime(empFormPag.getDataExpiracaoCreditoDcc());
                                        empFormPag.setDccDtExpMes(cDccExp.get(Calendar.MONTH) + 1);

                                        empFormPag.setDccDtExpAno(cDccExp.get(Calendar.YEAR));
                                    }

                                    if (empFormPag.getDataUltimoAcesso() != null) {
                                        long diasUltAcc = new Duration(new Date().getTime(), empFormPag.getDataUltimoAcesso().getTime()).getStandardDays();
                                        empFormPag.setUltimoAcessoSistemaDias((int) diasUltAcc);
                                    }

                                    if (empFormPag.getUltimoLancamento() != null) {
                                        long diaUltLanc = new Duration(new Date().getTime(), empFormPag.getUltimoLancamento().getTime()).getStandardDays();
                                        empFormPag.setUltimoLacamentoDias((int) diaUltLanc);

                                        Calendar cDiaUltLanc = Calendar.getInstance();
                                        cDiaUltLanc.setTime(empFormPag.getUltimoLancamento());

                                        empFormPag.setUaMes(cDiaUltLanc.get(Calendar.MONTH) + 1);
                                        empFormPag.setUaAno(cDiaUltLanc.get(Calendar.YEAR));
                                    }


                                    lista.add(empFormPag);
                                }
                                empresaFormaPagamentoDao.insert(lista);
                            } catch (Exception e) {
                                Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
                            }
                        }
                    }, token);

                } catch (Exception e) {
                    List<EmpresaFinanceiro> empresasFinanceiroPorChave = consultarPorChave(mapaEmpresasFinanceiro, chaveBanco);

                    if (empresasFinanceiroPorChave.isEmpty()) {
                        EmpresaFormaPagamento empFormPag = initEmpresaFormaPagamentoDefault(null, ativaOamd, servidorWeb, e);
                        empresaFormaPagamentoDao.insert(empFormPag);
                    } else {
                        for (EmpresaFinanceiro empresaFinanceiro : empresasFinanceiroPorChave) {
                            EmpresaFormaPagamento empFormPag = initEmpresaFormaPagamentoDefault(empresaFinanceiro, ativaOamd, servidorWeb, e);
                            empresaFormaPagamentoDao.insert(empFormPag);
                        }
                    }

                    Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
                }
            }

        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
        }
    }

    private EmpresaFormaPagamento initEmpresaFormaPagamentoDefault(EmpresaFinanceiro empresaFinanceiro, Boolean ativaOamd, Boolean servidorWeb, Exception e) {
        EmpresaFormaPagamento empFormPag = new EmpresaFormaPagamento();
        empFormPag.setDataConsulta(dataConsulta);
        empFormPag.setConsultaMes(calendar.get(Calendar.MONTH) + 1);
        empFormPag.setConsultaAno(calendar.get(Calendar.YEAR));
        empFormPag.setChave(chaveBanco);
        empFormPag.setNomeBanco(nomeBanco);
        empFormPag.setServidorWeb(servidorWeb);
        empFormPag.setMotivoIndisponivel(e.getMessage());
        empFormPag.setAtivaOamd(ativaOamd);
        empFormPag.setAtiva(true);
        if (empresaFinanceiro != null) {
            empFormPag.setCodigoFinanceiro(empresaFinanceiro.getCodigoFinanceiro());
            empFormPag.setCodigoEmpresa(empresaFinanceiro.getEmpresazw());
        }
        return empFormPag;
    }

    private List<EmpresaFinanceiro> consultarPorChave(Map<String, EmpresaFinanceiro> mapaEmpresasFinanceiro, String chaveBanco) {
        List<EmpresaFinanceiro> empresas = new ArrayList<>();
        for (String chave : mapaEmpresasFinanceiro.keySet()) {
            if (!UteisValidacao.emptyString(chaveBanco) && chaveBanco.length() > 5 && chave.contains(chaveBanco)) {
                empresas.add(mapaEmpresasFinanceiro.get(chave));
            }
        }

        return empresas;
    }

    @Override
    public void atualizaCodigoEmpresaFinanceiro() throws Exception {
        final  List<EmpresaFinanceiro> empresasFinanceiroCodigo = empresaFinanceiroService.obterEmpresasCodigoFinanceiro();
        empresasFinanceiroCodigo.forEach(empresaFinanceiro -> {
            try {
                oamdService.atualizarCodigoEmpresaFinanceiroZW(empresaFinanceiro);
            } catch (ServiceException e) {
                e.printStackTrace();
            }
        });

    }

//    @Override
//    public void buscaTotalTransacoesEnviadas() {
//        try {
//            Uteis.logar(null, "Started -> " + getClass().getSimpleName());
//            Map<String, EmpresaOAMDJSON> mapaParamConexoes = getEmpresaService().preencherMapaParamConexoes();
//            Set<String> keySet = mapaParamConexoes.keySet();
//
//            transacoesEnviadasDao.delete(transacoesEnviadasDao.findByParam("SELECT objs FROM TransacoesEnviadas objs " +
//                    "WHERE EXTRACT(MONTH FROM dataconsulta) = :MES AND EXTRACT(YEAR FROM dataconsulta) =  :ANO", pMesAno));
//
//            for (String k : keySet) {
//                try {
//                    InputStream in = getClass().getClassLoader().getResourceAsStream("consulta_oamd_transacoes_enviadas.sql");
//                    StringBuilder sql = new StringBuilder(Uteis.convertStreamToStringBuffer(in));
//                    final String empresaZW;
//
//                    //Desconsidera empresas de testes para métrica de ultimo acesso funcionar.
//                    if (mapaParamConexoes.get(k).getUsoTeste().equals("false") && mapaParamConexoes.get(k).getUsoInterno().equals("false")) {
//                        empresaZW = "PRODUCAO";
//                    } else {
//                        empresaZW = "TESTE";
//                    }
//
//                    buscaTodasEmpresasSelectOne(mapaParamConexoes.get(k), k, sql.toString(), new TrataJson() {
//                        @Override
//                        public void exc(String json) {
//                            try {
//                                List<TransacoesEnviadasAceitasJSON> listJson = TransacoesEnviadasAceitasJSON.readList(json, TransacoesEnviadasAceitasJSON.class);
//                                List<TransacoesEnviadas> lista = new ArrayList<TransacoesEnviadas>();
//
//                                for (TransacoesEnviadasAceitasJSON o : listJson) {
//                                    TransacoesEnviadas t = new TransacoesEnviadas();
//                                    t.setDataConsulta(dataConsulta);
//                                    t.setChave(chaveBanco);
//                                    t.setNomeBanco(nomeBanco);
//                                    t.setQtdTransacoesEnviadas(o.getQtd() == null ? 0 : o.getQtd());
//                                    t.setValorTransacoesEnviadas(new BigDecimal(o.getValor() == null ? 0 : o.getValor()));
//                                    lista.add(t);
//                                }
//                                transacoesEnviadasDao.insert(lista);
//                            } catch (Exception e) {
//                                Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//                            }
//                        }
//                    });
//
//                } catch (Exception e) {
//                    Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//                }
//            }
//
//        } catch (Exception e) {
//            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//        }
//    }

//    @Override
//    public void buscaTotalTransacoesAceitas() {
//        try {
//            Uteis.logar(null, "Started -> " + getClass().getSimpleName());
//            Map<String, EmpresaOAMDJSON> mapaParamConexoes = getEmpresaService().preencherMapaParamConexoes();
//            Set<String> keySet = mapaParamConexoes.keySet();
//
//            transacoesAceitasDao.delete(transacoesAceitasDao.findByParam("SELECT objs FROM TransacoesAceitas objs " +
//                    "WHERE EXTRACT(MONTH FROM dataconsulta) = :MES AND EXTRACT(YEAR FROM dataconsulta) =  :ANO", pMesAno));
//
//            for (String k : keySet) {
//                try {
//                    InputStream in = getClass().getClassLoader().getResourceAsStream("consulta_oamd_transacoes_aceitas.sql");
//                    StringBuilder sql = new StringBuilder(Uteis.convertStreamToStringBuffer(in));
//                    final String empresaZW;
//
//                    //Desconsidera empresas de testes para métrica de ultimo acesso funcionar.
//                    if (mapaParamConexoes.get(k).getUsoTeste().equals("false") && mapaParamConexoes.get(k).getUsoInterno().equals("false")) {
//                        empresaZW = "PRODUCAO";
//                    } else {
//                        empresaZW = "TESTE";
//                    }
//
//                    buscaTodasEmpresasSelectOne(mapaParamConexoes.get(k), k, sql.toString(), new TrataJson() {
//                        @Override
//                        public void exc(String json) {
//                            try {
//                                List<TransacoesEnviadasAceitasJSON> listJson = TransacoesEnviadasAceitasJSON.readList(json, TransacoesEnviadasAceitasJSON.class);
//                                List<TransacoesAceitas> lista = new ArrayList<TransacoesAceitas>();
//
//                                for (TransacoesEnviadasAceitasJSON o : listJson) {
//                                    TransacoesAceitas t = new TransacoesAceitas();
//                                    t.setDataConsulta(dataConsulta);
//                                    t.setChave(chaveBanco);
//                                    t.setNomeBanco(nomeBanco);
//                                    t.setQtdTransacoesAceitas(o.getQtd() == null ? 0 : o.getQtd());
//                                    t.setValorTransacoesAceitas(new BigDecimal(o.getValor() == null ? 0 : o.getValor()));
//                                    lista.add(t);
//                                }
//                                transacoesAceitasDao.insert(lista);
//                            } catch (Exception e) {
//                                Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//                            }
//                        }
//                    });
//
//                } catch (Exception e) {
//                    Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//                }
//            }
//
//        } catch (Exception e) {
//            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, null, e);
//        }
//    }

    private void buscaTodasEmpresasSelectOne(Empresa params, String chave, String sql, TrataJson trataJson, String token) throws Exception {
        boolean bancoTreino = !params.getModulos().contains("ZW");
        String roboControle = params.getRoboControle();
        Map<String, String> p = new HashMap<>();
        p.put("hostPG", params.getHostBD());
        p.put("portaPG", params.getPorta().toString());
        p.put("userPG", params.getUserBD());
        p.put("pwdPG", params.getPasswordBD());
        if (bancoTreino) {
            p.put("bd", params.getNomeBDTreino());
        } else {
            p.put("bd", params.getNomeBD());
        }
        p.put("format", "json");
        p.put("sql", sql);
        p.put("op", "selectONE");
        p.put("lgn", token);

        //Pega a chave do banco de dados para o caso de algum
        //método precise da chave da empresa.
        chaveBanco = params.getChave();
        nomeBanco = params.getNomeBD();

        System.out.println(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm") + ":: Obtendo dados Game Interno " + chave);
        String result = HttpRequestUtil.executeRequestInner(roboControle + "/UpdateServlet", p, 30000, 5000, "UTF-8");
        if (result.contains("FATAL") || result.isEmpty()) {
            throw new Exception(result);
        }
        trataJson.exc(result);
        System.out.println(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm") + ":: Dados obtidos Game Interno " + chave);
    }

    private Date formatDate(String data, String mask) {
        try {
            if (!mapaFormat.containsKey(mask)) {
                mapaFormat.put(mask, new SimpleDateFormat(mask));
            }
            DateFormat df = mapaFormat.get(mask);
            if (data != null && !data.isEmpty()) {
                return df.parse(data);
            }
            return null;
        } catch (ParseException e) {
            return null;
        }
    }

    private static interface TrataJson {
        void exc(final String json);
    }
}
