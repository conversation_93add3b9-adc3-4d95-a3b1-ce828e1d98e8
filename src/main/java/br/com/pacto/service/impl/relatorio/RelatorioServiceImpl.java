package br.com.pacto.service.impl.relatorio;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.dto.ClienteFrequenciaDTO;
import br.com.pacto.dto.ClientePaganteDTO;
import br.com.pacto.dto.ConsultaAtivosDTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.relatorio.RelatorioService;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Service
@Qualifier(value = "relatorioService")
public class RelatorioServiceImpl implements RelatorioService {
    @Autowired
    EmpresaDao empresaDao;

    private static final String sqlFrequenciaSesi= "select distinct '%s' as ano, '%s' as mes, emp.nome as unidade, pes.nome, pes.cfp as cpf ,'' as empresaClienteTrabalha , cat.nome as categoriaCliente,\n" +
            " (CASE when (pes.cpfmae is null or pes.cpfmae = '') then (case when (pes.nomepai is null or pes.nomepai = '') then null else pes.nomepai end) else pes.nomemae end) as nomeResponsavel,\n" +
            " (CASE when (pes.cpfmae is null or pes.cpfmae = '') then (case when (pes.cpfpai is null or pes.cpfpai = '') then null else pes.cpfpai end) else pes.cpfmae end) as cpfResponsavel,\n" +
            " (CASE WHEN (pes.cnpjclientesesi is null or pes.cnpjclientesesi = '') THEN (CASE when (emp.cnpjclientesesi is null or emp.cnpjclientesesi = '') then null else emp.cnpjclientesesi end) else pes.cnpjclientesesi END) as empresaClienteTrabalhaCNPJ, sexo,  datanasc  as  nascimento,\n" +
            "(select numero from telefone where pessoa = pes.codigo order by tipotelefone limit 1) as phone1,\n" +
            "(select numero from telefone where pessoa = pes.codigo order by tipotelefone desc limit 1)as phone2,\n" +
            " (select email from email where pessoa = pes.codigo  limit 1)as email,\n" +
            " ende.endereco as endereco, ende.numero as numero, ende.cep as cep,cid.nome as cidade, es.sigla as estado,\n" +
//            "case when  pro.negociosesi = 2 then (select count(ps.codigo) from presenca ps inner join matriculaalunohorarioturma mt on mt.codigo  = ps.dadosturma where mt.pessoa  = pes.codigo and ps.datapresenca::date  between '%s' and '%s') else\n" +
//            "(select count(codigo) from acessocliente where cliente =cli.codigo and dthrentrada::date  between '%s' and '%s') end  " +
//            "(select count(*) as quantidade_acessos from acessocliente a inner join localacesso l ON l.codigo = a.localacesso inner join empresa e ON e.codigo = l.empresa \n" +
//            "inner join contrato c ON c.empresa = e.codigo where a.cliente = cli.codigo and c.codigo in (con.codigo) and a.dthrentrada::date between '%s' and '%s') as frequencia,\n" +
            "(select count(*) from acessocliente a inner join localacesso l ON l.codigo = a.localacesso inner join empresa e ON e.codigo = l.empresa inner join contrato c ON c.empresa = e.codigo\n" +
            "inner join plano pl ON pl.codigo = c.plano inner join produto p2 ON p2.codigo = pl.produtopadraogerarparcelascontrato where a.cliente = cli.codigo and c.codigo = con.codigo\n" +
            "and a.dthrentrada::date between '%s' and '%s' and ((p2.negociosesi = 2 AND a.meioidentificacaoentrada = 6) OR (p2.negociosesi = 1 AND a.meioidentificacaoentrada <> 6) )) as frequencia,\n" +
            "pro.codigoprodutosesi as codigoProduto\n" +
            "from cliente cli inner join pessoa pes on pes.codigo = cli.pessoa left join cidade cid on pes.cidade = cid.codigo left join estado es on es.codigo = pes.estado\n" +
            "inner join periodoacessocliente pr on pr.pessoa = pes.codigo and pr.datafinalacesso > '%s' and pr.datainicioacesso < '%s'\n" +
            "inner join contrato con on con.codigo = pr.contrato \n" +
            "inner join empresa emp on emp.codigo = con.empresa \n" +
            "inner join plano pla on pla.codigo = con.plano \n" +
            "inner join produto pro on pro.codigo = pla.produtopadraogerarparcelascontrato \n" +
            "left join endereco ende on ende.pessoa = pes.codigo\n" +
            "left join categoria cat on cat.codigo = cli.categoria\n" +
            "where (ende.codigo is null or ende.codigo =(select coalesce(max(codigo),0) from endereco where pessoa = pes.codigo))\n";
    private static final String sqlPagantesSesi= "select distinct '%s' as ano, '%s' as mes, emp.nome as unidade, pes.nome, pes.cfp as cpf ,'' as empresaClienteTrabalha , cat.nome as categoriaCliente,\n" +
            " (CASE when (pes.cpfmae is null or pes.cpfmae = '') then (case when (pes.nomepai is null or pes.nomepai = '') then null else pes.nomepai end) else pes.nomemae end) as nomeResponsavel,\n" +
            " (CASE when (pes.cpfmae is null or pes.cpfmae = '') then (case when (pes.cpfpai is null or pes.cpfpai = '') then null else pes.cpfpai end) else pes.cpfmae end) as cpfResponsavel,\n" +
            " (CASE WHEN (pes.cnpjclientesesi is null or pes.cnpjclientesesi = '') THEN (CASE when (emp.cnpjclientesesi is null or emp.cnpjclientesesi = '') then null else emp.cnpjclientesesi end) else pes.cnpjclientesesi END) as empresaClienteTrabalhaCNPJ, sexo,  datanasc  as  nascimento,\n" +
            "(select numero from telefone where pessoa = pes.codigo order by tipotelefone limit 1) as phone1,\n" +
            "(select numero from telefone where pessoa = pes.codigo order by tipotelefone desc limit 1)as phone2,\n" +
            " (select email from email where pessoa = pes.codigo  limit 1)as email,\n" +
            "ende.endereco as endereco, ende.numero as numero, ende.cep as cep,cid.nome as cidade, es.sigla as estado,\n" +
            "'' as planodecontas, '' as centrodecustos, pes.codigo as codigoexterno,\n" +
            "pro.codigoprodutosesi as codigoProduto " +
            "from cliente cli inner join pessoa pes on pes.codigo = cli.pessoa left join cidade cid on pes.cidade = cid.codigo left join estado es on es.codigo = pes.estado\n" +
            "left join endereco ende on ende.pessoa = pes.codigo\n" +
            "left join categoria cat on cat.codigo = cli.categoria\n" +
            "inner join movproduto movpro on movpro.pessoa = pes.codigo\n" +
            "inner join contrato c on c.codigo = movpro.contrato \n" +
            "inner join empresa emp on emp.codigo = c.empresa \n" +
            "inner join plano pl on pl.codigo = c.plano \n" +
            "inner join produto pro on pro.codigo = pl.produtopadraogerarparcelascontrato \n" +
            "where mesreferencia = '%s' and tipoproduto IN ('PM', 'MM') and (ende.codigo is null or ende.codigo =(select coalesce(max(codigo),0) from endereco where pessoa = pes.codigo))\n" +
            "ORDER BY pes.codigo, pro.codigoprodutosesi \n"+
            "\n";

    private static final String sqlAtivosMes= "select cl.matricula, p.nome, e.nome as empresa, (select email from email e2 where pessoa = p.codigo limit 1) as email, \n" +
            "(select t.numero from telefone t where t.pessoa = p.codigo limit 1 ) as tefefone, pl.descricao as plano, c.vigenciade as datainicio, c.vigenciaateajustada  as datafinal \n" +
            " from contrato c inner join cliente cl on cl.pessoa = c.pessoa inner join pessoa p on p.codigo  = c.pessoa inner join empresa e on e.codigo = cl.empresa \n" +
            " inner join plano pl on pl.codigo = c.plano where c.codigo in (select codigo from contrato where pessoa = p.codigo and '%s' < vigenciaateajustada  and  '%s'  > vigenciade order by  vigenciaateajustada desc limit 1 )";

    public List<ClientePaganteDTO> obterPagantesSesi(final String chaverede, final Integer ano, final Integer mes) throws ServiceException {
        try {

            String mesReferencia = (mes < 10 ?  "0" : "") + mes + "/" + ano;
            String sql = String.format(sqlPagantesSesi , ano, mes,mesReferencia);


            List<Empresa> empresasRede  = empresaDao.obterEmpresaPorRede(chaverede);
            List<Future<JSONObject>> respostas = new ArrayList<Future<JSONObject>>();
            ExecutorService executorService = Executors.newFixedThreadPool(empresasRede.size());
            for (Empresa empresa: empresasRede) {
                respostas.add(executorService.submit(new UpdateServletCallable(empresa, "selectONE", sql)));
            }
            List<ClientePaganteDTO> listaTotal = new ArrayList<>();
            ObjectMapper mapper = new ObjectMapper();
            for(Future<JSONObject> resposta : respostas) {
                if(resposta.get() != null && !UteisValidacao.emptyString(resposta.get().optString("result"))) {
                    listaTotal.addAll(Arrays.asList(mapper.readValue(resposta.get().get("result").toString(), ClientePaganteDTO[].class)));
                }
            }

            executorService.shutdown();
            return  listaTotal;
        }catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public  List<ClienteFrequenciaDTO>  obterFrequenciaSesi(final String chaverede, final Integer ano, final Integer mes) throws ServiceException {
        try {
            Date dataincio = Uteis.getDate("01/"+(mes < 10 ?  "0" : "") + mes + "/" + ano);
            Date datafinal = Uteis.obterUltimoDiaMesUltimaHora(dataincio);
            String sql = String.format(sqlFrequenciaSesi , ano, mes,Uteis.getDataFormatoBD(dataincio),Uteis.getDataFormatoBD(datafinal), Uteis.getDataFormatoBD(dataincio), Uteis.getDataFormatoBD(datafinal));

            List<Empresa> empresasRede  = empresaDao.obterEmpresaPorRede(chaverede);
            List<Future<JSONObject>> respostas = new ArrayList<Future<JSONObject>>();
            ExecutorService executorService = Executors.newFixedThreadPool(empresasRede.size());
            for (Empresa empresa: empresasRede) {
                respostas.add(executorService.submit(new UpdateServletCallable(empresa, "selectONE", sql)));
            }
            List<ClienteFrequenciaDTO> listaTotal = new ArrayList<>();
            ObjectMapper mapper = new ObjectMapper();
            for(Future<JSONObject> resposta : respostas) {
                if(resposta.get() != null && !UteisValidacao.emptyString(resposta.get().optString("result"))) {
                    listaTotal.addAll(Arrays.asList(mapper.readValue(resposta.get().get("result").toString(), ClienteFrequenciaDTO[].class)));
                }
            }

            executorService.shutdown();
            return  listaTotal;
        }catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private void concatenarJsonArray(JSONArray listaTotal, JSONArray listaEmpresa) {
        for (int i = 0; i < listaEmpresa.length(); i++){
            listaTotal.put(listaEmpresa.optJSONObject(i));
        }
    }

    @Override
    public List<ConsultaAtivosDTO>  executarConsultaAtivosRede(final String chaveRede, final Integer ano, final Integer mes) throws ServiceException {
        try {

            Date dataincio = Uteis.getDate("01/"+(mes < 10 ?  "0" : "") + mes + "/" + ano);
            Date datafinal = Uteis.obterUltimoDiaMesUltimaHora(dataincio);
            String sql = String.format(sqlAtivosMes ,Uteis.getDataFormatoBD(dataincio),Uteis.getDataFormatoBD(datafinal));

            List<Empresa> empresasRede  = empresaDao.obterEmpresaPorRede(chaveRede);
            List<Future<JSONObject>> respostas = new ArrayList<Future<JSONObject>>();
            ExecutorService executorService = Executors.newFixedThreadPool(empresasRede.size());
            for (Empresa empresa: empresasRede) {
                respostas.add(executorService.submit(new UpdateServletCallable(empresa, "selectONE", sql)));
            }
            List<ConsultaAtivosDTO> listaTotal = new ArrayList<>();
            ObjectMapper mapper = new ObjectMapper();
            for(Future<JSONObject> resposta : respostas) {
                if(resposta.get() != null && !UteisValidacao.emptyString(resposta.get().optString("result"))) {
                    listaTotal.addAll(Arrays.asList(mapper.readValue(resposta.get().get("result").toString(), ConsultaAtivosDTO[].class)));
                }
            }
            executorService.shutdown();

            return listaTotal;
        }catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }




}
