/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.feedApp;

import br.com.pacto.bean.feedApp.FeedApp;
import br.com.pacto.bean.feedApp.FeedAppComentario;
import br.com.pacto.bean.feedApp.FeedAppLike;
import br.com.pacto.controller.json.feedApp.DicaNutriJSON;
import br.com.pacto.controller.json.feedApp.FeedAppJSON;
import br.com.pacto.controller.oamd.JSONMapperOAMD;
import br.com.pacto.dao.intf.feedApp.FeedAppComentarioDao;
import br.com.pacto.dao.intf.feedApp.FeedAppDao;
import br.com.pacto.dao.intf.feedApp.FeedAppDenunciaDao;
import br.com.pacto.dao.intf.feedApp.FeedAppLikeDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.feedApp.FeedAppService;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR> Felipe
 */
@Service
public class FeedAppServiceImpl implements FeedAppService {

    @Autowired
    private FeedAppDao feedAppDao;
    @Autowired
    private FeedAppComentarioDao feedAppComentarioDao;
    @Autowired
    private FeedAppDenunciaDao feedAppDenunciaDao;
    @Autowired
    private FeedAppLikeDao feedAppLikeDao;

    @Override
    public FeedApp obterPorId(Integer id) throws ServiceException {
        try {
            return feedAppDao.findObjectByAttribute("codigo", id);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public FeedApp inserir(FeedApp object) throws ServiceException {
        try {
            return feedAppDao.insert(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public FeedApp alterar(FeedApp object) throws ServiceException {
        try {
            return feedAppDao.update(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void excluir(FeedApp object) throws ServiceException {
        try {
            feedAppDao.delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void excluirComRelacionamentos(FeedApp object) throws ServiceException {
        try {
            feedAppDenunciaDao.deleteComParam(new String[]{"feedApp.codigo"}, new Object[]{object.getCodigo()});
            feedAppComentarioDao.deleteComParam(new String[]{"feedApp.codigo"}, new Object[]{object.getCodigo()});
            feedAppLikeDao.deleteComParam(new String[]{"feedApp.codigo"}, new Object[]{object.getCodigo()});
            feedAppDao.delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public List<FeedApp> consultarPublicacoes(String chave, Integer index, Integer maxResults, boolean especifico, String email) throws ServiceException {
        try {
            StringBuilder sb = new StringBuilder();
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("chave", chave);
            sb.append("select obj from FeedApp obj where obj.chave = :chave ");
            if (especifico){
                sb.append(" and obj.usuarioApp.email = :emailBuscar ");
                p.put("emailBuscar", email);
            }
            sb.append(" order by obj.dataRegistro desc ");
            List<FeedApp> lista = feedAppDao.findByParam(sb.toString(), p, maxResults, index);
            if (!UteisValidacao.emptyList(lista)) {
                for (FeedApp feedApp : lista) {
                    feedApp.setQtdComentarios(qtdComentariosPorFeedApp(feedApp));
                    feedApp.setQtdLikes(qtdLikesPorFeedApp(feedApp));
                }
                return lista;
            } else {
                return new ArrayList<FeedApp>();
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Integer qtdComentariosPorFeedApp(FeedApp feedApp) throws ServiceException {
        try {
            Number count = feedAppComentarioDao.count("codigo", new String[]{"feedApp.codigo"},
                    new Object[]{feedApp.getCodigo()});
            return count.intValue();
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Integer qtdLikesPorFeedApp(FeedApp feedApp) throws ServiceException {
        try {
            Number count = feedAppLikeDao.count("codigo", new String[]{"feedApp.codigo"},
                    new Object[]{feedApp.getCodigo()});
            return count.intValue();
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void preencherListaLikes(FeedApp feedApp) throws ServiceException {
        try {
            feedApp.setLikes(new ArrayList<FeedAppLike>());
            feedApp.setLikes(feedAppLikeDao.findListByAttributes(new String[]{"feedApp.codigo"}, new Object[]{feedApp.getCodigo()}, "dataRegistro", 0));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void preencherListaComentarios(FeedApp feedApp, Integer maxComments) throws ServiceException {
        try {
            feedApp.setComentarios(new ArrayList<FeedAppComentario>());
            feedApp.setComentarios(feedAppComentarioDao.findListByAttributes(new String[]{"feedApp.codigo"}, 
                    new Object[]{feedApp.getCodigo()}, "dataRegistro DESC", maxComments != null ? maxComments : 0));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<FeedAppJSON> consultarPublicacoesNutri(Date dataInicio, Date dataFinal) throws ServiceException {
        try {
            Map<String, String> params = new HashMap<String, String>();
            params.put("dataInicial", Uteis.getDataAplicandoFormatacao(dataInicio, "dd/MM/yyyy HH:mm:ss"));
            params.put("dataFinal", Uteis.getDataAplicandoFormatacao(dataFinal, "dd/MM/yyyy HH:mm:ss"));

            ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
            final String url = Aplicacao.getProp(Aplicacao.myUpUrlBase).replace("https:", "http:") + "/prest/dicasNutri/consultarComData";
            final String executeRequest = executeRequestHttpService.executeRequestInner(url, 
                    params);

            JSONObject jsonObject = new JSONObject(executeRequest);
            JSONArray lista = null;
            try {
                lista = new JSONArray(jsonObject.get("return").toString());
            } catch (Exception e) {
                throw new Exception(jsonObject.get("erro").toString());
            }

            List<FeedAppJSON> listaFinal = new ArrayList<FeedAppJSON>();
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                try {
                    DicaNutriJSON dicaNutri = JSONMapperOAMD.getObject(obj, DicaNutriJSON.class);
                    listaFinal.add(dicaNutri.toFeedAppJSON());
                } catch (Exception ignored) {
                }
            }
            return listaFinal;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
