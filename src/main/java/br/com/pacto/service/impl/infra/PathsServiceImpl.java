package br.com.pacto.service.impl.infra;

import br.com.pacto.bean.empresa.Paths;
import br.com.pacto.bean.infra.PathsUrlEnum;
import br.com.pacto.dao.intf.infra.PathsDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.infra.PathsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PathsServiceImpl implements PathsService {

    @Autowired
    private PathsDao pathsDao;

    public Paths findDefault() throws ServiceException {
        try {
            List<Paths> all = pathsDao.findAll();
            Paths paths = all.isEmpty() ? new Paths() : all.get(0);
            if (paths.getApiZw() == null || paths.getApiZw().isEmpty()) {
                paths.setApiZw(PathsUrlEnum.API_ZW.getUrl());
            }
            if (paths.getUcp() == null || paths.getUcp().isEmpty()) {
                paths.setUcp(PathsUrlEnum.MY_UCP.getUrl());
            }
            return paths;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void clearPaths() {
        pathsDao.cleanPaths();
    }

}
