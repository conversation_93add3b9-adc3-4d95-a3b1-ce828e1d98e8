package br.com.pacto.service.impl.relatorio;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.objeto.HttpRequestUtil;
import br.com.pacto.objeto.Uteis;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

public class UpdateServletCallable implements Callable {

    private Empresa empresa;
    private String operacao;
    private String sql;
    private static final String CHARSET_ISO_8859_1 = "ISO-8859-1";

    public UpdateServletCallable(Empresa empresa, String operacao, String sql) {
        this.empresa = empresa;
        this.operacao = operacao;
        this.sql = sql;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    @Override
    public JSONObject call() throws Exception {
        if (empresa.getAtiva() && StringUtils.isNotBlank(empresa.getRoboControleSemHTTPS()) && empresa.getModulos().contains("ZW")) {
            String retorno = "";
            final String url = String.format("%s/UpdateServlet", empresa.getRoboControleSemHTTPS());
            Map<String, String> p = new HashMap();
            p.put("op", operacao);
            p.put("hostPG", empresa.getHostBD());
            p.put("portaPG", empresa.getPorta().toString());
            p.put("userPG", empresa.getUserBD());
            p.put("pwdPG", empresa.getPasswordBD());
            p.put("bd", empresa.getNomeBD());
            p.put("format", "json");
            p.put("sql", sql);
            String lgn = "7791db30d55e8fb17c8e9ad252010c19ed3efc0a1d501f29f8060f6ec4898d68b985dd70c096d12c";

//            DESCOMENTAR PARA AMBIENTE TESTE
//            JSONObject json = new JSONObject();
//            json.put("userName", "teste");
//            json.put("pwd", "123");
//            lgn = Uteis.encriptar(json.toString(), "chave_login_unificado");

            p.put("lgn", lgn);
            try {
                Uteis.logar(null, url + " " + p);
                retorno = HttpRequestUtil.executeRequestInner(url, p, 15000, 4000, CHARSET_ISO_8859_1);
                Uteis.logar(null, retorno);
                JSONObject jsonRetorno = new JSONObject(retorno);
                return jsonRetorno;

            } catch (Exception ex) {
                retorno = String.format("Erro ao consultar empresa %s , devido ao erro: %s",
                        empresa.getNomeBD(), ex.getMessage());
            }
        }
        return null;
    }
}
