package br.com.pacto.service.impl.feedgestor;

import br.com.pacto.bean.empresa.FeedGestor;
import br.com.pacto.dao.intf.likeFeedGestor.LikeFeedGestorDao;
import br.com.pacto.service.intf.likeFeedGestor.LikeFeedGestorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Si<PERSON>ira 31/01/2019
 */
@Service
public class LikeFeedGestorServiceImpl implements LikeFeedGestorService {

    @Autowired
    private LikeFeedGestorDao likeFeedGestorDao;

    @Override
    public void salvar(FeedGestor likeFeedGestor) throws Exception {
        this.likeFeedGestorDao.insert(likeFeedGestor);
    }

    @Override
    public void altera(FeedGestor likeFeedGestor) throws Exception {
        this.likeFeedGestorDao.update(likeFeedGestor);
    }

    @Override
    public void getAll() throws Exception {
        this.likeFeedGestorDao.findAll();
    }

    @Override
    public FeedGestor findId(Integer id, String chave, String guid) throws Exception {
       return this.likeFeedGestorDao.findId(id, chave, guid);
    }

    @Override
    public List<FeedGestor> getLikes(String chave, Integer codigoUsuario) throws Exception {
        List<FeedGestor> getLikes = this.likeFeedGestorDao.getLikes(chave, codigoUsuario);
        List<FeedGestor> countLikes = this.likeFeedGestorDao.countLike();
        for (FeedGestor like : getLikes) {
            for (FeedGestor feed : countLikes) {
                if (like.getGuid().equalsIgnoreCase(feed.getGuid())) {
                    feed.setId(like.getId());
                    feed.setCurtida(like.isCurtida());
                    break;
                }
            }
        }
        return countLikes;
    }

}
