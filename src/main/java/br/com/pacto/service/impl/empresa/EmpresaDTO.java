package br.com.pacto.service.impl.empresa;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.json.JSONObject;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EmpresaDTO {
    private Integer codigo;
    private String nome;

    public EmpresaDTO() {
    }

    public EmpresaDTO(JSONObject json) {
        this.codigo = json.optInt("codigo");
        this.nome = json.optString("nome");
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
