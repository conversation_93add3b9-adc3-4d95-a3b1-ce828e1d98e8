package br.com.pacto.service.impl.zw;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.controller.json.zw.SessaoJSON;
import br.com.pacto.objeto.HttpRequestUtil;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.zw.SessaoService;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.json.JSONArray;

/**
 * Created by glauco on 03/10/2014.
 */
@Service
public class SessaoServiceImpl implements SessaoService {

    @Override
    public List<SessaoJSON> obterTodos() throws ServiceException {
        try {
            String result = doListSessions();

            if (result.contains(",\n,")) {
                for (int i = 0; i < 3; i++) {
                    result = result.replaceAll(",\\n,", ",");
                }
            }

            if (result.contains("[\n,")) {
                result = result.replace("[\n,","[");
            }

            if (result.contains(",\n]")) {
                result = result.replace(",\n]","]");
            }

            if (result != null && !result.equals("[\n,\n]\n")) {
                JSONArray root = new JSONArray(result);
                return JSONMapper.getList(root, SessaoJSON.class);
            } else {
                return new ArrayList<SessaoJSON>();
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    private String doListSessions() {
        Map<String, String> p = new HashMap<String, String>();
        p.put("op", "listSessions");
        p.put("propagable", "s");

        Empresa e = new Empresa();
        try {
            String result = HttpRequestUtil.executeRequestInner(e.getRoboControleSemHTTPS()+ "/UpdateServlet", p, 2000);
            if (result != null && !result.isEmpty()) {
                result = result.replace("</br>", "").replace("</html>", "");
                if (result.contains("conseguimos processar")) {
                    int tentativas = 1;
                    do {
                        try {
                            Thread.sleep(500);
                        } catch (InterruptedException ex) {
                            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
                        }
                        result = HttpRequestUtil.executeRequestInner(e.getRoboControleSemHTTPS()+ "/UpdateServlet", p, 2000);
                        tentativas++;
                    } while (result.contains("conseguimos processar") && tentativas <= 3);
                }
            }

            System.out.println(result);
            return result;
        } catch (IOException ex) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

}
