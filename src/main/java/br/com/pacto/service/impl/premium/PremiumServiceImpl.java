package br.com.pacto.service.impl.premium;

import br.com.pacto.bean.premium.Premium;
import br.com.pacto.dao.intf.premium.PremiumDao;
import br.com.pacto.service.intf.premium.PremiumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> on 14/12/18.
 * @project OAMD_TRONCO
 */
@Service
public class PremiumServiceImpl implements PremiumService {

    @Autowired
    private PremiumDao premiumDao;

    @Override
    public void save(Premium premium) throws Exception {
        Premium result = premiumDao.findPorEmail(premium.getUsuario().getEmail());
        if ( result != null){
            if (!result.getProduto().equalsIgnoreCase(premium.getProduto())){
                result.setStatus(false);
                atualizar(result);
                premiumDao.insert(premium);
            } else {
                premium.setId(result.getId());
                atualizar(premium);
            }
        } else {
            premiumDao.insert(premium);
        }
    }

    @Override
    public void atualizar(Premium premium) throws Exception {
       premiumDao.update(premium);
    }

    @Override
    public void delete(Premium premium) throws Exception {
        premiumDao.delete(premium);
    }

}
