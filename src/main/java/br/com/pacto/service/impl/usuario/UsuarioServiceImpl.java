package br.com.pacto.service.impl.usuario;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuario.UsuarioZW;
import br.com.pacto.controller.pg4dev.dtos.Pg4devWebhookDTO;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.enums.Modulo;
import br.com.pacto.integracao.IntegracaoCadastrosWSConsumer;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.HttpRequestUtil;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.objeto.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.client.EmpresaWS;
import servicos.integracao.zw.client.UsuarioTO;

import javax.faces.model.SelectItem;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
@Qualifier(value = "usuarioService")
public class UsuarioServiceImpl implements UsuarioService {

    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private PerfilDao perfilDao;
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;

    @Override
    public Usuario alterar(Usuario object, String senha) throws ServiceException {
        try {
            if (senha != null && !senha.isEmpty()) {
                object.setSenha(Uteis.encriptar(senha));
            }
            if (object.getPerfil().getCodigo() != null && object.getPerfil().getCodigo() > 0) {
                object.setPerfil(perfilDao.findById(object.getPerfil().getCodigo()));
            }
            return usuarioDao.update(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(Usuario object) throws ServiceException {
        try {
            usuarioDao.delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Usuario inserir(Usuario object) throws ServiceException {
        try {
            if (object.getPerfil().getCodigo() != null && object.getPerfil().getCodigo() > 0) {
                object.setPerfil(perfilDao.findById(object.getPerfil().getCodigo()));
            }
            object.setSenha(Uteis.encriptar(object.getSenha()));
            return usuarioDao.insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Usuario validarUsuario(final String userName, final String password,
                                  boolean somenteUserName) throws ServiceException {
        try {
            if (validacao.isNull(userName) || validacao.isEmpty(userName)) {
                throw new ServiceException(viewUtils.getMensagem("login.usuarioInvalido"));
            }
            StringBuilder s = new StringBuilder("SELECT obj FROM Usuario obj where UPPER(username) = :username ");
            Map<String, Object> params = new HashMap<>();
            params.put("username", userName.toUpperCase());
            if (!somenteUserName) {
                if (validacao.isNull(password) || validacao.isEmpty(password)) {
                    throw new ServiceException(viewUtils.getMensagem("login.senhaInvalida"));
                }
                s.append("and senha = :senha");
                params.put("senha", Uteis.encriptar(password));
            }
            Usuario u = usuarioDao.findObjectByParam(s.toString(), params);
            if (u != null) {
                if (u.getAtivo()) {
                    u.setPassTrans(password);
                    return u;
                } else {
                    throw new ServiceException("Usuario Inativo");
                }
            } else {
                throw new ServiceException(viewUtils.getMensagem("comum.dadosIncorretos"));
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Usuario validarUsuarioPermissao(final String userName, final String password, final String permissao) throws ServiceException {
        try {
            Usuario u = validarUsuario(userName, password, false);
            if (u != null) {
                RecursoEnum rec = RecursoEnum.valueOf(permissao);
                if (u.getPerfil() != null && u.getPerfil().isItemHabilitado(rec, TipoPermissaoEnum.TOTAL)) {
                    return u;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
        return null;
    }

    @Override
    public List<Usuario> obterTodos() throws ServiceException {
        try {
            return usuarioDao.findAll();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Usuario obterPorId(Integer id) throws ServiceException {
        try {
            return usuarioDao.findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public UsuarioZW validarUsuarioZW(Empresa empresa, final String chave, final String userName, final String password, String roboControle, String userOamd) throws Exception {
        UsuarioTO usuarioZW = IntegracaoCadastrosWSConsumer.validarUsuario(chave, roboControle, userName, password);

        UsuarioZW usuario = new UsuarioZW();
        usuario.setNome(usuarioZW.getNome());
        usuario.setUserName(usuarioZW.getUserName());
        usuario.setCodigo(usuarioZW.getCodigo());
        usuario.setChave(chave);
        usuario.setMensagem(usuarioZW.getMensagem());
        usuario.setSenhaExpirada(usuarioZW.isExpirado());
        usuario.setForaDoHorario(usuarioZW.isForaDoHorario());
        usuario.setUsuarioMovelTreino(usuarioZW.getUsuarioMovelTreino());
        if (usuarioZW.getEmpresas().size() == 1) {
            usuario.setEmpresa(usuarioZW.getEmpresas().get(0));
        } else if (usuarioZW.getEmpresas().size() > 1) {
            for (EmpresaWS emp : usuarioZW.getEmpresas()) {
                usuario.getEmpresas().add(new SelectItem(emp.getCodigo(), emp.getNome()));
            }
        }

        if (usuario.getEmpresa() != null && usuario.getEmpresa().getCodigo() != null) {
            usuario.setPactoPay(permitePactoPay(empresa, usuario));
        }

        usuario.getEmpresasWS().addAll(usuarioZW.getEmpresas());
        usuario.getEmpresasAcessoModuloNota().addAll(usuarioZW.getEmpresasPermissaoModuloNotas());

        return usuario;
    }

    public boolean permitePactoPay(Empresa empresa, UsuarioZW usuario) {
        try {
            if (empresa.getModulos().contains(Modulo.PACTO_PAY.getSiglaModulo())) {
                String urlValidar = empresa.getRoboControle() + "/prest/pactopay/util?op=usuario&key=" + empresa.getChave() + "&u=" + usuario.getCodigo() + "&e=" + usuario.getEmpresa().getCodigo();
                String request = ExecuteRequestHttpService.executeRequestGET(urlValidar);
                org.primefaces.json.JSONObject json = new org.primefaces.json.JSONObject(request);
                return json.optBoolean("content");
            }
            return false;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public UsuarioZW validarUsuarioTR(final String chave, final String userName, final String password, String urlTreino, String userOamd) throws Exception {

        String url = urlTreino + "/prest/usuario/" + chave + "/login";

        Map<String, String> params = new HashMap<>();
        params.put("userName", userName);
        params.put("pwd", password);

        String respose = HttpRequestUtil.executeRequestInner(url, params, 10000);
        JSONObject objResponse = new JSONObject(respose);
        objResponse = objResponse.getJSONObject("return");

        JSONArray objEmpresas = objResponse.optJSONArray("empresas");
        objResponse.remove("empresas");

        UsuarioZW usuario = JSONMapper.getObject(objResponse, UsuarioZW.class);
        usuario.setChave(chave);
        usuario.setSomenteTreino(true);
        usuario.setCodigo(objResponse.optInt("cod"));
        usuario.setUserName(objResponse.optString("username"));

        for (int i = 0; i < objEmpresas.length(); i++) {
            JSONObject objEmpresa = objEmpresas.optJSONObject(i);
            if (objEmpresa.has("codigoProfessor")) {
                objEmpresa.remove("codigoProfessor");
            }
        }

        List<EmpresaWS> empresas = JSONMapper.getList(objEmpresas, EmpresaWS.class);
        usuario.setEmpresasWS(empresas);
        if (empresas.size() > 0) {
            usuario.setEmpresa(empresas.get(0));
        }

        return usuario;
    }

    @Override
    public Usuario prepararAmbiente(JSONObject json) throws ServiceException {
        try {
            final String timevld = json.get("timevld").toString();
            final String userName = json.getString("userName");
            if (!validacao.emptyString(timevld) && Long.parseLong(timevld) < Calendar.getInstance().getTimeInMillis()) {
                throw new ServiceException("Validação do usuário expirada, favor retorne ao Login e informe novamente senha e usuário");
            }
            if (userName == null || userName.isEmpty()) {
                throw new ServiceException(viewUtils.getMensagem("login.usuarioInvalido"));
            }
            return validarUsuario(userName, null, true);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public List<Perfil> obterPerfis() throws ServiceException {
        PerfilDao perfilDao = UtilContext.getBean(PerfilDao.class);
        try {
            return perfilDao.findAll();
        } catch (Exception ex) {
            Logger.getLogger(UsuarioServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex);
        }
    }

    @Override
    public Usuario obterPorUsername(String username) throws ServiceException {
        try {
            String s = "SELECT obj FROM Usuario obj where UPPER(username) = :username ";
            Map<String, Object> params = new HashMap<>();
            params.put("username", username.toUpperCase());
            return usuarioDao.findObjectByParam(s, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Usuario webhook(Pg4devWebhookDTO pg4dev) throws ServiceException {
        Usuario usuario = findByEmail(pg4dev.getEmail());
        if (usuario == null) {
            throw new ServiceException("Usuário não encontrado com o email " + pg4dev.getEmail());
        }
        usuario.setCurrentPg4devContainer(pg4dev.getUrlPg4dev());
        usuario.setCurrentDatePg4devContainer(Calendario.hoje());
        return usuarioDao.update(usuario);
    }

    @Override
    public Usuario findByEmail(String email) throws ServiceException {
        try {
            if (UteisValidacao.emptyString(email)) {
                throw new ServiceException("Email não informado");
            }

            String s = "SELECT obj FROM Usuario obj where lower(email) = :email ";
            Map<String, Object> params = new HashMap<>();
            params.put("email", email.toLowerCase());
            return usuarioDao.findObjectByParam(s, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
