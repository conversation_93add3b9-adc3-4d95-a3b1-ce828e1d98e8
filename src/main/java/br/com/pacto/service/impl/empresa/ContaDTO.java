package br.com.pacto.service.impl.empresa;

import java.util.List;

public class ContaDTO {
    private String inicioAssinatura;
    private String cartaoMascarado;
    private String proximaCobranca;
    private List<HistoricoCobrancaDTO> historico;

    public String getInicioAssinatura() {
        return inicioAssinatura;
    }

    public void setInicioAssinatura(String inicioAssinatura) {
        this.inicioAssinatura = inicioAssinatura;
    }

    public String getCartaoMascarado() {
        return cartaoMascarado;
    }

    public void setCartaoMascarado(String cartaoMascarado) {
        this.cartaoMascarado = cartaoMascarado;
    }

    public String getProximaCobranca() {
        return proximaCobranca;
    }

    public void setProximaCobranca(String proximaCobranca) {
        this.proximaCobranca = proximaCobranca;
    }

    public List<HistoricoCobrancaDTO> getHistorico() {
        return historico;
    }

    public void setHistorico(List<HistoricoCobrancaDTO> historico) {
        this.historico = historico;
    }
}
