package br.com.pacto.service.impl.infoMigracao;

import br.com.pacto.bean.infoMigracao.MigracaoRecursoFeedback;
import br.com.pacto.controller.json.infoMigracao.MigracaoRecursoFeedbackDTO;
import br.com.pacto.dao.intf.infoMigracao.MigracaoRecursoFeedbackDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.infoMigracao.MigracaoRecursoFeedbackService;
import br.com.pacto.util.enumeradores.TipoInfoMigracaoEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;

@Service
public class MigracaoRecursoFeedbackServiceImpl implements MigracaoRecursoFeedbackService {

    @Autowired
    private MigracaoRecursoFeedbackDao dao;

    public MigracaoRecursoFeedback inserir(MigracaoRecursoFeedback object) throws ServiceException {
        try {
            return getDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public MigracaoRecursoFeedback obterPorId(Integer id) throws ServiceException {
        try {
            return getDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<MigracaoRecursoFeedback> consultarTodos(boolean somenteComFeedback) throws ServiceException {
        try {
            if (somenteComFeedback) {
                return getDao().findByParam("SELECT obj FROM MigracaoRecursoFeedback obj " +
                        "WHERE length(coalesce(obj.feedback, '')) > 0 ORDER BY obj.id desc", new HashMap<>());
            } else {
                return getDao().findByParam("SELECT obj FROM MigracaoRecursoFeedback obj ORDER BY obj.id desc", new HashMap<>());
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public MigracaoRecursoFeedback feedback(String auth, MigracaoRecursoFeedbackDTO dto) throws ServiceException {
        try {
            MigracaoRecursoFeedback obj = new MigracaoRecursoFeedback();
            obj.setDataRegistro(Calendario.hoje());
            obj.setChave(dto.getChave());
            obj.setUsuario(dto.getUsuario());
            obj.setUsername(dto.getUsername());
            obj.setToken(auth);
            obj.setTelefone(obj.getTelefone());
            obj.setEmail(obj.getEmail());

            TipoInfoMigracaoEnum tipoInfoMigracaoEnum = null;
            try {
                tipoInfoMigracaoEnum = TipoInfoMigracaoEnum.valueOf(dto.getRecurso().toUpperCase());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            obj.setTipoInfoMigracao(tipoInfoMigracaoEnum != null ? tipoInfoMigracaoEnum.getId() : null);

            obj.setFeedback(dto.getFeedback());
            return this.inserir(obj);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public MigracaoRecursoFeedbackDao getDao() {
        return dao;
    }

    public void setDao(MigracaoRecursoFeedbackDao dao) {
        this.dao = dao;
    }

    public Integer total() throws ServiceException {
        try {
            return this.getDao().count("SELECT COUNT(obj) From MigracaoRecursoFeedback obj ");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }
}
