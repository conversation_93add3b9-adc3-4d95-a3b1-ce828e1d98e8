package br.com.pacto.service.impl.infra;

import br.com.pacto.bean.empresa.InfoInfraEnum;
import br.com.pacto.bean.infra.EstatisticasInfra;
import br.com.pacto.dao.intf.infra.EstatisticasInfraDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.infra.EstatisticasInfraService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class EstatisticasInfraServiceImpl implements EstatisticasInfraService {

    private final EstatisticasInfraDao estatisticasInfraDao;

    @Autowired
    public EstatisticasInfraServiceImpl(EstatisticasInfraDao estatisticasInfraDao) {
        this.estatisticasInfraDao = estatisticasInfraDao;
    }


    public EstatisticasInfra findByInfra(InfoInfraEnum infoInfraEnum) throws ServiceException {
        try {
            final String hql = "select obj from EstatisticasInfra obj where infoInfra = :infoInfraEnum";
            Map<String, Object> params = new HashMap<>();
            params.put("infoInfraEnum", infoInfraEnum);
            return estatisticasInfraDao.findObjectByParam(hql, params);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
