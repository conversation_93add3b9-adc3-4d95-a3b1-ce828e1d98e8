package br.com.pacto.service.impl.infra;

import br.com.pacto.bean.empresa.InfoInfraDTO;
import br.com.pacto.bean.empresa.InfoInfraEnum;
import br.com.pacto.bean.infra.InfraStatus;
import br.com.pacto.bean.infra.InfraStatusOperacao;
import br.com.pacto.controller.json.infra.dto.InfraStatusDTO;
import br.com.pacto.dao.intf.infra.InfraStatusDao;
import br.com.pacto.service.intf.infra.InfraStatusService;
import br.com.pacto.service.intf.infra.UsuarioLogadoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR> Karl<PERSON>
 * @since 12/02/19
 */
@Service
public class InfraStatusServiceImpl implements InfraStatusService {

    private final InfraStatusDao infraStatusDao;
    private final UsuarioLogadoService usuarioLogadoService;

    @Autowired
    public InfraStatusServiceImpl(InfraStatusDao infraStatusDao,
                                  UsuarioLogadoService usuarioLogadoService) {
        this.infraStatusDao = infraStatusDao;
        this.usuarioLogadoService = usuarioLogadoService;
    }

    @Override
    public void registrarInfraStart(InfraStatusDTO infraStatusDTO) throws Exception {
        final InfraStatus infraStatus = popularDadosComuns(infraStatusDTO);
        infraStatus.setOperacaoEnum(InfraStatusOperacao.START);

        infraStatusDao.registrarInfraStatus(infraStatus);
    }

    @Override
    public void registrarInfraStop(InfraStatusDTO infraStatusDTO) throws Exception {
        final InfraStatus infraStatus = popularDadosComuns(infraStatusDTO);
        infraStatus.setOperacaoEnum(InfraStatusOperacao.STOP);
        infraStatus.setUsuarios(usuarioLogadoService.countByIpAndInfoInfra(infraStatus.getIpExterno(), infraStatus.getInfoInfraEnum()));

        infraStatusDao.registrarInfraStatus(infraStatus);
    }

    private InfraStatus popularDadosComuns(InfraStatusDTO infraStatusDTO) {
        final InfraStatus infraStatus = new InfraStatus();
        infraStatus.setIpInterno(infraStatusDTO.getIpInterno());
        infraStatus.setIpExterno(infraStatusDTO.getIpExterno());
        infraStatus.setDataInicial(new Date().getTime());
        infraStatus.setInstancia(infraStatusDTO.getInstancia());
        infraStatus.setInfoInfra(getInfoInfra(infraStatusDTO.getInfoInfra()));
        infraStatus.setHostname(infraStatusDTO.getHostname());
        return infraStatus;
    }

    private String getInfoInfra(String infoInfra) {
        if (StringUtils.isBlank(infoInfra)) {
            return null;
        }

        for (InfoInfraEnum infoInfraEnum : InfoInfraEnum.values()) {
            if (infoInfraEnum.name().equalsIgnoreCase(infoInfra)) {
                return infoInfraEnum.name();
            }
        }

        return infoInfra;
    }

    public InfoInfraDTO findInfraByName(String name) {
        for (InfoInfraEnum infoInfraEnum : InfoInfraEnum.values()) {
            if (infoInfraEnum.name().equalsIgnoreCase(name)) {
                return new InfoInfraDTO(infoInfraEnum);
            }
        }

        return null;
    }

}
