package br.com.pacto.service.impl.zw;

import br.com.pacto.bean.zw.TabelaZwJson;
import br.com.pacto.bean.zw.TipoTabelaZwJson;
import br.com.pacto.dao.intf.zw.TabelaZwJsonDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.intf.zw.TabelaZwJsonService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLDecoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ulisses on 10/11/2016.
 */
@Service
public class TabelaZwJsonServiceImpl implements TabelaZwJsonService {

    @Autowired
    private TabelaZwJsonDao tabelaZwJsonDao;


    public List<TabelaZwJson> consultar(String nomeTabela,  TipoTabelaZwJson tipoTabela) throws Exception{
        Map<String, Object> params = new HashMap<String, Object>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT obj   ");
        sql.append("FROM TabelaZwJson obj ");
        sql.append("WHERE obj.tipoTabelaZwJson = :tipoTabela ");
        params.put("tipoTabela", tipoTabela);
        if ((nomeTabela != null) && (!nomeTabela.trim().equals(""))) {
            sql.append(" AND obj.nomeTabela = :nomeTabela ");
            params.put("nomeTabela", nomeTabela);
        }
        return tabelaZwJsonDao.findByParam(sql.toString(), params);
    }

    public void incluir(String jsonArrayTabelasZW, String nomeUsuario, TipoTabelaZwJson tipoTabela)throws Exception{
        excluirTodos(tipoTabela);
        Date dataInclusao = Calendario.hoje();
        JSONArray jsonArray = new JSONArray(jsonArrayTabelasZW);
        for (int i=0; i<jsonArray.length(); i++){
            JSONObject jsonObject = (JSONObject)jsonArray.get(i);
            TabelaZwJson tabelaZwJson = new TabelaZwJson();
            tabelaZwJson.setNomeTabela((String) jsonObject.get("nomeTabela"));
            tabelaZwJson.setDadosJson(jsonObject.get("dadosJson").toString());
            tabelaZwJson.setNomeUsuario(nomeUsuario);
            tabelaZwJson.setDataLancamento(dataInclusao);
            tabelaZwJson.setTipoTabelaZwJson(tipoTabela);
            tabelaZwJsonDao.insert(tabelaZwJson);
        }
    }

    public void incluir(String json, String nomeTabela, String nomeUsuario, TipoTabelaZwJson tipoTabela)throws Exception{
        Date dataInclusao = Calendario.hoje();
        TabelaZwJson tabelaZwJson = new TabelaZwJson();
        tabelaZwJson.setNomeTabela(nomeTabela);
        tabelaZwJson.setDadosJson(json);
        tabelaZwJson.setNomeUsuario(nomeUsuario);
        tabelaZwJson.setDataLancamento(dataInclusao);
        tabelaZwJson.setTipoTabelaZwJson(tipoTabela);
        tabelaZwJsonDao.insert(tabelaZwJson);
    }

    public void excluirTodos(TipoTabelaZwJson tipoTabela)throws Exception{
        List<TabelaZwJson> listaTabela = this.tabelaZwJsonDao.findAll();
        for (TabelaZwJson tabelaZwJson: listaTabela){
            if(tabelaZwJson.getTipoTabelaZwJson().equals(tipoTabela)){
                this.tabelaZwJsonDao.delete(tabelaZwJson);
            }
        }
    }


}
