package br.com.pacto.service.impl.empresa;

import br.com.pacto.bean.assinaturas.ConfigAssinatura;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.EmpresaSite;
import br.com.pacto.bean.empresa.Paths;
import br.com.pacto.controller.json.dto.CriarTrialNovoTreinoDTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dtos.PersonaDTO;
import br.com.pacto.security.interfaces.RequisicaoServico;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configassinatura.ConfigAssinaturaService;
import br.com.pacto.service.intf.empresa.EmpresaSiteService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class CobrancaPactoServiceImpl {

    @Autowired
    private DiscoveryService discoveryService;
    @Autowired
    private ConfigAssinaturaService configAssinaturaService;
    @Autowired
    private RequisicaoServico requisicaoServico;
    @Autowired
    private EmpresaSiteService empresaSiteService;

    private String urlApi() throws Exception {
        Paths paths = discoveryService.paths();
        return paths.getApiZw();
    }

    public String gerarToken(ConfigAssinatura configAssinatura) throws Exception {
        String urlApi = urlApi();
        JSONObject json = Uteis.doPost(new JSONObject(),
                urlApi + "/v2/vendas/" + configAssinatura.getChave() + "/tkn/12b1b5d9c8786de597a947fcf3e3da8a");
        return json.getString("return");
    }

    public String matriculaBase(ConfigAssinatura configAssinatura, String email) throws Exception {
        String urlApi = urlApi();
        JSONObject clienteJson = Uteis.doPost(new JSONObject(), urlApi + "/cliente/" + configAssinatura.getChave()
                + "/consultarCliente?cpf=&email=" + email
                + "&codigoEmpresaZW=" + configAssinatura.getEmpresa());
        return clienteJson.getJSONObject("return").getString("matricula");
    }

    public JSONObject sendToBase(ConfigAssinatura configAssinatura, CriarTrialNovoTreinoDTO trial) throws ServiceException {
        try {
            String token = gerarToken(configAssinatura);
            VendaDTO vendaDTO = new VendaDTO();
            vendaDTO.setUnidade(configAssinatura.getEmpresa());
            vendaDTO.setNrVezesDividir(1);
            vendaDTO.setPlano(configAssinatura.getPlano());
            vendaDTO.setNome(trial.getNome() + " - " + trial.getNomeapp());
            vendaDTO.setCpf("");
            vendaDTO.setDataNascimento(null);
            vendaDTO.setEmail(trial.getEmail());
            vendaDTO.setNomeCartao(trial.getNomecartao());
            vendaDTO.setNumeroCartao(trial.getNumerocartao().replace(" ", ""));
            vendaDTO.setValidade(trial.getValidade().replace("/", "/20"));
            vendaDTO.setCvv(trial.getCvv());
            vendaDTO.setTelefone(trial.getCelular());
            String urlApi = urlApi();
            return Uteis.doPost(new JSONObject(vendaDTO),
                    urlApi + "/v2/vendas/" + configAssinatura.getChave() + "/alunovendaapp/" + token);
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }

    }

    public ContaDTO contaPersonal() throws Exception {
        PersonaDTO persona = requisicaoServico.getPersona();
        if (persona == null) {
            throw new Exception("não foi possivel validar as credenciais para essa requisicao");
        }
        String chavePersonal = persona.getChave();
        ContaDTO contaDTO = new ContaDTO();
        Empresa empresaoamd = new Empresa();
        empresaoamd.setChave(chavePersonal);
        EmpresaSite empresaSite = empresaSiteService.obterPorEmpresaOAMD(empresaoamd);
        ConfigAssinatura configAssinatura = empresaSite.getConfigAssinatura();
        if(configAssinatura == null){
            configAssinatura = configAssinaturaService.pactoAtiva();
        }
        JSONObject dadosCliente = dadosClienteCobranca(configAssinatura.getChave(), empresaSite).getJSONObject("return");

        contaDTO.setInicioAssinatura(Uteis.getDataAplicandoFormatacao(empresaSite.getDataCadastro(), "dd 'de' MMMM 'de' yyyy"));
        contaDTO.setProximaCobranca(proximaCobranca(configAssinatura, dadosCliente.getInt("codigoPessoa")));
        contaDTO.setHistorico(new ArrayList<>());

        String urlApi = urlApi();
        JSONObject jsonAutorizacao = Uteis.doPost(new JSONObject(),
                urlApi + "/negociacao/" + configAssinatura.getChave() + "/obterAutorizacao?cliente=" + dadosCliente.getInt("codigo"));
        String nrcard = jsonAutorizacao.getString("cartao");
        contaDTO.setCartaoMascarado("**** **** **** " + nrcard.substring(12, 16));
        contaDTO.setHistorico(cobrancas(configAssinatura, dadosCliente.getInt("codigo")));
        return contaDTO;
    }

    private JSONObject dadosClienteCobranca(String chaveconf, EmpresaSite empresaSite) throws Exception {
        String urlApi = urlApi();
        return Uteis.doPost(new JSONObject(),
                urlApi + "/cliente/" + chaveconf + "/consultarDadosCliente?matricula=" + empresaSite.getMatriculaCobranca());
    }

    public String proximaCobranca(ConfigAssinatura configAssinatura, Integer codigoPessoaPersonal) throws Exception {
        String urlApi = urlApi();
        JSONObject jsonObject = Uteis.doPost(new JSONObject(),
                urlApi + "/cliente/" + configAssinatura.getChave() + "/consultarParcelasEmAberto?pessoa=" + codigoPessoaPersonal + "&empresa=" + configAssinatura.getEmpresa());
        List<Date> vencimentosParcelas = new ArrayList<>();
        JSONArray aReturn = jsonObject.getJSONArray("return");
        for (int i = 0; i < aReturn.length(); i++) {
            vencimentosParcelas.add(Uteis.getDate(aReturn.getJSONObject(i).getString("dataVencimento")));
        }
        Collections.sort(vencimentosParcelas);
        return vencimentosParcelas.isEmpty() ? "Não foi possível obter o próximo vencimento." : Uteis.getDataAplicandoFormatacao(vencimentosParcelas.get(0), "dd 'de' MMMM 'de' yyyy");
    }

    public List<HistoricoCobrancaDTO> cobrancas(ConfigAssinatura configAssinatura, Integer codigo) throws Exception {
        List<HistoricoCobrancaDTO> cobrancas = new ArrayList<>();
        String urlApi = urlApi();
        JSONObject jsonObject = Uteis.doPost(new JSONObject(),
                urlApi + "/negociacao/" + configAssinatura.getChave() + "/obterRecibosCliente?cliente=" + codigo);
        JSONArray aReturn = jsonObject.getJSONArray("return");
        for (int i = 0; i < aReturn.length(); i++) {
            JSONObject jsonRecibo = aReturn.getJSONObject(i);
            HistoricoCobrancaDTO historicoCobrancaDTO = new HistoricoCobrancaDTO();
            historicoCobrancaDTO.setData(jsonRecibo.getString("datapagamento"));
            historicoCobrancaDTO.setForma(jsonRecibo.getString("forma"));
            historicoCobrancaDTO.setValor(jsonRecibo.getString("valor"));
            cobrancas.add(historicoCobrancaDTO);
        }
        return cobrancas;
    }

    public String trocarCartao(CartaoDTO novoCartao) throws Exception {
        PersonaDTO persona = requisicaoServico.getPersona();
        if (persona == null) {
            throw new Exception("não foi possivel validar as credenciais para essa requisicao");
        }
        String chavePersonal = persona.getChave();
        Empresa empresaoamd = new Empresa();
        empresaoamd.setChave(chavePersonal);
        EmpresaSite empresaSite = empresaSiteService.obterPorEmpresaOAMD(empresaoamd);
        ConfigAssinatura configAssinatura = empresaSite.getConfigAssinatura();
        if(configAssinatura == null){
            configAssinatura = configAssinaturaService.pactoAtiva();
        }
        VendaDTO vendaDTO = new VendaDTO();
        vendaDTO.setUnidade(configAssinatura.getEmpresa());
        vendaDTO.setNomeCartao(novoCartao.getNomeCartao());
        vendaDTO.setNumeroCartao(novoCartao.getNumeroCartao().replace(" ", ""));
        vendaDTO.setValidade(novoCartao.getValidade().replace("/", "/20"));
        vendaDTO.setCvv(novoCartao.getCvv());
        String urlApi = urlApi();
        JSONObject retorno = Uteis.doPost(new JSONObject(vendaDTO),
                urlApi + "/v2/vendas/" + configAssinatura.getChave() + "/cobrarparcelasabertas/" + empresaSite.getMatriculaCobranca());
        if (!retorno.getString("return").equalsIgnoreCase("APROVADA")) {
            throw new Exception(retorno.getString("return"));
        }
        return "**** **** **** " + novoCartao.getNumeroCartao().replace(" ", "").substring(12, 16);
    }

    public EmpresaSite empresaSite() throws Exception {
        PersonaDTO persona = requisicaoServico.getPersona();
        if (persona == null) {
            throw new Exception("não foi possivel validar as credenciais para essa requisicao");
        }
        String chavePersonal = persona.getChave();
        Empresa empresaoamd = new Empresa();
        empresaoamd.setChave(chavePersonal);
        return empresaSiteService.obterPorEmpresaOAMD(empresaoamd);
    }

    public EmpresaSite empresaSite(String chave) throws Exception {
        Empresa empresaoamd = new Empresa();
        empresaoamd.setChave(chave);
        return empresaSiteService.obterPorEmpresaOAMD(empresaoamd);
    }

    public Object obterStatusPersonal(String chave) throws Exception {
        EmpresaSite empresaSite = empresaSite(chave);
        ClientDiscoveryDataDTO cliente = discoveryService.obterUrls(chave);
        String urlZW = cliente.getServiceUrls().getZwUrl();
        JSONObject retorno = Uteis.doGet(urlZW + "/prest/obterStatusPersonal?matricula=" + empresaSite.getMatriculaCobranca() + "&chave=" + chave,null);
        if (retorno.toString().contains("Ocorreu algo inesperado ao processar a requisição.")){
            RetornoErroDTO erroDTO = new RetornoErroDTO();
            erroDTO.setErro(retorno.getString("mensagem"));
            return erroDTO;
        } else {
            return new RetornoSituacaoPersonalGestorDTO(retorno);
        }
    }

    public String pagarAtrasadoApp(CartaoDTO novoCartao, String chave) throws Exception {
        EmpresaSite empresaSite = empresaSite(chave);
        ConfigAssinatura configAssinatura = empresaSite.getConfigAssinatura();
        if(configAssinatura == null){
           configAssinatura = configAssinaturaService.pactoAtiva();
        }
        VendaDTO vendaDTO = new VendaDTO();
        vendaDTO.setUnidade(configAssinatura.getEmpresa());
        vendaDTO.setNomeCartao(novoCartao.getNomeCartao());
        vendaDTO.setNumeroCartao(novoCartao.getNumeroCartao().replace(" ", ""));
        vendaDTO.setValidade(novoCartao.getValidade().replace("/", "/20"));
        vendaDTO.setCvv(novoCartao.getCvv());
        String urlApi = urlApi();
        JSONObject retorno = Uteis.doPost(new JSONObject(vendaDTO),
                urlApi + "/v2/vendas/" + configAssinatura.getChave() + "/cobrarparcelasabertas/" + empresaSite.getMatriculaCobranca());
        if (!retorno.getString("return").equalsIgnoreCase("APROVADA")) {
            throw new Exception(retorno.getString("return"));
        }
        return "**** **** **** " + novoCartao.getNumeroCartao().replace(" ", "").substring(12, 16);
    }

}
