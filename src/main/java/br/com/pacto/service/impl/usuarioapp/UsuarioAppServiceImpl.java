/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.usuarioapp;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.feed.usuarioapp.UsuarioApp;
import br.com.pacto.dao.Dao;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.dao.intf.usuarioapp.UsuarioAppDao;
import br.com.pacto.integracao.IntegracaoCadastrosWSConsumer;
import br.com.pacto.json.UsuarioAppBasicoJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.usuarioapp.UsuarioAppService;
import br.com.pacto.service.intf.usuarioapp.UsuarioAppSincroniaService;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.UteisJSON;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Service
public class UsuarioAppServiceImpl implements UsuarioAppService{

    @Autowired
    private UsuarioAppDao usuarioAppDao;
    @Autowired
    private EmpresaDao empresaDao;

    @Autowired
    private UsuarioAppSincroniaService usuarioAppSincroniaService;

    
    @Override
    public UsuarioApp inserir(UsuarioApp object) throws ServiceException {
        try {
            return usuarioAppDao.insert(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    public JSONArray obterListaPorChave(String chave) throws Exception{
        JSONArray array = new JSONArray();
        Dao daoOAMD = new Dao();
        try {
            ResultSet rs = daoOAMD.criarConsulta("SELECT deviceid, r.email, useragent FROM registration r\n"
                    + "INNER JOIN usuarioapp u ON u.email = r.email\n"
                    + "WHERE empresa_chave = '" + chave + "'");
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("deviceid", rs.getString("deviceid"));
                json.put("email", rs.getString("email"));
                json.put("useragent", rs.getString("useragent"));
                array.put(json);
            }
        } finally {
            daoOAMD.fecharConexao();
        }
        return array;
    }

    @Override
    public UsuarioApp obterPorEmail(String email) throws ServiceException {
        try {
            if (email == null || email.isEmpty()){
                return null;
            }
            StringBuilder sb = new StringBuilder();
            sb.append(" where UPPER(email) = :email ");
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("email",email.toUpperCase());
            List<UsuarioApp> usuarios = usuarioAppDao.findByParam(sb,p);
            if(!usuarios.isEmpty() && usuarios.size() == 1){
                return usuarios.get(0);
            }
            return null;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Override
    public List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosZW(Empresa e,
            final String celular, final String email) throws ServiceException {
        try {
            StringBuilder sql;
            if (celular != null) {
                sql = new StringBuilder("select distinct \n");
                sql.append("sit.codigoCliente as codigocliente, \n");
                sql.append("p.nome,regexp_replace(t.numero, '[^0-9]','','g') as celular,em.email, ");
                sql.append("sit.codigoUsuarioMovel, um.nome as nomeUsuarioMovel, um.ativo as statusUsuarioMovel ");
                sql.append("from pessoa p\n ");
                sql.append("inner join telefone t on t.pessoa = p.codigo and t.tipotelefone = 'CE' ")
                        .append("and regexp_replace(t.numero, '[^0-9]','','g') = '").append(celular).append("'\n ");
                sql.append("inner join email em on em.pessoa = p.codigo and em.email = '").append(email).append("' \n");
                sql.append("inner join situacaoclientesinteticodw sit on sit.codigopessoa = p.codigo ");
                sql.append("left join usuariomovel um on um.cliente = sit.codigocliente ");
            } else {
                sql = new StringBuilder("select distinct \n");
                sql.append("sit.codigoCliente as codigocliente, p.nome,em.email, ");
                sql.append("sit.codigoUsuarioMovel, um.nome as nomeUsuarioMovel, um.ativo as statusUsuarioMovel ");
                sql.append("from pessoa p\n ");                
                sql.append("inner join email em on em.pessoa = p.codigo and em.email = '").append(email).append("' \n");
                sql.append("inner join situacaoclientesinteticodw sit on sit.codigopessoa = p.codigo ");
                sql.append("left join usuariomovel um on um.cliente = sit.codigocliente ");
            }
            JSONArray arr = new JSONArray(IntegracaoCadastrosWSConsumer.consultaGenerica(
                    e.getRoboControleSemHTTPS(), e.getChave(), sql.toString()));
            return UteisJSON.jsonToListObject(UsuarioAppBasicoJSON.class, arr);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }


    private  boolean isMultIds ( JSONArray arr) throws JSONException {
        String id  = null ;
        boolean difIds = false;
        for (int i = 0; i < arr.length(); i++) {
            if( id != null){
                if ( !(arr.getJSONObject(i).get("codigocliente").toString()).equals(id)){
                    difIds = true;
                }
            }else{
                id = arr.getJSONObject(i).get("codigocliente").toString();
            }
        }
        return difIds;
    }

    private  JSONObject getUser ( JSONArray arr) throws JSONException {
        JSONObject userSelected = null;
        if(arr.length() > 0 ){
            for (int i = 0; i < arr.length(); i++) {
                if(!(arr.getJSONObject(i).get("codigousuariomovel").toString()).equals("0") && !(arr.getJSONObject(i).get("codigousuariomovel").toString()).equals("") ) {
                    return arr.getJSONObject(i);
                }
                if(!(arr.getJSONObject(i).get("email").toString()).equals("")){
                    userSelected = arr.getJSONObject(i);
                }
            }
        }

        return userSelected;
    }


    @Override
    public List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosByCelularZW(Empresa e,
         final String celular) throws ServiceException {

        try {
            String formatedNumber = celular.length() > 8 ? celular.substring(5 , celular.length()) : celular ;

            StringBuilder sql;

                sql = new StringBuilder("select distinct \n");
                sql.append("sit.codigoCliente as codigocliente, \n");
                sql.append("p.nome,regexp_replace(t.numero, '[^0-9]','','g') as celular, em.email, ");
                sql.append("sit.codigoUsuarioMovel, um.nome as nomeUsuarioMovel, um.ativo as statusUsuarioMovel ");
                sql.append("from pessoa p\n ");
                sql.append("inner join telefone t on t.pessoa = p.codigo and t.tipotelefone = 'CE' ");
                sql.append("left join email em on em.pessoa = p.codigo ");
                sql.append("inner join situacaoclientesinteticodw sit on sit.codigopessoa = p.codigo ");
                sql.append("left join usuariomovel um on um.cliente = sit.codigocliente ");
                sql.append("where t.numero LIKE '%").append(formatedNumber).append("'\n ");

            JSONArray arr = new JSONArray(IntegracaoCadastrosWSConsumer.consultaGenerica(
                    e.getRoboControleSemHTTPS(), e.getChave(), sql.toString()));

            if(arr.length() > 0 &&  isMultIds(arr)){
                return UteisJSON.jsonToListObject(UsuarioAppBasicoJSON.class, arr);
            }else{
                JSONArray newArr = new JSONArray();
                JSONObject user =  getUser(arr);
                if(user != null ){
                    newArr.put(user);
                }else{
                    newArr = arr;
                }
                return UteisJSON.jsonToListObject(UsuarioAppBasicoJSON.class, newArr);
            }

        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosByCelularApp(Empresa e,  final String ddi, final String ddd,
                final String celular) throws ServiceException {

        try {
            String formatedNumberSimple =  UteisValidacao.emptyString(ddd) ? celular : ddd + celular ;
            String formatedNumber =  UteisValidacao.emptyString(ddd) ? celular : '(' + ddd + ')' + celular ;

            /*
             *   - A consulta deve trazer todos os Clientes relacionados ao numero de telefone e
             *   - A consulta deve trazer apenas o Colaborador principal relacionado ao numero de telefone.
             *   - - O colaborador principal eh o que esta relacionado a algum Usuario.
             *   - - A Pessoa que possui apenas um Colaborador e este nao tem Usuario, entao essse eh o Colaborador principal.
             * */

            StringBuilder sql = new StringBuilder("select distinct ");
            sql.append("cli.codigo as codigocliente, ");
            sql.append("col.codigo as codigoColaborador, ");
            sql.append("pes.nome, ");
            sql.append("ema.email, ");
            sql.append("regexp_replace(tel.numero, '[^0-9]','','g') as celular, ");
            sql.append("umo.codigo as codigousuariomovel, ");
            sql.append("umo.nome as nomeUsuarioMovel, ");
            sql.append("umo.ativo as statusUsuarioMovel, ");
            sql.append("pes.fotokey as urlFoto, ");
            sql.append("cli.matricula, ");
            sql.append("usu.nome as nomeUsuario, ");
            sql.append("usu.username as userNameUsuario, ");
            sql.append("usu.codigo as codUsuario ");
            sql.append("from usuariomovel umo ");
            sql.append("full join (select * from colaborador col ");
            sql.append("where col.codigo in (select c.codigo from colaborador c where c.pessoa in (");
            sql.append("select c.pessoa ");
            sql.append("from colaborador c ");
            sql.append("left join pessoa  on c.pessoa = pessoa.codigo ");
            sql.append("left join telefone t on t.pessoa = c.pessoa where (t.numero ilike '%");
            sql.append(formatedNumberSimple);
            sql.append("%' or t.numero ilike '%");
            sql.append(formatedNumber);
            sql.append("%') group by c.pessoa, pessoa.codigo HAVING Count(*)  < 2  ORDER by pessoa.codigo)");
            sql.append(") or col.codigo in (select c.codigo from colaborador c ");
            sql.append(" inner join pessoa p on");
            sql.append(" c.pessoa = p.codigo");
            sql.append(" inner join telefone t on");
            sql.append(" t.pessoa = c.pessoa");
            sql.append(" inner join empresa e on");
            sql.append(" c.empresa = e.codigo");
            sql.append(" where");
            sql.append(" (t.numero ilike '%");
            sql.append(formatedNumberSimple);
            sql.append("%' or t.numero ilike '%");
            sql.append(formatedNumber);
            sql.append("%') and e.ativa is true))col on col.codigo = umo.colaborador ");
            sql.append("full join cliente cli on cli.codigo = umo.cliente ");
            sql.append("left join telefone tel on tel.pessoa = col.pessoa or tel.pessoa = cli.pessoa ");
            sql.append("left join usuario usu on usu.colaborador = col.codigo ");
            sql.append("left join pessoa pes on pes.codigo = col.pessoa or pes.codigo = cli.pessoa ");
            sql.append("left join (select pessoa, min(email) as email  from email  as em where em.emailcorrespondencia = true group by em.pessoa ) ema on ema.pessoa = cli.pessoa or ema.pessoa = col.pessoa ");
            sql.append("where (REGEXP_REPLACE(tel.numero, '[^0-9]', '', 'g') ilike '%");
            sql.append(formatedNumberSimple);
            sql.append("%' or REGEXP_REPLACE(tel.numero, '[^0-9]', '', 'g') ilike '%");
            sql.append(formatedNumber);
            sql.append("%') and " +
                    " umo.ativo = true " +
                    " order by pes.nome ");

            JSONArray arr = new JSONArray(IntegracaoCadastrosWSConsumer.consultaGenerica(
                    e.getRoboControleSemHTTPS(), e.getChave(), sql.toString()));

            List<UsuarioAppBasicoJSON> usuarioAppBasicoJSONS  = UteisJSON.jsonToListObject(UsuarioAppBasicoJSON.class, arr);
            Iterator<UsuarioAppBasicoJSON> iterator = usuarioAppBasicoJSONS.iterator();
            while (iterator.hasNext()) {
                UsuarioAppBasicoJSON usuario = iterator.next();
                Integer codigoUsuario = 0;
                if(!UteisValidacao.emptyString(usuario.getUrlfoto()))
                {
                    usuario.setUrlfoto(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/" + usuario.getUrlfoto());
                }
                if(usuario.getStatususuariomovel() == null)
                {
                    usuario.setStatususuariomovel(false);
                }
                if(!Uteis.intNullOrEmpty(usuario.getCodigocolaborador())) {
                    if(Uteis.intNullOrEmpty(usuario.getCodusuario())) {
                        iterator.remove();
                        continue;
                    }
                    usuario.setCodigousuariomovel(null);
                    usuario.setNomeusuariomovel(null);
                    usuario.setStatususuariomovel(null);
                }
                usuario.setCodigousuariotreino(obtemCodigoUsuarioTreino(usuario, e));
            }
            return usuarioAppBasicoJSONS;
        } catch (Exception ex) {
            throw new ServiceException("Não foi possivel completar essa ação");
        }
    }

    private Integer obtemCodigoUsuarioTreino(UsuarioAppBasicoJSON usuario, Empresa e) throws ServiceException {
        try {
            String urlCodigoUsuarioTreino = e.getUrlTreino() + "/prest/usuario/" + e.getChave() + "/app/codigoUsuarioTreino?";

            if(!UteisValidacao.emptyNumber(usuario.getCodigocolaborador())) {
                urlCodigoUsuarioTreino += "codigoColaborador=" + urlCodigoUsuarioTreino + usuario.getCodigocolaborador();
            }
            if(!UteisValidacao.emptyString(usuario.getMatricula())) {
                urlCodigoUsuarioTreino += "matricula=" + Integer.parseInt(usuario.getMatricula());
            }

            String resposta = ExecuteRequestHttpService.executeHttpRequest(urlCodigoUsuarioTreino,null,
                    new HashMap<>(), ExecuteRequestHttpService.METODO_GET, "utf-8");
            JSONObject json = new JSONObject(resposta);
            return json.optInt("codigoUsuario");
        } catch (Exception ex) {
            Uteis.logar(ex, UsuarioAppServiceImpl.class);
        }
        return null;
    }

    @Override
    public List<UsuarioAppBasicoJSON> verificarDadosBasicosByCelularColaborador(Empresa e, String ddi, String ddd, String celular) throws ServiceException {

        try {
            StringBuilder sql = new StringBuilder("select distinct ");
            sql.append("cli.codigo as codigocliente, ");
            sql.append("col.codigo as codigoColaborador, ");
            sql.append("pes.nome, ");
            sql.append("em.email, ");
            sql.append("regexp_replace(tel.numero, '[^0-9]','','g') as celular, ");
            sql.append("umo.codigo as codigousuariomovel, ");
            sql.append("umo.nome as nomeUsuarioMovel, ");
            sql.append("umo.ativo as statusUsuarioMovel, ");
            sql.append("pes.fotokey as urlFoto, ");
            sql.append("cli.matricula ");
            sql.append("from telefone tel ");
            sql.append("join pessoa pes on pes.codigo = tel.pessoa ");
            sql.append("left join colaborador col on col.pessoa = pes.codigo  ");
            sql.append("left join usuariomovel umo on umo.colaborador = col.codigo ");
            sql.append("left join email em on em.pessoa = pes.codigo ");
            sql.append("left join cliente cli on cli.pessoa = pes.codigo ");
            sql.append("where ((REGEXP_REPLACE(tel.numero, '[^0-9]', '', 'g') ilike '%"+ddd+celular+"%' ");
            sql.append("or (REGEXP_REPLACE(tel.numero, '[^0-9]', '', 'g') ilike '%(" + ddd + ")" + celular + "%') ");
            sql.append("order by pes.nome ");
            sql.append("limit 1 ");

            JSONArray arr = new JSONArray(IntegracaoCadastrosWSConsumer.consultaGenerica(
                    e.getRoboControleSemHTTPS(), e.getChave(), sql.toString()));

            List<UsuarioAppBasicoJSON> usuarioAppBasicoJSONS  = UteisJSON.jsonToListObject(UsuarioAppBasicoJSON.class, arr);
            for(UsuarioAppBasicoJSON usuario: usuarioAppBasicoJSONS)
            {
                if(!UteisValidacao.emptyString(usuario.getUrlfoto()))
                {
                    usuario.setUrlfoto(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/" + usuario.getUrlfoto());
                }
                if(usuario.getStatususuariomovel() == null)
                {
                    usuario.setStatususuariomovel(false);
                }
                usuario.setCodigousuariotreino(obtemCodigoUsuarioTreino(usuario, e));
            }
            return usuarioAppBasicoJSONS;
        }catch (Exception ex) {
            throw new ServiceException("Não foi possivel completar essa ação");
        }
    }

    @Override
    public UsuarioApp alterar(UsuarioApp object) throws ServiceException {
        try {
            return usuarioAppDao.update(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    public String baixarEmailsEmpresas(){
        try {
            List<Empresa> all = empresaDao.findAll();
            for(Empresa e : all){
                try {
                    Uteis.logar(null, "VOU BAIXAR OS EMAILS DO BANCO "+e.getName());
                    Uteis.logar(null, "GRAVEI OS EMAILS DA EMPRESA "+e.getName());
                } catch (Exception eX) {
                    Uteis.logar(null, "PROBLEMAS COM OS EMAILS DA EMPRESA "+e.getName()
                            +". Exc:"+eX.getMessage());
                }
                
            }
        } catch (Exception e) {
            Uteis.logar(e, UsuarioAppServiceImpl.class);
        }
        return "processofinalizado";
    }


    @Override
    public void alterarNomeFotoToken(UsuarioApp usuarioApp) throws ServiceException {
        try {
            usuarioAppDao.updateAlgunsCampos(new String[]{"nome"}, new Object[]{usuarioApp.getNome()}, new String[]{"email"}, new Object[]{usuarioApp.getEmail()});
            usuarioAppDao.updateAlgunsCampos(new String[]{"fotoKey"}, new Object[]{usuarioApp.getFotoKey()}, new String[]{"email"}, new Object[]{usuarioApp.getEmail()});
            usuarioAppDao.updateAlgunsCampos(new String[]{"tokenFacebook"}, new Object[]{usuarioApp.getTokenFacebook()}, new String[]{"email"}, new Object[]{usuarioApp.getEmail()});
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void executeNativeSQL(String sql) throws ServiceException {
        try {
            usuarioAppDao.executeNativeSQL(sql);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Override
    public UsuarioApp gerarUsuario(final String ctx, final String email, String cpf,
                                   String telefone, String dataNascimento, String senha) throws ServiceException {
        Empresa emp = usuarioAppSincroniaService.enfileirarUsuario(ctx, email, cpf, telefone, dataNascimento, senha);
        UsuarioApp usuarioRetornar = new UsuarioApp();
        usuarioRetornar.setEmpresa(emp);
        return usuarioRetornar;
    }

    @Override
    public List<UsuarioApp> descobrirUsuarioV3(String parametro) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            Map<String, Object> params = new HashMap<>();
            params.put("email", parametro.toUpperCase());

            query.append("WHERE UPPER(obj.email) = :email \n");

            String cpf = Uteis.tirarCaracteres(parametro, true);
            if (cpf != null && cpf.length() == 11) {
                params.put("cpf", cpf);
                query.append(" OR replace(replace(obj.cpf, '.', ''), '-','') = :cpf ");
            }

            String telefone = Uteis.tirarCaracteres(parametro, true);
            if (telefone != null && telefone.length() >= 10) {
                params.put("tel", "%" + telefone + "%");
                query.append("OR replace(replace(replace(obj.telefone, '(', ''), ')', ''), '-','') like :tel ");
            }

            return usuarioAppDao.findByParam(query, params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Boolean obterSituacaoUsuario(String key, Empresa e, Integer codEmpresa, Integer codUsuario) {
        return IntegracaoCadastrosWSConsumer.situacaoUsuario(e.getRoboControleSemHTTPS(), key, codUsuario, codEmpresa);
    }

    @Override
    public List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosByCelularTreinoIndepApp(Empresa e, final String ddi, final String ddd,
                                                                                             final String celular) throws ServiceException {

        try {
            String urlRequest = e.getUrlTreino() + "/prest/usuario/" + e.getChave() +
                    "/app/obterIdentidadeDadosBasicosCelular?telefone=" + celular + "&ddi=" + (ddi == null ? "" : ddi) + "&ddd=" + ddd;
            String resposta = ExecuteRequestHttpService.executeHttpRequest(urlRequest,null,
                    new HashMap<>(), ExecuteRequestHttpService.METODO_GET, "utf-8");
            JSONObject json = new JSONObject(resposta);
            JSONArray array = new JSONArray();
            try{
                array = json.getJSONArray("sucesso");
            }
            catch (Exception ex){
                throw new ServiceException(json.getString("erro"));
            }
            List<UsuarioAppBasicoJSON> usuarioAppBasicoJSONS  = UteisJSON.jsonToListObject(UsuarioAppBasicoJSON.class, array);
            for(UsuarioAppBasicoJSON usuario: usuarioAppBasicoJSONS)
            {
                if(!UteisValidacao.emptyString(usuario.getUrlfoto()))
                {
                    usuario.setUrlfoto(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/" + usuario.getUrlfoto());
                }
                usuario.setCodigousuariotreino(usuario.getCodigousuariomovel());
            }
            return usuarioAppBasicoJSONS;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public String gerarUsuarioTreinoIndependente(Empresa e, String email, String senha, Integer codigoColaborador, Integer codigoCliente){
        String resposta = new String();
        try {
            String urlRequest = e.getUrlTreino() + "/prest/usuario/" + e.getChave() +
                    "/app/gerarUsuarioTreinoIndependente?";
            if(!UteisValidacao.emptyNumber(codigoCliente)){
                urlRequest = urlRequest.concat("&codigoCliente=" + codigoCliente);
            }
            else{
                urlRequest = urlRequest.concat("&codigoColaborador=" + codigoColaborador);
            }
            urlRequest = urlRequest.concat("&email=" + email + "&senha=" + senha);

            String respostaTW = ExecuteRequestHttpService.executeHttpRequest(urlRequest,null,
                    new HashMap<>(), ExecuteRequestHttpService.METODO_GET, "utf-8");
            JSONObject json = new JSONObject(respostaTW);
            if(json.has("sucesso")){
                resposta = json.getString("sucesso");
            }else{
                if(json.has("erro")){
                    resposta = json.getString("erro");
                }
                else{
                    throw new ServiceException("Não foi possível completar essa ação");
                }
            }
    } catch (ServiceException ex) {
            ex.printStackTrace();
        } catch (JSONException ex) {
            ex.printStackTrace();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return resposta;
    }

    @Override
    public List<UsuarioAppBasicoJSON> verificarUsuarioEmail(Empresa e, String email) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT DISTINCT ");
            sql.append("cli.codigo AS codigocliente, ");
            sql.append("col.codigo AS codigoColaborador, ");
            sql.append("pes.nome, ");
            sql.append("ema.email, ");
            sql.append("STRING_AGG(DISTINCT REGEXP_REPLACE(tel.numero, '[^0-9]', '', 'g'), ',') AS celular, ");
            sql.append("umo.codigo AS codigousuariomovel, ");
            sql.append("umo.nome AS nomeUsuarioMovel, ");
            sql.append("umo.ativo AS statusUsuarioMovel, ");
            sql.append("pes.fotokey AS urlFoto, ");
            sql.append("cli.matricula, ");
            sql.append("usu.nome AS nomeUsuario, ");
            sql.append("usu.username AS userNameUsuario, ");
            sql.append("usu.codigo AS codUsuario ");
            sql.append("FROM usuariomovel umo ");
            sql.append("FULL JOIN colaborador col ON col.codigo = umo.colaborador ");
            sql.append("FULL JOIN cliente cli ON cli.codigo = umo.cliente ");
            sql.append("LEFT JOIN telefone tel ON tel.pessoa = col.pessoa OR tel.pessoa = cli.pessoa ");
            sql.append("LEFT JOIN usuario usu ON usu.colaborador = col.codigo ");
            sql.append("LEFT JOIN pessoa pes ON pes.codigo = col.pessoa OR pes.codigo = cli.pessoa ");
            sql.append("LEFT JOIN email ema ON ema.pessoa = cli.pessoa OR ema.pessoa = col.pessoa ");
            sql.append("WHERE (UPPER(ema.email) = UPPER('" + email + "')  or UPPER(umo.nome) = UPPER('" + email + "') or UPPER(usu.username) = UPPER('" + email + "')) AND umo.ativo = true ");
            sql.append("GROUP BY cli.codigo, col.codigo, pes.nome, ema.email, umo.codigo, umo.nome, umo.ativo, pes.fotokey, cli.matricula, usu.nome, usu.username, usu.codigo ");
            sql.append("ORDER BY pes.nome;");

            JSONArray arr = new JSONArray(IntegracaoCadastrosWSConsumer.consultaGenerica(
                    e.getRoboControleSemHTTPS(), e.getChave(), sql.toString()));

            List<UsuarioAppBasicoJSON> usuarioAppBasicoJSONS  = UteisJSON.jsonToListObject(UsuarioAppBasicoJSON.class, arr);
            Iterator<UsuarioAppBasicoJSON> iterator = usuarioAppBasicoJSONS.iterator();
            while (iterator.hasNext()) {
                UsuarioAppBasicoJSON usuario = iterator.next();
                if(!UteisValidacao.emptyString(usuario.getUrlfoto()))
                {
                    usuario.setUrlfoto(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/" + usuario.getUrlfoto());
                }
                if(usuario.getStatususuariomovel() == null)
                {
                    usuario.setStatususuariomovel(false);
                }
                if(!Uteis.intNullOrEmpty(usuario.getCodigocolaborador())) {
                    if(Uteis.intNullOrEmpty(usuario.getCodusuario())) {
                        iterator.remove();
                        continue;
                    }
                    usuario.setCodigousuariomovel(null);
                    usuario.setNomeusuariomovel(null);
                    usuario.setStatususuariomovel(null);
                }
                usuario.setCodigousuariotreino(obtemCodigoUsuarioTreino(usuario, e));
            }
            return usuarioAppBasicoJSONS;
            } catch (Exception ex) {
                throw new ServiceException(ex.getMessage());
            }
    }

    @Override
    public List<UsuarioAppBasicoJSON> verificarIdentidadeDadosBasicosByEmailTreinoIndepApp(Empresa e, String email) throws ServiceException {


        try {
            String urlRequest = e.getUrlTreino() + "/prest/usuario/" + e.getChave() +
                    "/app/obterIdentidadeDadosBasicosEmail?email=" + URLEncoder.encode(email, StandardCharsets.UTF_8.toString());
            String resposta = ExecuteRequestHttpService.executeHttpRequest(urlRequest,null,
                    new HashMap<>(), ExecuteRequestHttpService.METODO_GET, "utf-8");
            JSONObject json = new JSONObject(resposta);
            JSONArray array = new JSONArray();
            try{
                array = json.getJSONArray("sucesso");
            }
            catch (Exception ex){
                throw new ServiceException(json.getString("erro"));
            }
            List<UsuarioAppBasicoJSON> usuarioAppBasicoJSONS  = UteisJSON.jsonToListObject(UsuarioAppBasicoJSON.class, array);
            Iterator<UsuarioAppBasicoJSON> iterator = usuarioAppBasicoJSONS.iterator();
            while (iterator.hasNext()) {
                UsuarioAppBasicoJSON usuario = iterator.next();
                if(!UteisValidacao.emptyString(usuario.getUrlfoto()))
                {
                    usuario.setUrlfoto(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/" + usuario.getUrlfoto());
                }
                usuario.setCodigousuariotreino(usuario.getCodigousuariomovel());
            }
            return usuarioAppBasicoJSONS;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }
}
