/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.feedApp;

import br.com.pacto.bean.feed.usuarioapp.UsuarioApp;
import br.com.pacto.bean.feedApp.FeedApp;
import br.com.pacto.bean.feedApp.FeedAppComentario;
import br.com.pacto.bean.feedApp.FeedAppLike;
import br.com.pacto.dao.Dao;
import br.com.pacto.dao.intf.feedApp.FeedAppComentarioDao;
import br.com.pacto.dao.intf.feedApp.FeedAppLikeDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.feedApp.FeedAppComentarioService;
import br.com.pacto.service.intf.feedApp.FeedAppLikeService;
import br.com.pacto.service.intf.usuarioapp.UsuarioAppService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR> Felipe
 */
@Service
public class FeedAppLikeServiceImpl implements FeedAppLikeService {

    @Autowired
    private FeedAppLikeDao feedAppLikeDao;

    @Override
    public FeedAppLike obterPorId(Integer id) throws ServiceException {
        try {
            return feedAppLikeDao.findObjectByAttribute("codigo", id);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public FeedAppLike inserir(FeedAppLike object) throws ServiceException {
        try {
            return feedAppLikeDao.insert(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public FeedAppLike alterar(FeedAppLike object) throws ServiceException {
        try {
            return feedAppLikeDao.update(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void excluir(FeedAppLike object) throws ServiceException {
        try {
            feedAppLikeDao.delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public List<FeedAppLike> obterPorFeedAppUsuarioApp(FeedApp feedApp, UsuarioApp usuarioApp) throws ServiceException {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append(" where obj.feedApp.codigo = :feedAppCodigo AND UPPER(obj.usuarioApp.email) = :emailUsuario");
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("feedAppCodigo", feedApp.getCodigo());
            p.put("emailUsuario", usuarioApp.getEmail().toUpperCase());
            return feedAppLikeDao.findByParam(sb, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public boolean verificaUsuarioDeuLike(String email, FeedApp feedApp) throws ServiceException {
        try {
//            StringBuilder sb = new StringBuilder();
//            sb.append(" where obj.feedApp.codigo = :feedAppCodigo AND UPPER(obj.usuarioApp.email) = :emailUsuario");
//            Map<String, Object> p = new HashMap<String, Object>();
//            p.put("emailUsuario", email.toUpperCase());
//            p.put("feedAppCodigo", feedApp.getCodigo());
//            List<FeedAppLike> listaFeedAppLike = feedAppLikeDao.findByParam(sb, p);

            UsuarioApp usuarioApp = new UsuarioApp();
            usuarioApp.setEmail(email);
            List<FeedAppLike> listaFeedAppLike = obterPorFeedAppUsuarioApp(feedApp, usuarioApp);
            return !UteisValidacao.emptyList(listaFeedAppLike);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
}
