package br.com.pacto.service.impl.produtoPacto;

import br.com.pacto.bean.produtoPacto.ProdutoPacto;
import br.com.pacto.bean.produtoPacto.ProdutoPactoAvaliacao;
import br.com.pacto.controller.json.produtoPacto.ProdutoPactoDTO;
import br.com.pacto.dao.intf.produtoPacto.ProdutoPactoAvaliacaoDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.produtoPacto.ProdutoPactoAvaliacaoService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 10/06/2020
 */
@Service
public class ProdutoPactoAvaliacaoServiceImpl implements ProdutoPactoAvaliacaoService {

    @Autowired
    private ProdutoPactoAvaliacaoDao produtoPactoAvaliacaoDao;

    public ProdutoPactoAvaliacao inserir(ProdutoPactoAvaliacao object) throws ServiceException {
        try {
            return getProdutoPactoAvaliacaoDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProdutoPactoAvaliacao alterar(ProdutoPactoAvaliacao object) throws ServiceException {
        try {
            return getProdutoPactoAvaliacaoDao().update(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(ProdutoPactoAvaliacao object) throws ServiceException {
        try {
            getProdutoPactoAvaliacaoDao().delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProdutoPactoAvaliacao> obterTodos() throws ServiceException {
        try {
            return getProdutoPactoAvaliacaoDao().findAll();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProdutoPactoAvaliacao obterPorId(Integer id) throws ServiceException {
        try {
            return getProdutoPactoAvaliacaoDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ProdutoPactoAvaliacao> consultar(ProdutoPacto produtoPacto, Integer limit) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();
            sql.append("SELECT obj FROM ProdutoPactoAvaliacao obj WHERE 1 = 1 ");

            if (produtoPacto != null) {
                sql.append(" AND obj.produtoPacto = :produto ");
                params.put("produto", produtoPacto);
            }

            sql.append(" ORDER BY obj.dataRegistro desc ");

            if (!UteisValidacao.emptyNumber(limit)) {
                return getProdutoPactoAvaliacaoDao().findByParam(sql.toString(), params, limit, 0);
            }
            return getProdutoPactoAvaliacaoDao().findByParam(sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProdutoPactoAvaliacaoDao getProdutoPactoAvaliacaoDao() {
        return produtoPactoAvaliacaoDao;
    }

    public void setProdutoPactoAvaliacaoDao(ProdutoPactoAvaliacaoDao produtoPactoAvaliacaoDao) {
        this.produtoPactoAvaliacaoDao = produtoPactoAvaliacaoDao;
    }

    public void preencherAvaliacoes(ProdutoPactoDTO dto) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("(select count(*) from produtopactoavaliacao where nota = 1 and produtopacto_id = p.id) as nota1, \n");
            sql.append("(select count(*) from produtopactoavaliacao where nota = 2 and produtopacto_id = p.id) as nota2, \n");
            sql.append("(select count(*) from produtopactoavaliacao where nota = 3 and produtopacto_id = p.id) as nota3, \n");
            sql.append("(select count(*) from produtopactoavaliacao where nota = 4 and produtopacto_id = p.id) as nota4, \n");
            sql.append("(select count(*) from produtopactoavaliacao where nota = 5 and produtopacto_id = p.id) as nota5 \n");
            sql.append("from produtopacto p \n");
            sql.append("where p.id = ").append(dto.getId());
            ResultSet rs = getProdutoPactoAvaliacaoDao().createStatement(sql.toString());
            if (rs.next()) {
                dto.setTotalNota1(rs.getInt("nota1"));
                dto.setTotalNota2(rs.getInt("nota2"));
                dto.setTotalNota3(rs.getInt("nota3"));
                dto.setTotalNota4(rs.getInt("nota4"));
                dto.setTotalNota5(rs.getInt("nota5"));
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
