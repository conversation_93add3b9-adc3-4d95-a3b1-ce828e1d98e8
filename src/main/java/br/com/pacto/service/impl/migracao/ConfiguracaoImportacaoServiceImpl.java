package br.com.pacto.service.impl.migracao;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.migracao.modelo.ConfiguracaoImportacao;
import br.com.pacto.bean.migracao.modelo.DataSourceImportacao;
import br.com.pacto.dao.intf.migracao.ConfiguracaoImportacaoDao;
import br.com.pacto.dao.intf.migracao.DataSourceImportacaoDao;
import br.com.pacto.service.intf.migracao.ConfiguracaoImportacaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Descrição: Gerencia as ações involvendo a entidade {@link ConfiguracaoImportacao}
 * Projeto: NewOAMD
 *
 * <AUTHOR> - 12/abr/2018 às 14:29
 * Pacto Soluções - Todos os direitos reservados
 */
@Service
@Transactional(readOnly = true)
public class ConfiguracaoImportacaoServiceImpl implements ConfiguracaoImportacaoService {

    @Autowired
    private ConfiguracaoImportacaoDao configuracaoImportacaoDao;

    @Autowired
    private DataSourceImportacaoDao dataSourceImportacaoDao;

    public ConfiguracaoImportacao consultar(Empresa empresa, Integer codigoEmpresa) throws Exception {
        ConfiguracaoImportacao configuracaoImportacao = configuracaoImportacaoDao.consultar(empresa, codigoEmpresa);
        if (configuracaoImportacao != null) {
            configuracaoImportacao.setListaDataSourceImportacao(dataSourceImportacaoDao.listar(configuracaoImportacao));
        }
        return configuracaoImportacao;
    }

    @Transactional
    public void salvar(ConfiguracaoImportacao configuracaoImportacao) throws Exception {
        configuracaoImportacaoDao.getCurrentSession().saveOrUpdate(configuracaoImportacao);
        dataSourceImportacaoDao.deletarPorConfiguracaoImportacao(configuracaoImportacao);
        for (DataSourceImportacao dataSourceImportacao : configuracaoImportacao.getListaDataSourceImportacao()) {
            dataSourceImportacao.setCodigo(null);
            //TODO Se o dataSource for arquivo, verificar se o mesmo possui uma estrutura válida
            dataSourceImportacaoDao.getCurrentSession().save(dataSourceImportacao);
        }
    }
}
