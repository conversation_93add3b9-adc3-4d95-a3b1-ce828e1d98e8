package br.com.pacto.service.impl.produtoPacto;

import br.com.pacto.bean.configuracoes.ConfiguracaoEnum;
import br.com.pacto.bean.empresa.*;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.bean.produtoPacto.ConfigProdutoPacto;
import br.com.pacto.bean.produtoPacto.ProdutoPacto;
import br.com.pacto.bean.produtoPacto.ProdutoPactoCompra;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.dto.CriarTrialNovoTreinoDTO;
import br.com.pacto.controller.json.dto.TrialNovoTreinoDTO;
import br.com.pacto.controller.json.empresa.EmpresaSiteJSON;
import br.com.pacto.controller.json.produtoPacto.CompraSMSAssinaturaDTO;
import br.com.pacto.controller.json.produtoPacto.CompraSMSEmpresaDTO;
import br.com.pacto.controller.json.produtoPacto.ComprarProdutoPactoDTO;
import br.com.pacto.controller.json.produtoPacto.EmpresaDTO;
import br.com.pacto.controller.json.produtoPacto.EmpresaUsoProdutoPactoDTO;
import br.com.pacto.controller.json.produtoPacto.InfoEmpresaDTO;
import br.com.pacto.controller.json.produtoPacto.InfoUsuarioDTO;
import br.com.pacto.controller.json.produtoPacto.ProdutoPactoDTO;
import br.com.pacto.controller.json.produtoPacto.ResultadoCompraDTO;
import br.com.pacto.dao.intf.produtoPacto.PremiumConfigsDao;
import br.com.pacto.dao.intf.produtoPacto.ProdutoPactoDao;
import br.com.pacto.email.UteisEmail;
import br.com.pacto.enums.Modulo;
import br.com.pacto.enums.MsgExceptionEnum;
import br.com.pacto.enums.StatusModuloRecursoEnum;
import br.com.pacto.enums.TipoProdutoPacto;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Formatador;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.empresa.DiscoveryService;
import br.com.pacto.service.impl.empresa.VendaDTO;
import br.com.pacto.service.impl.empresa.VendaProdutoDTO;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.empresa.EmpresaSiteService;
import br.com.pacto.service.intf.empresafinanceiro.EmpresaFinanceiroService;
import br.com.pacto.service.intf.oamd.OAMDService;
import br.com.pacto.service.intf.produtoPacto.ConfigProdutoPactoService;
import br.com.pacto.service.intf.produtoPacto.ProdutoPactoCompraService;
import br.com.pacto.service.intf.produtoPacto.ProdutoPactoLogService;
import br.com.pacto.service.intf.produtoPacto.ProdutoPactoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.SchemaBancoEnum;
import br.com.pacto.util.enumeradores.TipoCobrancaPactoEnum;
import br.com.pacto.util.http.MetodoHttpEnum;
import br.com.pacto.util.http.RequestHttpService;
import br.com.pacto.util.http.RespostaHttpDTO;
import br.com.pacto.util.server.CharsetEnum;
import br.com.pacto.util.server.Constants;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static br.com.pacto.enums.TipoProdutoPacto.NENHUM;
import static br.com.pacto.util.server.Constants.HEADER_AUTHORIZATION;
import static br.com.pacto.util.server.Constants.HEADER_CONTENT_TYPE;

/**
 * <AUTHOR> Felipe
 */
@Service
@Qualifier(value = "produtoPactoService")
public class ProdutoPactoServiceImpl implements ProdutoPactoService {


    @Autowired
    private ProdutoPactoDao produtoPactoDao;
    @Autowired
    private ProdutoPactoCompraService compraService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private EmpresaFinanceiroService empresaFinanService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private EmpresaSiteService serviceSite;
    @Autowired
    private ConfiguracaoSistemaService configuracaoService;
    @Autowired
    private OAMDService oamdService;
    @Autowired
    private PremiumConfigsDao premiumConfigsDao;
    @Autowired
    private ConfigProdutoPactoService configProdutoPactoService;
    @Autowired
    private DiscoveryService discoveryService;
    @Autowired
    private ProdutoPactoLogService produtoPactoLogService;


    public ProdutoPactoDao getProdutoPactoDao() {
        return produtoPactoDao;
    }

    private String urlApi() throws Exception {
        Paths paths = discoveryService.paths();
        return paths.getApiZw();
    }

    public ProdutoPacto inserir(ProdutoPacto object) throws ServiceException {
        try {
            return getProdutoPactoDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProdutoPacto alterar(ProdutoPacto object) throws ServiceException {
        try {
            return getProdutoPactoDao().update(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(ProdutoPacto object) throws ServiceException {
        try {
            getProdutoPactoDao().delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProdutoPacto> obterTodos() throws ServiceException {
        try {
            return getProdutoPactoDao().findAll();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProdutoPacto obterPorId(Integer id) throws ServiceException {
        try {
            return getProdutoPactoDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ProdutoPacto> consultar(TipoProdutoPacto tipo, Boolean ativo) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();
            sql.append("SELECT obj FROM ProdutoPacto obj WHERE 1 = 1 ");
            if (tipo != null) {
                sql.append(" AND obj.tipo = :tipo ");
                params.put("tipo", tipo);
            }
            if (ativo != null) {
                sql.append(" AND obj.ativo = :ativo ");
                params.put("ativo", ativo);
            }
            sql.append(" ORDER BY obj.nome ");
            return getProdutoPactoDao().findByParam(sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ResultadoCompraDTO comprarProdutoPacto(ComprarProdutoPactoDTO compraDTO) throws ServiceException {

        ResultadoCompraDTO resultadoCompraDTO = new ResultadoCompraDTO();
        ProdutoPactoCompra compra = new ProdutoPactoCompra(compraDTO);
        try {

            ProdutoPacto produtoPacto = obterPorId(compra.getDto().getProdutoPacto().getId());
            if (produtoPacto == null || produtoPacto.getTipo().equals(NENHUM)) {
                throw new ServiceException(MsgExceptionEnum.PRODUTO_NAO_ENCONTRADO);
            }
            compra.setProdutoPacto(produtoPacto);
            compra.setTipoProdutoPacto(produtoPacto.getTipo());

            //INSERIR PARA PODER OBTER O ID
            compraService.inserir(compra);

            //NumeroPedido = "STORE-" + numero do pedido o ID do Produto Pacto Compra
            resultadoCompraDTO.setNumeroPedido(compra.getNumeroPedido());


            if (UteisValidacao.emptyString(compra.getDto().getNomeSolicitante())) {
                throw new ServiceException(MsgExceptionEnum.SOLICITANTE_NAO_INFORMADO);
            }
            if (UteisValidacao.emptyString(compra.getDto().getEmailSolicitante())) {
                throw new ServiceException(MsgExceptionEnum.SOLICITANTE_EMAIL_NAO_INFORMADO);
            }
            if (UteisValidacao.emptyString(compra.getDto().getTelefoneSolicitante())) {
                throw new ServiceException(MsgExceptionEnum.SOLICITANTE_TELEFONE_NAO_INFORMADO);
            }
            if (UteisValidacao.emptyNumber(compra.getDto().getCodigoFinanceiroPagador())) {
                throw new ServiceException("Código Financeiro da empresa pagador não informado.");
            }

            ConfigProdutoPacto configProdutoPacto = configProdutoPactoService.obterAtivo("PACTO STORE+");
            if (configProdutoPacto == null ||
                    UteisValidacao.emptyString(configProdutoPacto.getChave()) ||
                    UteisValidacao.emptyNumber(configProdutoPacto.getEmpresa())) {
                throw new ServiceException("Não foi possível obter as configurações da Empresa Pacto.");
            }

            String tokenComunicacaoAPISMS = configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.SMS_TOKEN);


            //validar dados
            for (EmpresaDTO empresaDTO : compra.getDto().getEmpresas()) {

                if (UteisValidacao.emptyString(empresaDTO.getChave())) {
                    throw new ServiceException(MsgExceptionEnum.CHAVE_NAO_INFORMADA);
                }
                if (UteisValidacao.emptyNumber(empresaDTO.getCodigoZw())) {
                    throw new ServiceException(MsgExceptionEnum.EMPRESA_NAO_INFORMADA);
                }
                if (UteisValidacao.emptyNumber(empresaDTO.getCodigoFinanceiro())) {
                    throw new ServiceException(MsgExceptionEnum.CODIGO_FINANCEIRO_NAO_INFORMADO);
                }

                EmpresaFinanceiro empFinan = empresaFinanService.findByCodFinanceiro(empresaDTO.getCodigoFinanceiro());
                if (empFinan == null || UteisValidacao.emptyString(empFinan.getChaveZw())) {
                    throw new ServiceException(MsgExceptionEnum.EMPRESA_FINANCEIRO_NAO_ENCONTRADA);
                }

                //validar se existe cnpj na empresa financeiro
                if (UteisValidacao.emptyString(empFinan.getCnpj())) {
                    throw new ServiceException(MsgExceptionEnum.EMPRESA_FINANCEIRO_CNPJ_NAO_INFORMADO);
                }

                Empresa empresaOAMD = empresaService.obterPorId(empresaDTO.getChave());
                if (empresaOAMD == null || UteisValidacao.emptyString(empresaOAMD.getChave())) {
                    throw new ServiceException(MsgExceptionEnum.EMPRESA_NAO_ENCONTRADA);
                }


                empresaDTO.setNomeResumo(empFinan.getNomeResumo());
                empresaDTO.setNomeFantasia(empFinan.getNomeFantasia());
                empresaDTO.setNomeRazaoSocial(empFinan.getRazaoSocial());
                empresaDTO.setCnpj(empFinan.getCnpj());
                empresaDTO.setTelefone(empFinan.getTelefone());
                empresaDTO.setEmail(empFinan.getEmail());
                empresaDTO.setEndereco(empFinan.getEndereco());


                //Validações de compra de módulo antes de realizar a cobrança
                if (produtoPacto.getTipo().getModuloOAMD() != null) {
                    //Validar se a empresa já tem o módulo ativado
                    empresaOAMD.setModulosEmpresa(new ArrayList<>(Arrays.asList(empresaOAMD.getModulos().split("\\,"))));
                    if (empresaOAMD.getModulosEmpresa().contains(produtoPacto.getTipo().getModuloOAMD().getSiglaModulo())) {
                        throw new ServiceException(MsgExceptionEnum.MODULO_JA_ATIVADO);
                    }
                }

                String tokenSMSTransacionalOuMarketing = "";
                CompraSMSAssinaturaDTO assinaturaAtualSMS = null;
                //obter token SMS ou cadastrar a empresa no SMS
                if (produtoPacto.getTipo().isSMS_Transacional() || produtoPacto.getTipo().isSMS_Marketing()) {

                    tokenSMSTransacionalOuMarketing = obterTokenSMS(compra, empresaDTO, empFinan, empresaOAMD, tokenComunicacaoAPISMS);

                    if (UteisValidacao.emptyString(tokenSMSTransacionalOuMarketing)) {
                        throw new ServiceException("Não foi possível obter o token do SMS, por favor tente novamente.");
                    }

                    //verificar o cliente tem assinatura de sms vigente
                    assinaturaAtualSMS = verificarAssinaturasVigentesSMS(produtoPacto.getTipo(), tokenSMSTransacionalOuMarketing, tokenComunicacaoAPISMS, false);

                    //consultar o saldo atual do cliente para adicionar a quantidade adquirida
                    if (produtoPacto.getTipo().isSMS_Marketing()) {
                        consultarSaldoSMS(assinaturaAtualSMS, tokenSMSTransacionalOuMarketing, empresaOAMD, tokenComunicacaoAPISMS);
                    }
                }
                empresaDTO.setTokenSMSTransacionalOuMarketing(tokenSMSTransacionalOuMarketing);
                if (assinaturaAtualSMS != null) {
                    empresaDTO.setIdAssinaturaAtualSMS(assinaturaAtualSMS.getId());
                    empresaDTO.setSaldoAtualSMS(assinaturaAtualSMS.getSaldoAtual());
                }
            }
            compra.setJsonEmpresas(new JSONArray(compra.getDto().getEmpresas()).toString());


            //realizar a cobrança do ZillyonWeb
            inserirBaseProdutoPacto(resultadoCompraDTO, compra, configProdutoPacto);
            if (!resultadoCompraDTO.isSucesso()) {
                throw new ServiceException(resultadoCompraDTO.getResultado());
            }


            //verificar se são todas das mesma chave.
            //se for caso o recurso comprado seja pra cada banco, então só precisa executar a operação uma vez
            boolean todasMesmaChave = true;
            String chaveEmpresas = compra.getDto().getEmpresas().get(0).getChave();
            for (EmpresaDTO empresaDTO : compra.getDto().getEmpresas()) {
                if (!empresaDTO.getChave().equals(chaveEmpresas)) {
                    todasMesmaChave = false;
                }
            }

            boolean executarSomenteUmaVez = false;
            for (EmpresaDTO empresaDTO : compra.getDto().getEmpresas()) {
                try {

                    EmpresaFinanceiro empFinan = empresaFinanService.findByCodFinanceiro(empresaDTO.getCodigoFinanceiro());
                    if (empFinan == null || UteisValidacao.emptyString(empFinan.getChaveZw())) {
                        throw new ServiceException(MsgExceptionEnum.EMPRESA_FINANCEIRO_NAO_ENCONTRADA);
                    }

                    //validar se existe cnpj na empresa financeiro
                    if (UteisValidacao.emptyString(empFinan.getCnpj())) {
                        throw new ServiceException(MsgExceptionEnum.EMPRESA_FINANCEIRO_CNPJ_NAO_INFORMADO);
                    }

                    Empresa empresaOAMD = empresaService.obterPorId(empresaDTO.getChave());
                    if (empresaOAMD == null || UteisValidacao.emptyString(empresaOAMD.getChave())) {
                        throw new ServiceException(MsgExceptionEnum.EMPRESA_NAO_ENCONTRADA);
                    }


                    //Se chegou aqui é pq houve a cobrança
                    //Se der erro registrar no log para análise...
                    if (produtoPacto.getTipo().getModuloOAMD() != null) {

                        //compra de Módulo
                        compraDeModulo(compra, empresaDTO, empresaOAMD, produtoPacto);

                        //atualizar módulos na sessão
                        atualizarModulosSessao(compra, empresaDTO, empresaOAMD);

                        //verificar se são todas das mesma chave.
                        //se for caso o recurso comprado seja pra cada banco, então só precisa executar a operação uma vez
                        executarSomenteUmaVez = true;

                    } else if (produtoPacto.getTipo().isEmail()) {

                        //compra de e-mail
                        compraDeEmail(compra, empFinan, empresaOAMD);

                        //verificar se são todas das mesma chave.
                        //se for caso o recurso comprado seja pra cada banco, então só precisa executar a operação uma vez
                        executarSomenteUmaVez = true;

                    } else if (produtoPacto.getTipo().isSMS_Transacional()) {

                        //compra de SMS do Waller - Transacional
                        compraDeSMSTransacional(empresaDTO, compra, tokenComunicacaoAPISMS);

                    } else if (produtoPacto.getTipo().isSMS_Marketing()) {

                        //compra de SMS do Waller - Marketing
                        compraDeSMSMarketing(empresaDTO, compra, tokenComunicacaoAPISMS);

                    } else if (produtoPacto.getTipo().isCreditoPacto()) {

                        //compra de Crédito de DCC
                        compraDeCredito(empresaDTO, empresaOAMD, compra);

                    } else if (produtoPacto.getTipo().isModuloCobrancaCartao()) {

                        //configurar pos pago efetivado
                        configurarPosPagoEfetivado(empresaDTO, empresaOAMD, compra);
                    }

                    enviarEmailFinanceiroPacto(empresaDTO, compra);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    produtoPactoLogService.logAlta(compra, empresaDTO, "PRODUTO_PACTO_COD_FINANCEIRO_" + empresaDTO.getCodigoFinanceiro(), ex.getMessage());
                }

                if (executarSomenteUmaVez && todasMesmaChave) {
                    Uteis.logarDebug("Todas Empresas são da mesma chave " + chaveEmpresas);
                    break;
                }
            }

            enviarEmailMoviDesk(compra);
            enviarEmailCliente(compra);
            compra.setSucesso(true);
            compra.setResultado("ok");
        } catch (ServiceException ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ComprarProduto: " + ex.getMessage());
            compra.setSucesso(false);
            compra.setResultado(ex.getMessage());
            resultadoCompraDTO.setSucesso(false);
            resultadoCompraDTO.setResultado(ex.getMessage());
        } finally {
            compra.setResultadoCompra(new JSONObject(resultadoCompraDTO).toString());
            if (UteisValidacao.emptyNumber(compra.getId())) {
                compraService.inserir(compra);
            } else {
                compraService.alterar(compra);
            }
        }
        return resultadoCompraDTO;
    }

    private void configurarPosPagoEfetivado(EmpresaDTO empresaDTO, Empresa empresa, ProdutoPactoCompra compra) {
        try {
            String retorno = oamdService.alterarInformacoesEmpresaCobrancaPacto(empresa, empresaDTO.getCodigoZw(), TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO.getCodigo(),
                    true, 30, compra.getDto().getProdutoPacto().getValorCreditoPacto(), true, 1,
                    0, (compra.getProdutoPacto().getTipo().getDescricao() + " - PACTO STORE+"), 0, false,
                    null);

            if (retorno.startsWith("ERRO")) {
                retorno = retorno.replaceFirst("ERRO:", "");
                throw new Exception(retorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            produtoPactoLogService.logAlta(compra, empresaDTO, "CONFIGURAR_POS_PAGO_EFETIVADO_" + empresaDTO.getCodigoFinanceiro(), ex.getMessage());
        }
    }

    private void atualizarModulosSessao(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, Empresa empresaOAMD) {
        try {
            JSONObject body = new JSONObject();
            body.put("operacao", "atualizarModulos");

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            Map<String, String> params = new HashMap<>();
            params.put("chave", empresaOAMD.getChave());

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresaOAMD.getRoboControle() + "/prest/pactostore", headers, params, body.toString(), MetodoHttpEnum.POST);
            JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
            String retorno = jsonRetorno.optString("dados");
            if (!jsonRetorno.optBoolean("sucesso")) {
                throw new ServiceException(retorno);
            }
            if (!retorno.equalsIgnoreCase("ok")) {
                throw new ServiceException(retorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            produtoPactoLogService.log(compra, empresaDTO, "ATUALIZAR_MODULOS_SESSAO", ex.getMessage());
        }
    }

    private void inserirBaseProdutoPacto(ResultadoCompraDTO resultadoCompraDTO, ProdutoPactoCompra compra,
                                         ConfigProdutoPacto configProdutoPacto) {
        try {
            String retornoVendasAPI = realizarVendaVendasOnline(compra, configProdutoPacto);
            resultadoCompraDTO.setSucesso(true);
            resultadoCompraDTO.setResultado("Sucesso");

            try {
                JSONArray lista = new JSONArray(retornoVendasAPI);
                for (int e = 0; e < lista.length(); e++) {
                    JSONObject obj = lista.getJSONObject(e);
                    try {
                        String matricula = obj.optString("matricula");
                        String msgRetorno = obj.optString("msgRetorno");
                        Integer codigoFinanceiro = obj.optInt("codigoFinanceiro");

                        //setar a matricula
                        if (!UteisValidacao.emptyString(matricula) && !UteisValidacao.emptyNumber(codigoFinanceiro)) {
                            getProdutoPactoDao().executeNativeSQL("update empresafinanceiro set matriculaprodutopacto = '" + matricula + "' where codigofinanceiro = " + codigoFinanceiro);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            resultadoCompraDTO.setSucesso(false);
            resultadoCompraDTO.setResultado(ex.getMessage());
        }
    }

    private String realizarVendaVendasOnline(ProdutoPactoCompra compra,
                                             ConfigProdutoPacto configProdutoPacto) throws ServiceException {

        String msgRetornoVendas = "";
        try {
            List<VendaDTO> listaVenda = new ArrayList<>();

            for (EmpresaDTO empresaDTO : compra.getDto().getEmpresas()) {
                VendaDTO vendaDTO = criarVendaDTO(configProdutoPacto.getEmpresa(), empresaDTO, compra);
                listaVenda.add(vendaDTO);
            }

            Empresa empresaProdutoPacto = empresaService.obterPorId(configProdutoPacto.getChave());

            JSONObject body = new JSONObject();
            body.put("operacao", "vendaPactoStore");
            body.put("listaVenda", new JSONArray(listaVenda).toString());
            //empresa que será responsável pelo pagamento
            body.put("codigoFinanceiroPagador", compra.getDto().getCodigoFinanceiroPagador());

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            Map<String, String> params = new HashMap<>();
            params.put("chave", configProdutoPacto.getChave());

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresaProdutoPacto.getRoboControle() + "/prest/pactostore", headers, params, body.toString(), MetodoHttpEnum.POST);
            JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());

            boolean sucesso = jsonRetorno.optBoolean("sucesso");
            String dados = jsonRetorno.optString("dados");

            if (!sucesso) {
                String retorno = dados.replace("ERRO:", "").replace("ERRO", "").replace("trigger:", "").replace("Acesse a tela do Cliente para confirmação.", "");
                if (retorno.contains("Aluno já possui contrato de matrícula lançado a poucos minutos")) {
                    throw new Exception("Identificamos que houve uma compra para esse produto lançado a poucos minutos. Por favor, tente novamente mais tarde.");
                }
                throw new Exception(dados);
            }
            msgRetornoVendas = dados;
        } catch (Exception ex) {
            ex.printStackTrace();
            msgRetornoVendas = ex.getMessage();
            throw new ServiceException(ex.getMessage());
        } finally {
            compra.setRetornoVendasOnline(msgRetornoVendas);
        }
        return msgRetornoVendas;
    }

    private VendaDTO criarVendaDTO(Integer unidadeBaseCobrancaProdutoPacto, EmpresaDTO empresaDTO, ProdutoPactoCompra compra) {
        VendaDTO vendaDTO = new VendaDTO();
        vendaDTO.setUnidade(unidadeBaseCobrancaProdutoPacto);

        //dados não utilizados
        vendaDTO.setCpf("");
        vendaDTO.setDataNascimento(null);

        //nr parcelas
        vendaDTO.setNrVezesDividir(compra.getDto().getNrVezesDividir());

        //nome da empresa
        String nomeEmpresa = empresaDTO.getNomeResumo();
        if (UteisValidacao.emptyString(nomeEmpresa)) {
            nomeEmpresa = empresaDTO.getNomeFantasia();
        }
        if (UteisValidacao.emptyString(nomeEmpresa)) {
            nomeEmpresa = empresaDTO.getNomeRazaoSocial();
        }
        vendaDTO.setNome(nomeEmpresa);

        //plano
        //codigo do plano no zw
        vendaDTO.setPlano(compra.getProdutoPacto().getPlanoZW());

        //produto
        vendaDTO.setProdutos(new ArrayList<>());
        if (!UteisValidacao.emptyNumber(compra.getProdutoPacto().getProdutoZW())) {
            VendaProdutoDTO vendaProdutoDTO = new VendaProdutoDTO();
            //código do produto no zw
            vendaProdutoDTO.setProduto(compra.getProdutoPacto().getProdutoZW());
            if (compra.getProdutoPacto().getTipo().isModuloCobrancaCartao()) {
                vendaProdutoDTO.setValorUnitario(compra.getProdutoPacto().getValorCreditoPacto());
            }

            //se informa o valor é pq o valor do produto é unitário
            if (compra.getDto().getProdutoPacto().isInformarQtd()) {
                //qtd que veio do front Pacto Store
                vendaProdutoDTO.setQtd(compra.getDto().getProdutoPacto().getQtd());
            } else {
                vendaProdutoDTO.setQtd(1);
            }
            vendaDTO.getProdutos().add(vendaProdutoDTO);
        }

        //cnpj da empresa
        vendaDTO.setCnpj(empresaDTO.getCnpj());
        //email da empresa
        vendaDTO.setEmail(empresaDTO.getEmail());
        //endereco
        vendaDTO.setEndereco(empresaDTO.getEndereco());
        //telefone
        vendaDTO.setTelefone(empresaDTO.getTelefone());

        //codigoFinanceiro
        vendaDTO.setCodigoFinanceiro(empresaDTO.getCodigoFinanceiro());
        //chave
        vendaDTO.setChave(empresaDTO.getChave());
        //codigo empresa do zw
        vendaDTO.setCodigoEmpresaZW(empresaDTO.getCodigoZw());

        //observação
        vendaDTO.setObservacaoCliente("Codigo Financeiro " + empresaDTO.getCodigoFinanceiro() + ". | Código Empresa ZW: " + empresaDTO.getCodigoZw() + ". | Chave: " + empresaDTO.getChave());


        //nome titular do cartão
        vendaDTO.setNomeCartao(compra.getDto().getCartao().getTitular());
        //numero cartão
        vendaDTO.setNumeroCartao(compra.getDto().getCartao().getNumero().replace(" ", ""));
        if (compra.getDto().getCartao().getValidade().length() == 5) {
            vendaDTO.setValidade(compra.getDto().getCartao().getValidade().replace("/", "/20"));
        } else {
            vendaDTO.setValidade(compra.getDto().getCartao().getValidade());
        }
        //cvv
        vendaDTO.setCvv(compra.getDto().getCartao().getCvv());
        //cpf cnpj titular
        vendaDTO.setCpftitularcard(compra.getDto().getCartao().getCpf());
        return vendaDTO;
    }

    private void compraDeCredito(EmpresaDTO empresaDTO, Empresa empresa, ProdutoPactoCompra compra) throws ServiceException {
        try {
            Integer qtdCredito = compra.getDto().getProdutoPacto().getQtd();
            Double valorTotal = Uteis.arredondarForcando2CasasDecimais(compra.getDto().getProdutoPacto().getAdesao());
            if (!compra.getProdutoPacto().isQtdFixa()) {
                //se for qtd fixa o valor é o valor total e não unitário
                valorTotal = Uteis.arredondarForcando2CasasDecimais(compra.getDto().getProdutoPacto().getAdesao() * qtdCredito);
            }

            Integer qtdParcelas = compra.getDto().getNrVezesDividir();
            String nomeUsuario = "RESP: " + compra.getDto().getNomeSolicitante();
            String justificativa = "COMPRA REALIZADA PELO VIA " + compra.getDto().getSistemaOrigem().toUpperCase();

            //parametro "gerarCobrancaFinanceiro" como false já que a cobrança já foi realizada.
            String retorno = oamdService.gravarPrePagoDCCLancarAgendamentoFinan(empresa, empresaDTO.getCodigoZw(), qtdCredito, qtdParcelas, valorTotal, true,
                    nomeUsuario, justificativa, false);

            if (!retorno.toUpperCase().startsWith("SUCESSO")) {
                throw new Exception(retorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(MsgExceptionEnum.ERRO_COMPRAR_CREDITO, ex.getMessage());
        }
    }

    private void compraDeZillyonWeb(ProdutoPactoCompra compra) throws ServiceException {
        try {
            EmpresaSiteJSON site = new EmpresaSiteJSON();
//            site.setNomeResponsavel(compra.getDto().getResponsavel());
//            site.setNomeFantasia(compra.getDto().getNomeFantasia());
//            site.setRazaoSocial(compra.getDto().getRazaoSocial());
//            site.setCnpj(compra.getDto().getCnpj());
//            site.setEmail(compra.getDto().getEmail());
//            site.setTelefone(compra.getDto().getCelular());
//            site.setgRecaptchaResponse(compra.getDto().getCaptcha());
            site.setModulos(compra.getProdutoPacto().getTipo().getModuloOAMD().getSiglaModulo());
//          site.setSenha(dto.getCliente().getSenha());
//          site.setCaptcha(dto.getCaptcha());
            EmpresaSite empresaSite = serviceSite.cadastrarEmpresaNovaZW(site);
            //TODO confirmar os dados retornados CHAVE e código da Empresa
//            EmpresaDTO empresaDTO = new EmpresaDTO();
//            empresaDTO.setChave(empresaSite.getEmpresa().getChave());
//            empresaDTO.setCodigoZw(1);
//            compra.setEmpresaDTO(empresaDTO);
        } catch (Exception ex) {
            throw new ServiceException(MsgExceptionEnum.ERRO_COMPRAR_ZILLYONWEB, ex.getMessage());
        }
    }

    private void compraDeAppPersonal(ProdutoPactoCompra compra) throws ServiceException {
        try {
            CriarTrialNovoTreinoDTO criarTrialDTO = new CriarTrialNovoTreinoDTO();
//            criarTrialDTO.setNome(compra.getDto().getResponsavel());
//            criarTrialDTO.setCelular(compra.getDto().getCelular());
//            criarTrialDTO.setEmail(compra.getDto().getEmail());
//            criarTrialDTO.setSenha(compra.getDto().getSenha());
//            criarTrialDTO.setCaptcha(compra.getDto().getCaptcha());

            TrialNovoTreinoDTO trialDTO = serviceSite.cadastrarTrialNovoTreino(criarTrialDTO);
//            EmpresaDTO empresaDTO = new EmpresaDTO();
//            empresaDTO.setChave(trialDTO.getChave());
//            empresaDTO.setCodigoZw(trialDTO.getEmpresaId());
//            compra.setEmpresaDTO(empresaDTO);

        } catch (Exception ex) {
            throw new ServiceException(MsgExceptionEnum.ERRO_COMPRAR_APP_PERSONAL, ex.getMessage());
        }
    }

    private void compraDeModulo(ProdutoPactoCompra compra, EmpresaDTO empresaDTO,
                                Empresa empresaOAMD, ProdutoPacto produtoPacto) throws ServiceException {
        try {

            empresaOAMD.setModulosEmpresa(new ArrayList<>(Arrays.asList(empresaOAMD.getModulos().split("\\,"))));

            if (empresaOAMD.getModulosEmpresa().contains(produtoPacto.getTipo().getModuloOAMD().getSiglaModulo())) {
                throw new ServiceException(MsgExceptionEnum.MODULO_JA_ATIVADO);
            }

            //Gerar banco do TreinoWeb caso o produto precise e a empresa não tenha banco do TreinoWeb.
            if (produtoPacto.getTipo().isPrecisaBancoTreinoWeb() &&
                    !empresaOAMD.getModulos().contains(Modulo.TREINO.getSiglaModulo()) &&
                    !empresaOAMD.getModulos().contains(Modulo.NOVO_TREINO.getSiglaModulo())) {
                try {
                    empresaOAMD.setHostBDSubir(empresaOAMD.getInfoInfra().getHostSGBD_TR());
                    empresaOAMD.setPortaBDSubir(empresaOAMD.getInfoInfra().getPortaPG());
                    empresaService.gerarEmpresa(empresaOAMD.getInfoInfra(), FlagSoftwareEnum.TR, empresaOAMD, SchemaBancoEnum.PADRAO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    if (ex.getMessage().toLowerCase().contains("already exists") ||
                            ex.getMessage().toLowerCase().contains("existe")) {
                        Uteis.logarDebug("Banco Musc já existe... | " + ex.getMessage());
                    } else {
                        throw new ServiceException("Erro ao criar banco treino: " + ex.getMessage());
                    }
                }
            }

            empresaOAMD.getModulosEmpresa().add(produtoPacto.getTipo().getModuloOAMD().getSiglaModulo());

            //se for treino já habilita os módulos Crossfit e Avaliação Física
            if (produtoPacto.getTipo().equals(TipoProdutoPacto.TREINO_NOVO)) {
                empresaOAMD.getModulosEmpresa().add(Modulo.NOVO_CROSSFIT.getSiglaModulo());
                empresaOAMD.getModulosEmpresa().add(Modulo.NOVO_AVALIACAO_FISICA.getSiglaModulo());
            }

            StringBuilder lstModulos = new StringBuilder();
            for (String moduloEmpresa : empresaOAMD.getModulosEmpresa()) {
                lstModulos.append(moduloEmpresa.concat(","));
            }
            empresaOAMD.setModulos(lstModulos.substring(0, lstModulos.length() - 1));

            empresaService.alterar(empresaOAMD);

            Usuario usuarioAPI = usuarioService.validarUsuario(Constants.LOGIN_API_OAMD, Constants.SENHA_API_OAMD, false);
            try {
                //ATUALIZAR O OAMD DA INFRA
                empresaService.atualizarModulosOAMDInfra(empresaOAMD, usuarioAPI);
            } catch (Exception ex) {
                ex.printStackTrace();
                produtoPactoLogService.logAlta(compra, empresaDTO, "ATUALIZAR_MODULOS_OAMD_INFRA", ex.getMessage());
            }

            try {
                //sincronizar o usuário com o TreinoWeb
                if (produtoPacto.getTipo().isPrecisaBancoTreinoWeb()) {
                    empresaService.sincronizarUsuarioTreinoZW(empresaOAMD, usuarioAPI, "", compra.getDto().getUsuario().getCodUsuarioZw());
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                produtoPactoLogService.logBaixa(compra, empresaDTO, "SINCRONIZAR_USUARIO_TREINO", ex.getMessage());
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    private String obterTokenSMS(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, EmpresaFinanceiro empFinan,
                                 Empresa empresaOAMD, String tokenComunicacaoAPISMS) throws ServiceException {
        try {

            InfoEmpresaDTO infoEmpresaDTO = obterInformacoesEmpresa(empresaOAMD, empFinan.getEmpresazw(), empFinan.getCodigoFinanceiro());

            if (compra.getProdutoPacto().getTipo().isSMS_Transacional() && !UteisValidacao.emptyString(infoEmpresaDTO.getTokenSMSTransacional())) {
                return infoEmpresaDTO.getTokenSMSTransacional();
            }

            if (compra.getProdutoPacto().getTipo().isSMS_Marketing() && !UteisValidacao.emptyString(infoEmpresaDTO.getTokenSMSMarketing())) {
                return infoEmpresaDTO.getTokenSMSMarketing();
            }

            //não encontrou o token.. então tentar cadastrar no serviço de SMS
            String tokenSMSTransacional = "";
            String tokenSMSMarketing = "";
            InfoUsuarioDTO infoUsuarioDTO = obterInformacoesUsuarioZW(empresaOAMD, compra.getDto().getUsuario().getCodUsuarioZw());

            String usuarioNome = infoUsuarioDTO.getNome();
            String usuarioCPF = infoUsuarioDTO.getCpf();
            String usuarioTelefoneCelular = infoUsuarioDTO.getCelular();
            String usuarioEmail = "";
            if (!UteisValidacao.emptyList(infoUsuarioDTO.getEmail())) {
                usuarioEmail = infoUsuarioDTO.getEmail().get(0);
            }

            String nomeFantasia = empFinan.getNomeFantasia();
            String razaoSocial = empFinan.getRazaoSocial();
            String cnpj = empFinan.getCnpj();
            String endereco = empFinan.getEndereco();
            String nomeRespFinanceiro = compra.getDto().getNomeSolicitante();
            String emailRespFinanceiro = compra.getDto().getEmailSolicitante();
            String cpf = compra.getDto().getUsuario().getCpf();
            String telComercial = "";
            String telCelular = compra.getDto().getTelefoneSolicitante();


            //verificar se as informações estão vazias e utilizar os da consulta do zillyonweb
            if (UteisValidacao.emptyString(nomeFantasia)) {
                nomeFantasia = infoEmpresaDTO.getNome();
            }
            if (UteisValidacao.emptyString(razaoSocial)) {
                razaoSocial = infoEmpresaDTO.getRazaoSocial();
            }
            if (UteisValidacao.emptyString(cnpj)) {
                cnpj = infoEmpresaDTO.getCnpj();
            }
            if (UteisValidacao.emptyString(endereco)) {
                endereco = infoEmpresaDTO.getEndereco();
            }
            if (UteisValidacao.emptyString(telComercial)) {
                telComercial = infoEmpresaDTO.getTelcomercial1();
            }
            if (UteisValidacao.emptyString(telComercial)) {
                telComercial = infoEmpresaDTO.getTelcomercial2();
            }
            if (UteisValidacao.emptyString(telComercial)) {
                telComercial = infoEmpresaDTO.getTelcomercial3();
            }
            if (UteisValidacao.emptyString(nomeRespFinanceiro)) {
                nomeRespFinanceiro = empFinan.getResponsavelFinanceiro();
            }
            if (UteisValidacao.emptyString(nomeRespFinanceiro)) {
                nomeRespFinanceiro = usuarioNome;
            }
            if (UteisValidacao.emptyString(emailRespFinanceiro)) {
                emailRespFinanceiro = empFinan.getEmail();
            }
            if (UteisValidacao.emptyString(emailRespFinanceiro)) {
                emailRespFinanceiro = usuarioEmail;
            }
            if (UteisValidacao.emptyString(cpf)) {
                cpf = usuarioCPF;
            }
            if (UteisValidacao.emptyString(telCelular)) {
                telCelular = Uteis.obterSomenteNumeros(usuarioTelefoneCelular);
            }

            CompraSMSEmpresaDTO compraSMSEmpresaDTO = new CompraSMSEmpresaDTO();
            compraSMSEmpresaDTO.setChave(empFinan.getChaveZw());
            compraSMSEmpresaDTO.setNomeFantasia(nomeFantasia);
            compraSMSEmpresaDTO.setRazaoSocial(razaoSocial);
            compraSMSEmpresaDTO.setCnpj(Uteis.obterSomenteNumeros(cnpj));
            compraSMSEmpresaDTO.setEndereco(endereco);
            compraSMSEmpresaDTO.setComplemento(infoEmpresaDTO.getComplemento());
            compraSMSEmpresaDTO.setCep(Uteis.obterSomenteNumeros(infoEmpresaDTO.getCep()));
            compraSMSEmpresaDTO.setNomeRespFinanceiro(nomeRespFinanceiro);
            compraSMSEmpresaDTO.setCpfRespFinanceiro(Uteis.obterSomenteNumeros(cpf));
            compraSMSEmpresaDTO.setTelComercial(Uteis.obterSomenteNumeros(telComercial));
            compraSMSEmpresaDTO.setTelCelularResp(Uteis.obterSomenteNumeros(telCelular));
            compraSMSEmpresaDTO.setEmailRespFinanceiro(emailRespFinanceiro);


//            System.out.println(Uteis.retirarAcentuacaoRegex(teste).replaceAll( "[^A-Za-z0-9]",""));
//            System.out.println(Uteis.retirarAcentuacaoRegex(teste).replaceAll( "[^A-Za-z]",""));

            String nomeFantasiaMaximo11 = (Uteis.retirarAcentuacaoRegex(nomeFantasia).replaceAll("[^A-Za-z0-9]", ""));
            if (nomeFantasiaMaximo11.length() > 11) {
                nomeFantasiaMaximo11 = nomeFantasiaMaximo11.substring(0, 11);
            }
            compraSMSEmpresaDTO.setRemetente(nomeFantasiaMaximo11);

            try {

                String body = new JSONObject(compraSMSEmpresaDTO).toString();
                String urlCadastrarEmpresa = configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.URL_SMS_EMPRESA);

                Uteis.logarDebug("CompraDeSMS1 URL: " + urlCadastrarEmpresa);
                Uteis.logarDebug("CompraDeSMS1 Body: " + body);

                Map<String, String> headers = new HashMap<>();
                headers.put(HEADER_CONTENT_TYPE, Constants.APPLICATION_JSON);
                headers.put(HEADER_AUTHORIZATION, tokenComunicacaoAPISMS);

                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlCadastrarEmpresa, headers, null, body, MetodoHttpEnum.POST);

                Uteis.logarDebug("CompraDeSMS1 Response: " + respostaHttpDTO.getResponse());

                JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
                if (json.has("empresa")) {
                    JSONObject empresaJSON = json.getJSONObject("empresa");
                    tokenSMSTransacional = empresaJSON.optString("token");
                    tokenSMSMarketing = empresaJSON.optString("tokenShortCode");
                } else if (json.has("erro")) {
                    throw new Exception(json.optString("erro"));
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new ServiceException(MsgExceptionEnum.ERRO_GENERICO, ex.getMessage());
            }

            if (compra.getProdutoPacto().getTipo().isSMS_Transacional() && UteisValidacao.emptyString(tokenSMSTransacional)) {
                throw new ServiceException(MsgExceptionEnum.SMS_ERRO_CADASTRO);
            }
            if (compra.getProdutoPacto().getTipo().isSMS_Marketing() && UteisValidacao.emptyString(tokenSMSMarketing)) {
                throw new ServiceException(MsgExceptionEnum.SMS_ERRO_CADASTRO);
            }

            //atualizar no ZillyonWeb o Token do SMS
            atualizarTokenSMSZillyonWeb(compra, empresaDTO, empresaOAMD, empFinan, tokenSMSTransacional, tokenSMSMarketing);
            //atualizar no TreinoWeb o Token do SMS
            atualizarTokenSMSTreinoWeb(compra, empresaDTO, empresaOAMD, empFinan, tokenSMSTransacional, tokenSMSMarketing);

            if (compra.getProdutoPacto().getTipo().isSMS_Transacional()) {
                return tokenSMSTransacional;
            }
            if (compra.getProdutoPacto().getTipo().isSMS_Marketing()) {
                return tokenSMSMarketing;
            }
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(MsgExceptionEnum.ERRO_GENERICO, ex.getMessage());
        }
    }

    private CompraSMSAssinaturaDTO verificarAssinaturasVigentesSMS(TipoProdutoPacto tipoProdutoPacto, String tokenSMSTransacionalOuMarketing,
                                                                   String tokenComunicacaoAPISMS, boolean somenteConsulta) throws ServiceException {
        try {

            String urlConsultarAssinaturas = String.format(configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.URL_SMS_CONSULTAR_ASSINATURAS), tokenSMSTransacionalOuMarketing);

            Uteis.logarDebug("verificarAssinaturasVigentesSMS URL: " + urlConsultarAssinaturas);

            Map<String, String> headers = new HashMap<>();
            headers.put(HEADER_AUTHORIZATION, tokenComunicacaoAPISMS);

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlConsultarAssinaturas, headers, null, null, MetodoHttpEnum.POST);

            Uteis.logarDebug("verificarAssinaturasVigentesSMS Response: " + respostaHttpDTO.getResponse());

            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
            if (json.has("return")) {
                JSONArray lista = new JSONArray(json.get("return").toString());
                for (int e = 0; e < lista.length(); e++) {
                    CompraSMSAssinaturaDTO compraSMSAssinaturaDTO = new CompraSMSAssinaturaDTO(lista.getJSONObject(e));
                    if (Calendario.entre(Calendario.hoje(), compraSMSAssinaturaDTO.getDataInicio_Date(), compraSMSAssinaturaDTO.getDataFim_Date())) {
                        if (tipoProdutoPacto.isSMS_Marketing() && !UteisValidacao.emptyNumber(compraSMSAssinaturaDTO.getLimiteUnico())) {
                            return compraSMSAssinaturaDTO;
                        } else if (tipoProdutoPacto.isSMS_Transacional() && !UteisValidacao.emptyNumber(compraSMSAssinaturaDTO.getLimiteDiario())) {
                            Integer difDias = Calendario.diferencaEmDias(Calendario.hoje(), compraSMSAssinaturaDTO.getDataFim_Date());
                            if (difDias > 5) {
                                if (somenteConsulta) {
                                    return compraSMSAssinaturaDTO;
                                } else {
                                    throw new Exception("Já existe uma assinatura de SMS vigente até o dia " + Calendario.getData(compraSMSAssinaturaDTO.getDataFim_Date(), "dd/MM/yyyy"));
                                }
                            } else {
                                return compraSMSAssinaturaDTO;
                            }
                        }
                    }
                }
                return null;
            } else if (json.optString("erro").toLowerCase().contains("nenhuma assinatura vigente")) {
                Uteis.logarDebug("verificarAssinaturasVigentesSMS - Nenhuma Vigente: " + json.optString("erro"));
                return null;
            }
            throw new Exception(respostaHttpDTO.getResponse());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(MsgExceptionEnum.ERRO_GENERICO, ex.getMessage());
        }
    }

    private void atualizarTokenSMSTreinoWeb(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, Empresa empresaOAMD,
                                            EmpresaFinanceiro empFinan, String tokenSMSTransacional, String tokenSMSMarketing) throws ServiceException {
        //preencher TokenSMS no TreinoWeb
        if (empresaOAMD.isUtilizaTreinoWeb()) {

            //atualizar TokenSMSTransacional no TreinoWeb
            if (compra.getProdutoPacto().getTipo().isSMS_Transacional() && !UteisValidacao.emptyString(tokenSMSTransacional)) {
                try {
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json");

                    Map<String, String> params = new HashMap<>();
                    params.put("codFinanceiro", empFinan.getCodigoFinanceiro().toString());
                    params.put("token", tokenSMSTransacional);

                    RequestHttpService service = new RequestHttpService();
                    RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresaOAMD.getUrlTreinoWeb() + "/prest/empresa" + empFinan.getChaveZw() + "/tokenSMS", headers, params, null, MetodoHttpEnum.POST);

                    JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());

                    if (!jsonRetorno.has("content")) {
                        if (jsonRetorno.has("meta")) {
                            JSONObject meta = jsonRetorno.getJSONObject("meta");
                            String message = meta.optString("message");
                            if (!UteisValidacao.emptyString(message)) {
                                throw new ServiceException(message);
                            } else {
                                throw new ServiceException(jsonRetorno.toString());
                            }
                        } else {
                            throw new ServiceException(jsonRetorno.toString());
                        }
                    }

                    JSONObject content = jsonRetorno.getJSONObject("content");
                    String tokenSMS = content.optString("tokenSMS");
                    if (!tokenSMS.equalsIgnoreCase(tokenSMSTransacional)) {
                        throw new Exception(jsonRetorno.toString());
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    produtoPactoLogService.logAlta(compra, empresaDTO, "TOKEN_SMS_TRANSACIONAL_ATUALIZAR_TW", ex.getMessage());
                    throw new ServiceException(MsgExceptionEnum.ERRO_ATUALIZAR_TOKEN_TR);
                }
            }

            //atualizar TokenSMSMarketing no TreinoWeb
            if (compra.getProdutoPacto().getTipo().isSMS_Marketing() && !UteisValidacao.emptyString(tokenSMSMarketing)) {
                try {
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json");

                    Map<String, String> params = new HashMap<>();
                    params.put("codFinanceiro", empFinan.getCodigoFinanceiro().toString());
                    params.put("token", tokenSMSMarketing);

                    RequestHttpService service = new RequestHttpService();
                    RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresaOAMD.getUrlTreinoWeb() + "/prest/empresa" + empFinan.getChaveZw() + "/tokenSMSShortcode", headers, params, null, MetodoHttpEnum.POST);

                    JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());

                    if (!jsonRetorno.has("content")) {
                        if (jsonRetorno.has("meta")) {
                            JSONObject meta = jsonRetorno.getJSONObject("meta");
                            String message = meta.optString("message");
                            if (!UteisValidacao.emptyString(message)) {
                                throw new ServiceException(message);
                            } else {
                                throw new ServiceException(jsonRetorno.toString());
                            }
                        } else {
                            throw new ServiceException(jsonRetorno.toString());
                        }
                    }

                    JSONObject content = jsonRetorno.getJSONObject("content");
                    String tokenSMSShortcode = content.optString("tokenSMSShortcode");
                    if (!tokenSMSMarketing.equalsIgnoreCase(tokenSMSShortcode)) {
                        throw new Exception(jsonRetorno.toString());
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    produtoPactoLogService.logAlta(compra, empresaDTO, "TOKEN_SMS_MARKETING_ATUALIZAR_TW", ex.getMessage());
                    throw new ServiceException(MsgExceptionEnum.ERRO_ATUALIZAR_TOKEN_TR);
                }
            }
        }
    }

    private void atualizarTokenSMSZillyonWeb(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, Empresa empresaOAMD,
                                             EmpresaFinanceiro empFinan, String tokenSMSTransacional, String tokenSMSMarketing) throws ServiceException {
        //preencher TokenSMS no ZillyonWeb
        if (empresaOAMD.getModulos().contains(Modulo.ZILLYON_WEB.getSiglaModulo())) {

            //atualizar TokenSMSTransacional no ZillyonWeb
            if (compra.getProdutoPacto().getTipo().isSMS_Transacional() && !UteisValidacao.emptyString(tokenSMSTransacional)) {
                try {
                    JSONObject body = new JSONObject();
                    body.put("operacao", "atualizarTokenSMSTransacional");
                    body.put("empresa", empFinan.getChaveZw());
                    body.put("empresaFinanceiro", empFinan.getCodigoFinanceiro());
                    body.put("tokenSMSTransacional", tokenSMSTransacional);

                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json");

                    Map<String, String> params = new HashMap<>();
                    params.put("chave", empresaOAMD.getChave());

                    RequestHttpService service = new RequestHttpService();
                    RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresaOAMD.getRoboControle() + "/prest/pactostore", headers, params, body.toString(), MetodoHttpEnum.POST);
                    JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
                    String retorno = jsonRetorno.optString("dados");
                    if (!jsonRetorno.optBoolean("sucesso")) {
                        throw new ServiceException(retorno);
                    }
                    if (!retorno.equalsIgnoreCase("ok")) {
                        throw new ServiceException(retorno);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    produtoPactoLogService.logAlta(compra, empresaDTO, "TOKEN_SMS_TRANSACIONAL_ATUALIZAR_ZW", ex.getMessage());
                    throw new ServiceException(MsgExceptionEnum.ERRO_ATUALIZAR_TOKEN_ZW);
                }
            }

            //atualizar TokenSMSMarketing no ZillyonWeb
            if (compra.getProdutoPacto().getTipo().isSMS_Marketing() && !UteisValidacao.emptyString(tokenSMSMarketing)) {
                try {
                    JSONObject body = new JSONObject();
                    body.put("operacao", "atualizarTokenSMSMarketing");
                    body.put("empresa", empFinan.getChaveZw());
                    body.put("empresaFinanceiro", empFinan.getCodigoFinanceiro());
                    body.put("tokenSMSMarketing", tokenSMSMarketing);

                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json");

                    Map<String, String> params = new HashMap<>();
                    params.put("chave", empresaOAMD.getChave());

                    RequestHttpService service = new RequestHttpService();
                    RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresaOAMD.getRoboControle() + "/prest/pactostore", headers, params, body.toString(), MetodoHttpEnum.POST);
                    JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
                    String retorno = jsonRetorno.optString("dados");
                    if (!jsonRetorno.optBoolean("sucesso")) {
                        throw new ServiceException(retorno);
                    }
                    if (!retorno.equalsIgnoreCase("ok")) {
                        throw new ServiceException(retorno);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    produtoPactoLogService.logAlta(compra, empresaDTO, "TOKEN_SMS_MARKETING_ATUALIZAR_ZW", ex.getMessage());
                    throw new ServiceException(MsgExceptionEnum.ERRO_ATUALIZAR_TOKEN_ZW);
                }
            }
        }
    }

    private void compraDeSMSTransacional(EmpresaDTO empresaDTO, ProdutoPactoCompra compra, String tokenComunicacaoAPISMS) throws ServiceException {
        try {

            //enviar os dados da compra
            CompraSMSAssinaturaDTO assinaturaDTO = new CompraSMSAssinaturaDTO();

            //Waller informou que sempre é duração de 1 ano, seja avulso ou pacote.
            assinaturaDTO.setDataInicial(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
            assinaturaDTO.setDataFinal(Uteis.getDataAplicandoFormatacao(Uteis.somarCampoData(Calendario.hoje(), Calendar.YEAR, 1), "yyyy-MM-dd"));
            assinaturaDTO.setLimiteUnico(0);
            assinaturaDTO.setLimiteDiario(compra.getDto().getProdutoPacto().getQtd());

            if (compra.getDto().getProdutoPacto().isRecorrente()) {
                assinaturaDTO.setValorAssinatura(compra.getDto().getProdutoPacto().getMensalidade() * compra.getDto().getProdutoPacto().getFidelidade());
            } else {
                assinaturaDTO.setValorAssinatura(compra.getDto().getProdutoPacto().getAdesao());
            }

            //aqui se utiliza o token para enviar a assinatura do SMS
            String url;
            if (!UteisValidacao.emptyNumber(empresaDTO.getIdAssinaturaAtualSMS())) {
                url = String.format(configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.URL_SMS_RENOVAR_ASSINATURA), empresaDTO.getTokenSMSTransacionalOuMarketing());
            } else {
                url = String.format(configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.URL_SMS_ASSINATURA), empresaDTO.getTokenSMSTransacionalOuMarketing());
            }

            Uteis.logarDebug("compraDeSMSTransacional URL: " + url);

            HashMap<String, String> params = new HashMap<>();
            params.put("dataInicial", assinaturaDTO.getDataInicial());
            params.put("dataFinal", assinaturaDTO.getDataFinal());
            params.put("limiteDiario", assinaturaDTO.getLimiteDiario().toString());
            params.put("limiteUnico", assinaturaDTO.getLimiteUnico().toString());
            params.put("valorAssinatura", assinaturaDTO.getValorAssinatura().toString());
            if (!UteisValidacao.emptyNumber(empresaDTO.getIdAssinaturaAtualSMS())) {
                Long longID = Long.parseLong(empresaDTO.getIdAssinaturaAtualSMS().toString());
                params.put("id", longID.toString());
            }

            Map<String, String> headers = new HashMap<>();
            headers.put(HEADER_CONTENT_TYPE, Constants.APPLICATION_FORM_URLENCODED);
            headers.put(HEADER_AUTHORIZATION, tokenComunicacaoAPISMS);

            Uteis.logarDebug("compraDeSMSTransacional params: " + params);
            Uteis.logarDebug("compraDeSMSTransacional tokenComunicacaoAPISMS: " + tokenComunicacaoAPISMS);

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, params, null, MetodoHttpEnum.POST);
            String resposta = respostaHttpDTO.getResponse();

            Uteis.logarDebug("compraDeSMSTransacional Response: " + resposta);

            JSONObject json = new JSONObject(resposta);
            if (json.has("assinatura")) {
                JSONObject assinaturaJSON = json.getJSONObject("assinatura");
                Uteis.logarDebug("compraDeSMSTransacional - Assinatura " + assinaturaJSON.toString());
            } else if (resposta.contains("existe uma assinatura para esta")) {
                throw new ServiceException(MsgExceptionEnum.ASSINATURA_VIGENTE);
            } else if (!UteisValidacao.emptyString(json.optString("erro"))) {
                throw new ServiceException(json.optString("erro"));
            } else {
                throw new Exception(resposta);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(MsgExceptionEnum.ERRO_GENERICO, ex.getMessage());
        }
    }

    private void compraDeSMSMarketing(EmpresaDTO empresaDTO, ProdutoPactoCompra compra, String tokenComunicacaoAPISMS) throws ServiceException {
        try {

            //enviar os dados da compra
            CompraSMSAssinaturaDTO assinaturaDTO = new CompraSMSAssinaturaDTO();

            //Waller informou que sempre é duração de 1 ano, seja avulso ou pacote.
            assinaturaDTO.setDataInicial(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
            assinaturaDTO.setDataFinal(Uteis.getDataAplicandoFormatacao(Uteis.somarCampoData(Calendario.hoje(), Calendar.YEAR, 1), "yyyy-MM-dd"));
            assinaturaDTO.setLimiteDiario(0);

            Integer qtdSMS = compra.getDto().getProdutoPacto().getQtd();

            if (compra.getProdutoPacto().isQtdFixa()) {
                //se for qtd fixa pegar do cadastro de produto a qtd
                assinaturaDTO.setLimiteUnico(qtdSMS);

                //o valor é o valor total
                assinaturaDTO.setValorAssinatura(compra.getDto().getProdutoPacto().getAdesao());
            } else {

                //pegar a qtd informada pelo cliente no Pacto Store
                //nesse caso o valor o valor unitário
                Double valorTotal = Uteis.arredondarForcando2CasasDecimais(compra.getDto().getProdutoPacto().getAdesao() * qtdSMS);
                assinaturaDTO.setLimiteUnico(qtdSMS);
                assinaturaDTO.setValorAssinatura(valorTotal);
            }

            //soma com o saldo que o cliente ainda tem disponível
            if (!UteisValidacao.emptyNumber(empresaDTO.getSaldoAtualSMS())) {
                assinaturaDTO.setLimiteUnico(assinaturaDTO.getLimiteUnico() + empresaDTO.getSaldoAtualSMS());
            }

            //aqui se utiliza o token para enviar a assinatura do SMS
            String url;
            if (!UteisValidacao.emptyNumber(empresaDTO.getIdAssinaturaAtualSMS())) {
                url = String.format(configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.URL_SMS_RENOVAR_ASSINATURA), empresaDTO.getTokenSMSTransacionalOuMarketing());
            } else {
                url = String.format(configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.URL_SMS_ASSINATURA), empresaDTO.getTokenSMSTransacionalOuMarketing());
            }

            Uteis.logarDebug("compraDeSMSMarketing URL: " + url);

            HashMap<String, String> params = new HashMap<>();
            params.put("dataInicial", assinaturaDTO.getDataInicial());
            params.put("dataFinal", assinaturaDTO.getDataFinal());
            params.put("limiteDiario", assinaturaDTO.getLimiteDiario().toString());
            params.put("limiteUnico", assinaturaDTO.getLimiteUnico().toString());
            params.put("valorAssinatura", assinaturaDTO.getValorAssinatura().toString());
            if (!UteisValidacao.emptyNumber(empresaDTO.getIdAssinaturaAtualSMS())) {
                Long longID = Long.parseLong(empresaDTO.getIdAssinaturaAtualSMS().toString());
                params.put("id", longID.toString());
            }

            Map<String, String> headers = new HashMap<>();
            headers.put(HEADER_CONTENT_TYPE, Constants.APPLICATION_FORM_URLENCODED);
            headers.put(HEADER_AUTHORIZATION, tokenComunicacaoAPISMS);

            Uteis.logarDebug("compraDeSMSMarketing params: " + params);
            Uteis.logarDebug("compraDeSMSMarketing tokenComunicacaoAPISMS: " + tokenComunicacaoAPISMS);

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, params, null, MetodoHttpEnum.POST);
            String resposta = respostaHttpDTO.getResponse();

            Uteis.logarDebug("compraDeSMSMarketing Response: " + resposta);

            JSONObject json = new JSONObject(resposta);
            if (json.has("assinatura")) {
                JSONObject assinaturaJSON = json.getJSONObject("assinatura");
                Uteis.logarDebug("compraDeSMSMarketing - Assinatura " + assinaturaJSON.toString());
            } else if (resposta.contains("existe uma assinatura para esta")) {
                throw new ServiceException(MsgExceptionEnum.ASSINATURA_VIGENTE);
            } else if (json.has("erro")) {
                throw new ServiceException(json.optString("erro"));
            } else {
                throw new Exception(resposta);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(MsgExceptionEnum.ERRO_GENERICO, ex.getMessage());
        }
    }

    private void consultarSaldoSMS(CompraSMSAssinaturaDTO assinaturaAtualSMS, String tokenSMS,
                                   Empresa empresaOAMD, String tokenComunicacaoAPISMS) throws ServiceException {
        try {

            String urlConsulta = configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.URL_SMS_CONSULTAR_SALDO);

            Uteis.logarDebug("consultarSaldoSMS URL: " + urlConsulta);

            HashMap<String, String> params = new HashMap<>();
            params.put("key", empresaOAMD.getChave());
            params.put("token", tokenSMS);

            Map<String, String> headers = new HashMap<>();
            headers.put(HEADER_AUTHORIZATION, tokenComunicacaoAPISMS);

            Uteis.logarDebug("consultarSaldoSMS params: " + params);
            Uteis.logarDebug("consultarSaldoSMS tokenComunicacaoAPISMS: " + tokenComunicacaoAPISMS);

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlConsulta, headers, params, null, MetodoHttpEnum.POST);
            String resposta = respostaHttpDTO.getResponse();

            Uteis.logarDebug("consultarSaldoSMS Response: " + resposta);

            JSONObject json = new JSONObject(resposta);
            if (json.has("return")) {
                JSONObject jsonReturn = new JSONObject(json.optString("return"));
                if (assinaturaAtualSMS == null) {
                    assinaturaAtualSMS = new CompraSMSAssinaturaDTO();
                }
                assinaturaAtualSMS.setSaldoAtual(jsonReturn.getInt("balance"));
            } else if (json.optString("erro").toLowerCase().contains("saldo indisponivel")) {
                Uteis.logarDebug("Cliente não tem saldo... " + resposta);
            } else if (json.has("erro")) {
                throw new ServiceException(json.optString("erro"));
            } else {
                throw new Exception(resposta);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(MsgExceptionEnum.ERRO_GENERICO, ex.getMessage());
        }
    }

    private void compraDeEmail(ProdutoPactoCompra compra, EmpresaFinanceiro empFinan, Empresa empresa) throws
            ServiceException {
        try {

            Map<String, String> params = new HashMap<>();
            params.put("chave", empresa.getChave());
            params.put("empresa", empFinan.getEmpresazw().toString());
            params.put("limite", compra.getDto().getProdutoPacto().getQtd().toString());

            Uteis.logarDebug("compraDeEmail params: " + params);

            Map<String, String> headers = new HashMap<>();
            headers.put(HEADER_CONTENT_TYPE, Constants.APPLICATION_JSON);

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresa.getRoboControle() + "/prest/crm/config", headers, params, null, MetodoHttpEnum.GET);

            Uteis.logarDebug("compraDeEmail response: " + respostaHttpDTO.getResponse());

            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
            String returno = json.optString("return");
            String mensagem = json.optString("mensagem");
            if (returno.equalsIgnoreCase("OK")) {
                Uteis.logarDebug("compraDeEmail | Sucesso " + respostaHttpDTO.getResponse());
            } else {
                throw new Exception(mensagem);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(MsgExceptionEnum.ERRO_GENERICO, ex.getMessage());
        }
    }

    private String getDataString(HashMap<String, String> params) throws UnsupportedEncodingException {
        StringBuilder result = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (first)
                first = false;
            else
                result.append("&");
            result.append(URLEncoder.encode(entry.getKey(), "UTF-8"));
            result.append("=");
            result.append(URLEncoder.encode(entry.getValue(), "UTF-8"));
        }
        return result.toString();
    }

    private void enviarEmailFinanceiroPacto(EmpresaDTO empresaDTO, ProdutoPactoCompra compra) {
        try {

            String html = gerarCorpoEmailFinanceiro(empresaDTO, compra);

            String assunto = "VENDA DE PRODUTO PACTO - " + compra.getProdutoPacto().getNome().toUpperCase();

            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(assunto, configuracaoService.obterConfigEmailNoReply());
            uteisEmail.setEmailReponderPara("");

            List<String> listaEmails = obterEmailsPacto();
            String[] arrayEmail = listaEmails.stream().toArray(String[]::new);

            String nomeEmpresa = empresaDTO.getNomeFantasia();
            if (UteisValidacao.emptyString(nomeEmpresa)) {
                nomeEmpresa = empresaDTO.getNomeResumo();
            }
            if (UteisValidacao.emptyString(nomeEmpresa)) {
                nomeEmpresa = empresaDTO.getNomeRazaoSocial();
            }

            JSONObject jsonEnvioEmail = new JSONObject();
            try {
                jsonEnvioEmail.put("emails", Arrays.toString(arrayEmail));
                uteisEmail.enviarEmailN(arrayEmail, html, nomeEmpresa, "");
                jsonEnvioEmail.put("sucesso", true);
                jsonEnvioEmail.put("msg", "ok");
            } catch (Exception ex) {
                jsonEnvioEmail.put("msg", ex.getMessage());
                jsonEnvioEmail.put("sucesso", false);
                ex.printStackTrace();
                Uteis.logarDebug("ERRO ao enviar email financeiro: " + ex.getMessage());
            }

            compra.setResultadoEmailFinanceiro(jsonEnvioEmail.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            compra.setResultadoEmailFinanceiro(ex.getMessage());
        }
    }

    private void enviarEmailCliente(ProdutoPactoCompra compra) {
        try {
            String email = compra.getDto().getEmailSolicitante();
            if (UteisValidacao.emptyString(email.trim())) {
                throw new Exception("E-mail não informado.");
            }

            String html = gerarCorpoEmailCliente(compra);

            String assunto = "PACTO STORE+ - " + compra.getProdutoPacto().getNome().toUpperCase();

            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(assunto, configuracaoService.obterConfigEmailNoReply());
            uteisEmail.setEmailReponderPara("");


            JSONObject jsonEnvioEmail = new JSONObject();
            try {
                jsonEnvioEmail.put("emails", email);
                uteisEmail.enviarEmail(email, html, "");
                jsonEnvioEmail.put("sucesso", true);
                jsonEnvioEmail.put("msg", "ok");
            } catch (Exception ex) {
                jsonEnvioEmail.put("msg", ex.getMessage());
                jsonEnvioEmail.put("sucesso", false);
                ex.printStackTrace();
                Uteis.logarDebug("ERRO ao enviar e-mail cliente: " + ex.getMessage());
            }

            compra.setResultadoEmailCliente(jsonEnvioEmail.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            compra.setResultadoEmailCliente(ex.getMessage());
        }
    }

    private void enviarEmailMoviDesk(ProdutoPactoCompra compra) {
        try {
            Uteis.logarDebug("enviarEmailMoviDesk | Inicio...");

            String chave = "";
            try {
                chave = compra.getDto().getEmpresas().get(0).getChave();
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            String cnpj = "";
            String empresa = "";
            try {
                EmpresaFinanceiro empresaFinanceiro = empresaFinanService.findByCodFinanceiro(compra.getDto().getEmpresas().get(0).getCodigoFinanceiro());
                if (empresaFinanceiro != null) {
                    cnpj = empresaFinanceiro.getCnpj();
                    empresa = empresaFinanceiro.getNomeFantasia();
                    if (UteisValidacao.emptyString(empresa)) {
                        empresa = empresaFinanceiro.getNomeResumo();
                    }
                    if (UteisValidacao.emptyString(empresa)) {
                        empresa = empresaFinanceiro.getRazaoSocial();
                    }

                    if (UteisValidacao.emptyString(chave)) {
                        chave = empresaFinanceiro.getChaveZw();
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            StringBuilder html = new StringBuilder();
            html.append("<html> ");
            html.append("<head> ");
            html.append("    <title>Pacto Soluções</title> ");
            html.append("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"> ");
            html.append("</head> ");
            html.append("<body bgcolor=\"#FFFFFF\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\" class=\"bodyPanel\"> ");
            html.append("<div style=\"padding: 20px\"> ");
            html.append("    <div style=\"padding-top: 20px\"> ");
            html.append("       Atenção: nova ativação de recurso pela Pacto Store que necessita de implantação! ");
            html.append("    </div> ");
            html.append("	<div style=\"padding-top: 20px\"> ");
            html.append("        Produto: ").append(compra.getProdutoPacto().getNome().toUpperCase()).append("  ");
            html.append("    </div> ");
            html.append("	<div style=\"padding-top: 20px\"> ");
            html.append("        Chave: ").append(chave).append(" ");
            html.append("    </div> ");
            html.append("	<div> ");
            html.append("        Empresa: ").append(empresa).append(" ");
            html.append("    </div> ");
            html.append("	<div> ");
            html.append("        CNPJ: ").append(cnpj).append(" ");
            html.append("    </div> ");
            html.append("	<div style=\"padding-top: 10px\"> ");
            html.append("        Nome: ").append(compra.getDto().getNomeSolicitante()).append(" ");
            html.append("    </div> ");
            html.append("	<div> ");
            html.append("        Telefone: ").append(compra.getDto().getTelefoneSolicitante()).append(" ");
            html.append("    </div> ");
            html.append("	<div> ");
            html.append("        E-mail: ").append(compra.getDto().getEmailSolicitante()).append(" ");
            html.append("    </div> ");
            html.append("	<div style=\"padding-top: 20px\"> ");
            html.append("       P.S. Esse é um email automático.");
            html.append("    </div> ");
            html.append("	<div> ");
            html.append("	   Este email é um serviço de Pacto Store - Sistema Pacto | Política de privacidade  ");
            html.append("    </div> ");
            html.append("</div> ");
            html.append("</body> ");
            html.append("</html> ");

            String assunto = "PACTO STORE - ATIVAÇÃO " + compra.getProdutoPacto().getNome().toUpperCase();

            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(assunto, configuracaoService.obterConfigEmailNoReply());
            if (!UteisValidacao.emptyString(compra.getDto().getEmailSolicitante())) {
                uteisEmail.setEmailReponderPara(compra.getDto().getEmailSolicitante());
            } else {
                uteisEmail.setEmailReponderPara(Aplicacao.getProp(Aplicacao.EMAIL_MOVIDESK_RESPONDER));
            }
            uteisEmail.enviarEmail(Aplicacao.getProp(Aplicacao.EMAIL_MOVIDESK), html.toString(), "");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("enviarEmailMoviDesk | ERRO | " + ex.getMessage());
        } finally {
            Uteis.logarDebug("enviarEmailMoviDesk | Fim...");
        }
    }

    private List<String> obterEmailsPacto() {
        List<String> lista = new ArrayList<>();
        try {
            String valor = configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.PRODUTOS_PACTO_EMAIL);
            lista.addAll(Arrays.asList(valor.split(";")));
        } catch (Exception ex) {
            lista.add("<EMAIL>");
            lista.add("<EMAIL>");
            Uteis.logarDebug("ERRO Obter emails: " + ex.getMessage());
        }
        return lista;
    }

    private String gerarCorpoEmailFinanceiro(EmpresaDTO empresaDTO, ProdutoPactoCompra compra) throws
            IOException {
        InputStream emailAquisicaoFinanceiro = this.getClass().getResourceAsStream("/br/com/pacto/util/resources/modeloEmail/emailFinanceiro.txt");
        String email = Uteis.convertStreamToString(emailAquisicaoFinanceiro, CharsetEnum.UTF8.getNomeJava());

        boolean apresentaAdesaoOriginal = !compra.getProdutoPacto().getAdesao().equals(compra.getDto().getProdutoPacto().getAdesao());
        boolean apresentaMensalidadeOriginal = !compra.getProdutoPacto().getMensalidade().equals(compra.getDto().getProdutoPacto().getMensalidade());

        String nomeFantasia = empresaDTO.getNomeFantasia();
        String razaoSocial = empresaDTO.getNomeRazaoSocial();

        email = email.replaceAll("#NOME_FANTASIA#", nomeFantasia.toUpperCase())
                .replaceAll("#RAZAO_SOCIAL#", razaoSocial.toUpperCase())
                .replaceAll("#CHAVE#", empresaDTO.getChave())
                .replaceAll("#COD_EMPRESA#", empresaDTO.getCodigoZw().toString())
                .replaceAll("#PRODUTO#", compra.getProdutoPacto().getNome().toUpperCase())
                .replaceAll("#ADESAO#", Formatador.formatarValorMonetarioSemMoeda(compra.getDto().getProdutoPacto().getAdesao()))
                .replaceAll("#APRESENTA_ADESAO_ORIGINAL#", apresentaAdesaoOriginal ? "block" : "none")
                .replaceAll("#ADESAO_ORIGINAL#", Formatador.formatarValorMonetarioSemMoeda(compra.getProdutoPacto().getAdesao()))
                .replaceAll("#QTD_PARCELAS#", compra.getDto().getNrVezesDividir().toString())
                .replaceAll("#RESPONSAVEL#", compra.getDto().getNomeSolicitante().toUpperCase())
                .replaceAll("#TELEFONE#", compra.getDto().getTelefoneSolicitante().toUpperCase())
                .replaceAll("#EMAIL#", compra.getDto().getEmailSolicitante())
                .replaceAll("#NUMERO_PEDIDO#", compra.getNumeroPedido())
                .replaceAll("#APRESENTA_RECORRENCIA#", compra.getProdutoPacto().isRecorrente() ? "block" : "none")
                .replaceAll("#RECORRENCIA#", compra.getProdutoPacto().isRecorrente() ? "SIM" : "NÃO")
                .replaceAll("#MENSALIDADE#", Formatador.formatarValorMonetarioSemMoeda(compra.getDto().getProdutoPacto().getMensalidade()))
                .replaceAll("#APRESENTA_MENSALIDADE_ORIGINAL#", apresentaMensalidadeOriginal ? "block" : "none")
                .replaceAll("#MENSALIDADE_ORIGINAL#", Formatador.formatarValorMonetarioSemMoeda(compra.getProdutoPacto().getMensalidade()))
                .replaceAll("#APRESENTA_CUPOM_DESCONTO#", compra.getProdutoPactoCupomDesconto() != null ? "block" : "none")
                .replaceAll("#CUPOM_DESCONTO#", compra.getProdutoPactoCupomDesconto() != null ? compra.getProdutoPactoCupomDesconto().getCupom() : "")
                .replaceAll("#DESCONTO_ADESAO#", Formatador.formatarValorMonetarioSemMoeda(compra.getProdutoPactoCupomDesconto() != null ? compra.getProdutoPactoCupomDesconto().getDescontoAdesao() : 0.0))
                .replaceAll("#DESCONTO_MENSALIDADE#", Formatador.formatarValorMonetarioSemMoeda(compra.getProdutoPactoCupomDesconto() != null ? compra.getProdutoPactoCupomDesconto().getDescontoMensalidade() : 0.0))
                .replaceAll("#DATA#", Uteis.getDataComHora(Calendario.hoje()));
        return email;
    }

    private String gerarCorpoEmailCliente(ProdutoPactoCompra compra) throws IOException {

        InputStream emailAquisicaoFinanceiro = this.getClass().getResourceAsStream("/br/com/pacto/util/resources/modeloEmail/emailCliente.txt");
        String email = Uteis.convertStreamToString(emailAquisicaoFinanceiro, CharsetEnum.UTF8.getNomeJava());

        if (!UteisValidacao.emptyString(compra.getProdutoPacto().getHtmlEmailCliente().trim())) {
            email = compra.getProdutoPacto().getHtmlEmailCliente();
        }

        boolean apresentarVideo = !UteisValidacao.emptyString(compra.getProdutoPacto().getUrlVideo());

        email = email.replaceAll("#NOME#", compra.getDto().getNomeSolicitante().toUpperCase())
                .replaceAll("#NUMERO_PEDIDO#", compra.getNumeroPedido())
                .replaceAll("#PRODUTO_NOME#", compra.getProdutoPacto().getNome().toUpperCase())
                .replaceAll("#PRODUTO_APRESENTAR#", apresentarVideo ? "block" : "none")
                .replaceAll("#PRODUTO_VIDEO#", compra.getProdutoPacto().getUrlVideo());
        return email;
    }

    public EmpresaUsoProdutoPactoDTO verificarModulosRecursosUtilizados(Empresa empresaOAMD, EmpresaFinanceiro empFinan,
                                                                        Integer codigoFinanceiro, boolean consultarSaldoSMS) throws ServiceException {

        if (empFinan == null) {
            empFinan = empresaFinanService.findByCodFinanceiro(codigoFinanceiro);
        }
        if (empFinan == null || UteisValidacao.emptyString(empFinan.getChaveZw())) {
            throw new ServiceException(MsgExceptionEnum.EMPRESA_FINANCEIRO_NAO_ENCONTRADA);
        }

        if (empresaOAMD == null) {
            empresaOAMD = empresaService.obterPorId(empFinan.getChaveZw());
        }
        if (empresaOAMD == null || UteisValidacao.emptyString(empresaOAMD.getChave())) {
            throw new ServiceException(MsgExceptionEnum.EMPRESA_NAO_ENCONTRADA);
        }

        empresaOAMD.setModulosEmpresa(new ArrayList<>(Arrays.asList(empresaOAMD.getModulos().split("\\,"))));

        EmpresaUsoProdutoPactoDTO empresaDTO = new EmpresaUsoProdutoPactoDTO(empresaOAMD);

        //consultar no ZW
        if (empresaOAMD.getModulosEmpresa().contains(Modulo.ZILLYON_WEB.getSiglaModulo())) {

            InfoEmpresaDTO infoEmpresaDTO = obterInformacoesEmpresa(empresaOAMD, empFinan.getEmpresazw(), empFinan.getCodigoFinanceiro());

            empresaDTO.setCreditoAtual(infoEmpresaDTO.getCreditoDCCAtual());
            empresaDTO.setUtilizaCobrancaCartaoCredito(infoEmpresaDTO.isConvenioCartaoCredito());
            empresaDTO.setUtilizaCobrancaBoleto(infoEmpresaDTO.isConvenioBoleto());
            empresaDTO.setUtilizaCobrancaDebitoConta(infoEmpresaDTO.isConvenioDebitoConta());
            empresaDTO.setUtilizaFacial(infoEmpresaDTO.isReconhecimentoFacial());
            empresaDTO.setUtilizaTotem(infoEmpresaDTO.isTotem());
            empresaDTO.setUtilizaNotaFiscal(infoEmpresaDTO.isNotaFiscal());
            empresaDTO.setAppGestor(infoEmpresaDTO.isAppGestor());

            try {
                empresaDTO.setTipoCobrancaPacto(TipoCobrancaPactoEnum.getConsultarPorCodigo(infoEmpresaDTO.getTipoCobrancaPacto()));
            } catch (Exception ex) {
                ex.printStackTrace();
                empresaDTO.setTipoCobrancaPacto(null);
            }

            try {
                empresaDTO.setSmsTransacionalVigente(false);
                empresaDTO.setPacoteSMSTransacional(null);

                //consultar se já existe assinatura de sms vigente
                if (!UteisValidacao.emptyString(infoEmpresaDTO.getTokenSMSTransacional()) && consultarSaldoSMS) {
                    String tokenComunicacaoAPISMS = configuracaoService.obterValorPorConfiguracao(ConfiguracaoEnum.SMS_TOKEN);
                    CompraSMSAssinaturaDTO compraSMSAssinaturaDTO = verificarAssinaturasVigentesSMS(TipoProdutoPacto.SMS_TRANSACIONAL,
                            infoEmpresaDTO.getTokenSMSTransacional(), tokenComunicacaoAPISMS, true);
                    if (compraSMSAssinaturaDTO != null) {
                        empresaDTO.setSmsTransacionalVigente(true);
                        empresaDTO.setPacoteSMSTransacional(compraSMSAssinaturaDTO.getLimiteDiario());
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        if (empresaOAMD.getModulosEmpresa().contains(Modulo.TREINO.getSiglaModulo()) ||
                empresaOAMD.getModulosEmpresa().contains(Modulo.NOVO_TREINO.getSiglaModulo())) {
            try {
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");

                Map<String, String> params = new HashMap<>();
                params.put("codFinanceiro", codigoFinanceiro.toString());

                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresaOAMD.getUrlTreinoWeb() + "/prest/empresa/" + empresaOAMD.getChave() + "/empresa", headers, params, null, MetodoHttpEnum.GET);

                JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
                if (!jsonRetorno.has("content")) {
                    if (jsonRetorno.has("meta")) {
                        JSONObject meta = jsonRetorno.getJSONObject("meta");
                        String message = meta.optString("message");
                        if (!UteisValidacao.emptyString(message)) {
                            throw new ServiceException(message);
                        } else {
                            throw new ServiceException(jsonRetorno.toString());
                        }
                    } else {
                        throw new ServiceException(jsonRetorno.toString());
                    }
                }

                JSONObject content = jsonRetorno.getJSONObject("content");
                empresaDTO.setAppAcademia(content.getBoolean("usaMobile"));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return empresaDTO;
    }

    private InfoEmpresaDTO obterInformacoesEmpresa(Empresa empresaOAMD, Integer codigoEmpresaZW, Integer
            codigoFinanceiro) throws ServiceException {
        try {
            JSONObject body = new JSONObject();
            body.put("operacao", "consultarInfoEmpresa");
            body.put("empresa", codigoEmpresaZW);
            body.put("empresaFinanceiro", codigoFinanceiro);

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            Map<String, String> params = new HashMap<>();
            params.put("chave", empresaOAMD.getChave());

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresaOAMD.getRoboControle() + "/prest/pactostore", headers, params, body.toString(), MetodoHttpEnum.POST);
            JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
            if (!jsonRetorno.optBoolean("sucesso")) {
                throw new ServiceException(jsonRetorno.optString("dados"));
            }
            return new InfoEmpresaDTO(empresaOAMD.getChave(), new JSONObject(jsonRetorno.optString("dados")));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    private InfoUsuarioDTO obterInformacoesUsuarioZW(Empresa empresaOAMD, Integer codigoUsuarioZW) throws
            ServiceException {
        try {
            JSONObject body = new JSONObject();
            body.put("operacao", "consultarInfoUsuario");
            body.put("usuario", codigoUsuarioZW);

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            Map<String, String> params = new HashMap<>();
            params.put("chave", empresaOAMD.getChave());

            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(empresaOAMD.getRoboControle() + "/prest/pactostore", headers, params, body.toString(), MetodoHttpEnum.POST);
            JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
            if (!jsonRetorno.optBoolean("sucesso")) {
                throw new ServiceException(jsonRetorno.optString("dados"));
            }
            return new InfoUsuarioDTO(new JSONObject(jsonRetorno.optString("dados")));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    private boolean jaComprou(String chave, Integer codigoFinanceiro, ProdutoPacto produtoPacto) throws ServiceException {
        try {
            return compraService.jaComprou(chave, codigoFinanceiro, produtoPacto, null, null, true);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    private boolean jaComprouMesmoTipo(String chave, Integer codigoFinanceiro, ProdutoPacto produtoPactoDifernte, TipoProdutoPacto tipoProdutoPacto) throws ServiceException {
        try {
            return compraService.jaComprou(chave, codigoFinanceiro, null, produtoPactoDifernte, tipoProdutoPacto, true);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    public void processarStatusModuloRecurso(Integer codigoFinanceiro, ProdutoPacto produtoPacto, ProdutoPactoDTO dto,
                                             EmpresaUsoProdutoPactoDTO empresaDTO) throws ServiceException {

        TipoProdutoPacto tipo = produtoPacto.getTipo();
        boolean produtoGratuito = UteisValidacao.emptyNumber(dto.getAdesao()) && UteisValidacao.emptyNumber(dto.getMensalidade());
        boolean jaComprou = false;
        if (produtoPacto.isParaTodasEmpresasBanco()) {
            jaComprou = jaComprou(empresaDTO.getChave(), null, produtoPacto);
        } else {
            jaComprou = jaComprou(null, codigoFinanceiro, produtoPacto);
        }

        boolean permiteRepetir = produtoPacto.isPermiteRepetir();
        if (tipo.isReconhecimentoFacial() || tipo.isModulo() || tipo.isEmail() || tipo.isSMS_Transacional()) {
            permiteRepetir = false;
        }

        switch (tipo) {
            case CENTRAL_DE_EVENTOS:
                processarStatus(empresaDTO.isUtilizaCentralEventos(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case CRM:
                processarStatus(empresaDTO.isUtilizaCRM(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case FINANCEIRO:
                processarStatus(empresaDTO.isUtilizaFinanceiro(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case GAME_OF_RESULTS:
                processarStatus(empresaDTO.isUtilizaGame(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case STUDIO:
                processarStatus(empresaDTO.isUtilizaStudio(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case ZILLYON_WEB:
                processarStatus(empresaDTO.isUtilizaZW(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case TOTEM:
                processarStatus(empresaDTO.isUtilizaTotem(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case TREINO_NOVO:
                processarStatus(empresaDTO.isUtilizaTreinoNovo(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case VENDAS_ONLINE_NOVO:
                processarStatus(empresaDTO.isUtilizaVendasOnlineNovo(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case INTELIGENCIA_ARTIFICIAL:
                processarStatus(empresaDTO.isUtilizaInteligenciaArtificial(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case CREDITO_PACTO_EFETIVADO:
                if (empresaDTO.getTipoCobrancaPacto() != null && empresaDTO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO)) {
                    if (produtoGratuito) {
                        dto.setStatusModuloRecurso(StatusModuloRecursoEnum.GRATUITO.name());
                    } else {
                        dto.setStatusModuloRecurso(StatusModuloRecursoEnum.COMPRAR.name());
                    }
                } else {
                    dto.setStatusModuloRecurso(StatusModuloRecursoEnum.INDISPONIVEL.name());
                }
                break;
            case CREDITO_PACTO_TENTATIVA:
                if (empresaDTO.getTipoCobrancaPacto() != null && empresaDTO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO)) {
                    if (produtoGratuito) {
                        dto.setStatusModuloRecurso(StatusModuloRecursoEnum.GRATUITO.name());
                    } else {
                        dto.setStatusModuloRecurso(StatusModuloRecursoEnum.COMPRAR.name());
                    }
                } else {
                    dto.setStatusModuloRecurso(StatusModuloRecursoEnum.INDISPONIVEL.name());
                }
                break;
            case RECONHECIMENTO_FACIAL:
                processarStatus(empresaDTO.isUtilizaFacial(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case COBRANCA_CARTAO_CREDITO:
                processarStatus(empresaDTO.isUtilizaCobrancaCartaoCredito(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case SMS_TRANSACIONAL:
                //se tiver já comprado outro pacote de SMS bloqueia os demais....
                boolean jaComprouOutroProdutoDoMesmoTipoSMS = jaComprouMesmoTipo(null, codigoFinanceiro, produtoPacto, produtoPacto.getTipo());
                if (jaComprouOutroProdutoDoMesmoTipoSMS) {
                    dto.setStatusModuloRecurso(StatusModuloRecursoEnum.INDISPONIVEL.name());
                } else if (!jaComprou && empresaDTO.isSmsTransacionalVigente()) {
                    if (dto.getQtd().equals(empresaDTO.getPacoteSMSTransacional())) {
                        dto.setStatusModuloRecurso(StatusModuloRecursoEnum.ATIVADO.name());
                    } else {
                        dto.setStatusModuloRecurso(StatusModuloRecursoEnum.INDISPONIVEL.name());
                    }
                } else {
                    //cliente sempre pode comprar caso não tenha token será gerado no momento da cobrança
                    processarStatus(false, jaComprou, permiteRepetir, dto, produtoGratuito);
                }
                break;
            case SMS_MARKETING:
                //cliente sempre pode comprar caso não tenha token será gerado no momento da cobrança
                processarStatus(false, jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case APP_ACADEMIA:
                processarStatus(empresaDTO.isAppAcademia(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case APP_PERSONAL:
                if (produtoGratuito) {
                    dto.setStatusModuloRecurso(StatusModuloRecursoEnum.GRATUITO.name());
                } else {
                    dto.setStatusModuloRecurso(StatusModuloRecursoEnum.COMPRAR.name());
                }
                break;
            case APP_GESTOR:
                processarStatus(empresaDTO.isAppGestor(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case NOTA_FISCAL:
                processarStatus(empresaDTO.isUtilizaNotaFiscal(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case EMAIL:
                //se tiver já comprado outro pacote de email bloqueia os demais....
                boolean jaComprouOutroProdutoDoMesmoTipo = jaComprouMesmoTipo(empresaDTO.getChave(), null, produtoPacto, produtoPacto.getTipo());
                if (jaComprouOutroProdutoDoMesmoTipo) {
                    dto.setStatusModuloRecurso(StatusModuloRecursoEnum.INDISPONIVEL.name());
                } else {
                    processarStatus(false, jaComprou, permiteRepetir, dto, produtoGratuito);
                }
                break;
            case COBRANCA_DEBITO_CONTA:
                processarStatus(empresaDTO.isUtilizaCobrancaDebitoConta(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
            case COBRANCA_BOLETO:
                processarStatus(empresaDTO.isUtilizaCobrancaBoleto(), jaComprou, permiteRepetir, dto, produtoGratuito);
                break;
        }
    }

    private void processarStatus(boolean utiliza, boolean jaComprou, boolean permiteRepetir, ProdutoPactoDTO dto,
                                 boolean produtoGratuito) {
        if ((utiliza || jaComprou) && !permiteRepetir) {
            dto.setStatusModuloRecurso(StatusModuloRecursoEnum.ATIVADO.name());
        } else if (produtoGratuito) {
            dto.setStatusModuloRecurso(StatusModuloRecursoEnum.GRATUITO.name());
        } else {
            dto.setStatusModuloRecurso(StatusModuloRecursoEnum.COMPRAR.name());
        }
    }

    public PremiumConfigs configsPremium() throws Exception {
        List<PremiumConfigs> all = premiumConfigsDao.findAll();
        return all == null || all.isEmpty() ? new PremiumConfigs() : all.get(0);
    }
}
