/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.tlprojetos;

import br.com.pacto.dao.intf.tlprojetos.TLProjetosDao;
import br.com.pacto.objeto.to.TLProjetos;
import br.com.pacto.service.intf.tlprojetos.TLProjetosService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class TLProjetosServiceImpl implements TLProjetosService{

    @Autowired
    private TLProjetosDao dao;
    
    @Override
    public List<TLProjetos> obterTodos() throws Exception {
        return dao.findAll();
    }
    
}
