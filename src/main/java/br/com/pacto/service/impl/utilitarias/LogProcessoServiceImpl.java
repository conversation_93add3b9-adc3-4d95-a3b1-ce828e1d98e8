package br.com.pacto.service.impl.utilitarias;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.utilitarias.LogProcesso;
import br.com.pacto.dao.intf.utilitarias.LogProcessoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.utilitarias.LogProcessoService;
import br.com.pacto.util.ViewUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Qualifier(value = "logProcessoService")
public class LogProcessoServiceImpl implements LogProcessoService {

    @Autowired
    private LogProcessoDao logProcessoDao;
    @Autowired
    private ViewUtils viewUtils;

    @Override
    public LogProcesso inserir(LogProcesso logProcesso) throws ServiceException {
        try {
            return getLogProcessoDao().insert(logProcesso);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public LogProcesso inserir(Empresa e, int idEmpresa, Usuario usuario, String acao, String resultado, Date dataAlteracao) throws ServiceException {
        LogProcesso logProcesso = new LogProcesso();

        logProcesso.setEmpresa(e);
        logProcesso.setIdEmpresa(idEmpresa);
        logProcesso.setDataEHora(Calendario.hoje());
        logProcesso.setUsuario((usuario == null) ? "WEBSERVICE" : usuario.getUserName());
        logProcesso.setResultado(resultado);
        logProcesso.setAcao(acao);
        return inserir(logProcesso);
    }

    @Override
    public void obterPor(Empresa e) throws Exception {
        String s = "SELECT obj FROM LogProcesso obj WHERE obj.empresa.chave = :chave ORDER BY obj.dataEHora DESC";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("chave", e.getChave());

        e.setLogProcessos(getLogProcessoDao().findByParam(s, params));
    }

    public LogProcessoDao getLogProcessoDao() {
        return logProcessoDao;
    }

    public void setLogProcessoDao(LogProcessoDao logProcessoDao) {
        this.logProcessoDao = logProcessoDao;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }
}
