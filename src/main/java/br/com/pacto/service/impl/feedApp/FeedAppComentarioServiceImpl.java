/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.feedApp;

import br.com.pacto.bean.feedApp.FeedAppComentario;
import br.com.pacto.dao.Dao;
import br.com.pacto.dao.intf.feedApp.FeedAppComentarioDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.feedApp.FeedAppComentarioService;
import br.com.pacto.service.intf.usuarioapp.UsuarioAppService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR> Felipe
 */
@Service
public class FeedAppComentarioServiceImpl implements FeedAppComentarioService {

    @Autowired
    private FeedAppComentarioDao feedAppComentarioDao;

    @Override
    public FeedAppComentario obterPorId(Integer id) throws ServiceException {
        try {
            return feedAppComentarioDao.findObjectByAttribute("codigo", id);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public FeedAppComentario inserir(FeedAppComentario object) throws ServiceException {
        try {
            return feedAppComentarioDao.insert(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public FeedAppComentario alterar(FeedAppComentario object) throws ServiceException {
        try {
            return feedAppComentarioDao.update(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }


    @Override
    public void excluir(FeedAppComentario object) throws ServiceException {
        try {
            feedAppComentarioDao.delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

}
