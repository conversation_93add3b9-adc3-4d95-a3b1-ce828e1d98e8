package br.com.pacto.service.impl.redeEmpresa;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.Paths;
import br.com.pacto.bean.empresa.redeempresa.ContatoRedeEmpresa;
import br.com.pacto.bean.empresa.redeempresa.HistoricoCreditoRedeDCC;
import br.com.pacto.bean.empresa.redeempresa.RedeEmpresa;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.controller.json.empresa.FiltroPlanoRedeEmpresaJSON;
import br.com.pacto.controller.json.empresa.RedeDTO;
import br.com.pacto.controller.json.empresafinanceiro.EmpresaFinanceiroDetailDTO;
import br.com.pacto.dao.Dao;
import br.com.pacto.dao.intf.empresafinanceiro.EmpresaFinanceiroDao;
import br.com.pacto.dao.intf.redeempresa.ContatoRedeEmpresaDao;
import br.com.pacto.dao.intf.redeempresa.HistoricoCreditoRedeDCCDao;
import br.com.pacto.dao.intf.redeempresa.RedeEmpresaDao;
import br.com.pacto.dto.ConfiguracaoTreinoRedeEmpresaDTO;
import br.com.pacto.dto.PlanoRedeEmpresaDTO;
import br.com.pacto.enums.Modulo;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Criptografia;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.empresa.ClientDiscoveryDataDTO;
import br.com.pacto.service.impl.empresa.EmpresaDTO;
import br.com.pacto.service.impl.empresa.EmpresaFinanceiroDTO;
import br.com.pacto.service.impl.empresa.ServiceMapDTO;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.empresafinanceiro.EmpresaFinanceiroService;
import br.com.pacto.service.intf.infra.PathsService;
import br.com.pacto.service.intf.oamd.OAMDService;
import br.com.pacto.service.intf.redeempresa.RedeEmpresaService;
import br.com.pacto.util.*;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.hibernate.util.StringHelper;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;


/**
 * Created by Ulisses on 22/06/2017.
 */

@Service
@Qualifier(value = "redeEmpresaService")
public class RedeEmpresaServiceImpl implements RedeEmpresaService {

    @Autowired
    private RedeEmpresaDao redeEmpresaDao;

    @Autowired
    private EmpresaService es;

    @Autowired
    private EmpresaFinanceiroDao empresaFinanceiroDao;

    @Autowired
    private HistoricoCreditoRedeDCCDao histDCCDao;

    @Autowired
    private ContatoRedeEmpresaDao contatoRedeEmpresaDao;

    @Autowired
    private PathsService pathsService;

    @Autowired
    private ViewUtils viewUtils;

    @Autowired
    private HttpServico httpServico;

    public static Map<String, ClientDiscoveryDataDTO> mapaDiscovery = new HashMap<>();

    public RedeEmpresa inserir(RedeEmpresa redeEmpresa) throws Exception{
        if ((redeEmpresa.getNome() == null) || (redeEmpresa.getNome().trim().equals(""))){
            throw new Exception("Informe o nome da Rede de empresa");
        }
        redeEmpresa.setChaverede(Criptografia.encryptMD5(redeEmpresa.getNome().toUpperCase()));
        redeEmpresa = getRedeEmpresaDao().insert(redeEmpresa);
        atualizarRedeEmpresaNaTabelaEmpresaFinanceiro(redeEmpresa);
        return redeEmpresa;
    }

    private void atualizarRedeEmpresaNaTabelaEmpresaFinanceiro(RedeEmpresa redeEmpresa)throws Exception{
        if ((redeEmpresa.getListaEmpresa() != null) && (redeEmpresa.getListaEmpresa().size() > 0)){
            StringBuilder ids = new StringBuilder();
            for (EmpresaFinanceiro empresaFinanceiro: redeEmpresa.getListaEmpresa()){
                if (UtilReflection.objetoMaiorQueZero(empresaFinanceiro, "getRedeempresa_id()")){
                    if (!(empresaFinanceiro.getRedeempresa_id().equals(redeEmpresa.getId()))){
                        RedeEmpresa obj = this.redeEmpresaDao.findById(empresaFinanceiro.getRedeempresa_id());
                        throw new Exception("Operação não permitida. A empresa '" + empresaFinanceiro.getNomeResumo() + "' já está na rede " + obj.getNome());
                    }
                }
                if (ids.length() == 0){
                    ids.append(empresaFinanceiro.getCodigo());
                }else{
                    ids.append(",").append(empresaFinanceiro.getCodigo());
                }
            }
            // apagar as referencias aos registros antigos.
            getRedeEmpresaDao().executarSqlNativo("update empresaFinanceiro set redeEmpresa_id = null where redeEmpresa_id = " + redeEmpresa.getId());
            // setar as novas referencias.
            StringBuilder sql = new StringBuilder();
            sql.append("update empresaFinanceiro set redeEmpresa_id = ").append(redeEmpresa.getId());
            sql.append(" where codigo in(").append(ids.toString()).append(")");
            getRedeEmpresaDao().executarSqlNativo(sql.toString());
        }
    }

    public RedeEmpresa alterar(RedeEmpresa redeEmpresa) throws Exception{
        List<EmpresaFinanceiro> listaEmpresaFinanceiro = new ArrayList<EmpresaFinanceiro>();
        listaEmpresaFinanceiro.addAll(redeEmpresa.getListaEmpresa());
        if (redeEmpresa.getChaverede() == null) {
            redeEmpresa.setChaverede(Criptografia.encryptMD5(redeEmpresa.getNome().toUpperCase()));
        }
        redeEmpresa = getRedeEmpresaDao().update(redeEmpresa);
        redeEmpresa.setListaEmpresa(listaEmpresaFinanceiro);
        atualizarRedeEmpresaNaTabelaEmpresaFinanceiro(redeEmpresa);
        return redeEmpresa;
    }

    public void excluir(RedeEmpresa redeEmpresa) throws Exception{
        // apagar as referencias aos registros antigos.
        getRedeEmpresaDao().executarSqlNativo("update empresaFinanceiro set redeEmpresa_id = null where redeEmpresa_id = " + redeEmpresa.getId());
        getRedeEmpresaDao().delete(redeEmpresa);
    }

    public RedeEmpresa obterPorId(Integer id) throws Exception{
        RedeEmpresa redeEmpresa = getRedeEmpresaDao().findById(id);
        redeEmpresa.setListaEmpresa(getEmpresaFinanceiroDao().consultarEmpresasRede(id));
        redeEmpresa.setListaContatoApresentar(new ArrayList<ContatoRedeEmpresa>(redeEmpresa.getListaContatoRedeEmpresa()));
        return redeEmpresa;
    }

    public RedeEmpresa obterPorChaveRede(String chaveRede) throws Exception {
        RedeEmpresa redeEmpresa = getRedeEmpresaDao().findObjectByAttribute("chaverede", chaveRede);
        redeEmpresa.setListaEmpresa(getEmpresaFinanceiroDao().consultarEmpresasRede(redeEmpresa.getId()));
        return redeEmpresa;
    }

    public List<RedeEmpresa> obterTodos() throws Exception{
        return getRedeEmpresaDao().findAll();
    }

    public RedeEmpresaDao getRedeEmpresaDao() {
        return redeEmpresaDao;
    }

    public void setRedeEmpresaDao(RedeEmpresaDao redeEmpresaDao) {
        this.redeEmpresaDao = redeEmpresaDao;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public EmpresaFinanceiroDao getEmpresaFinanceiroDao() {
        return empresaFinanceiroDao;
    }

    public void setEmpresaFinanceiroDao(EmpresaFinanceiroDao empresaFinanceiroDao) {
        this.empresaFinanceiroDao = empresaFinanceiroDao;
    }

    public ContatoRedeEmpresaDao getContatoRedeEmpresaDao() {
        return contatoRedeEmpresaDao;
    }

    public void setContatoRedeEmpresaDao(ContatoRedeEmpresaDao contatoRedeEmpresaDao) {
        this.contatoRedeEmpresaDao = contatoRedeEmpresaDao;
    }

    public RedeEmpresa gravarCreditoRedeDCC(RedeEmpresa redeEmpresa,
                                            EmpresaJSON rede,
                                            Integer qtdCreditosAdd,
                                            String usuario,
                                            String justificativa,
                                            Boolean gerarCobrancaPacto,
                                            Double valortotal,
                                            Date dataPrimeiraParcela,
                                            Boolean gravarCreditos,
                                            Integer codEmpresaFinanceiroCobranca) throws Exception {

        OAMDService os = UtilContext.getBean(OAMDService.class);
        List<EmpresaFinanceiro> empresasFinanceiro = getEmpresaFinanceiroDao().consultarEmpresasRede(redeEmpresa.getId());
        for (EmpresaFinanceiro ef : empresasFinanceiro) {
            if (ef.getCodigo().equals(codEmpresaFinanceiroCobranca)) {
                redeEmpresa.setEmpresaFinanceiroCobranca(ef);
                break;
            }
        }

        redeEmpresa.setCreditos(redeEmpresa.getCreditos() + qtdCreditosAdd);
        redeEmpresa.setTipocobrancapacto(rede.getTipocobrancapacto());
        redeEmpresa.setValorcreditopacto(rede.getValorcreditopacto());
        redeEmpresa.setQtdCreditoUtilizadoPosPagoPacto(rede.getQtdCreditoUtilizadoPosPagoPacto());
        redeEmpresa.setGerarNotaFiscal(rede.isGerarnotafiscalcobrancapacto());
        redeEmpresa.setGerarCobrancaAutomaticaPacto(rede.isGerarcobrancaautomaticapacto());
        redeEmpresa.setQtdCreditoRenovar(rede.getQtdcreditorenovarprepagocobrancapacto());
        redeEmpresa.setQtdParcelas(rede.getQtdparcelascobrancapacto());
        redeEmpresa.setCobrarCreditoPactoBoleto(rede.isCobrarcreditopactoboleto());

        List<String> listaChavesEmpresaRede = redeEmpresa.getListaChavesEmpresaRede();

        redeEmpresa = redeEmpresaDao.update(redeEmpresa);

        if (!gravarCreditos) {
            for (EmpresaFinanceiro e : empresasFinanceiro) {
                Empresa empresa = es.obterObjetoPorChave(e.getChaveZw());
                if (!empresa.getAtiva()) {
                    continue;
                }

                boolean empresaResponsavelCobrancaPacto = (!UteisValidacao.emptyNumber(e.getCodigoFinanceiro()) &&
                        e.getCodigoFinanceiro().equals(redeEmpresa.getEmpresaFinanceiroCobranca().getCodigoFinanceiro()));

                String retorno = os.alterarInformacoesEmpresaCobrancaPacto(empresa,
                        e.getEmpresazw(),
                        rede.getTipocobrancapacto(),
                        gerarCobrancaPacto && e.getChaveZw().equals(redeEmpresa.getChaveCobrancaDCC()),
                        rede.getQtddiasfechamentocobrancapacto(),
                        rede.getValorcreditopacto(),
                        rede.isGerarnotafiscalcobrancapacto(),
                        rede.getQtdparcelascobrancapacto(),
                        rede.getQtdcreditorenovarprepagocobrancapacto(),
                        usuario, rede.getDiavencimentocobrancapacto(),
                        empresaResponsavelCobrancaPacto,
                        rede.isCobrarcreditopactoboleto());
            }

            return redeEmpresa;
        }

        for (EmpresaFinanceiro e : empresasFinanceiro) {
            Empresa empresa = es.obterObjetoPorChave(e.getChaveZw());
            if (!empresa.getAtiva()) {
                continue;
            }

            String retorno = os.gravarPrePagoDCCLancarAgendamentoFinan(empresa,
                    e.getEmpresazw(),
                    qtdCreditosAdd,
                    rede.getQtdparcelascobrancapacto(),
                    dividirValorTotalPorEmpresas(valortotal, listaChavesEmpresaRede.size()),
                    rede.isGerarnotafiscalcobrancapacto(),
                    usuario, justificativa,
                    validarGeracaoCobrancaAutomatica(gerarCobrancaPacto, e.getChaveZw(), listaChavesEmpresaRede));
        }

        HistoricoCreditoRedeDCC historico = new HistoricoCreditoRedeDCC();
        historico.setDataRegistro(Calendario.hoje());
        historico.setDataCobranca(dataPrimeiraParcela);
        historico.setSaldo(redeEmpresa.getCreditos());
        historico.setQuantidade(qtdCreditosAdd);
        historico.setValorTotal(valortotal);
        historico.setGerarNota(rede.isGerarnotafiscalcobrancapacto());
        historico.setGerarCobranca(gerarCobrancaPacto);
        historico.setNomeUsuario(usuario);
        historico.setRedeEmpresa(redeEmpresa);
        historico.setJustificativa(justificativa);
        histDCCDao.insert(historico);

        return redeEmpresa;
    }

    private boolean validarGeracaoCobrancaAutomatica(Boolean gerarCobrancaPacto, String chaveZw, List<String> listaChavesEmpresaRede) {
        boolean possuiChave = false;

        for (String chave : listaChavesEmpresaRede) {
            if (chaveZw.equals(chave)) {
                possuiChave = true;
                break;
            }
        }

        return gerarCobrancaPacto && possuiChave;
    }

    private Double dividirValorTotalPorEmpresas(Double valortotal, int quantidadeEmpresas) {
        if (quantidadeEmpresas <= 1) {
            return valortotal;
        } else {
            return valortotal / quantidadeEmpresas;
        }
    }

    public String debitarDCCRede(final String chave, final Integer codigoEmpresa, final Integer debito) throws Exception{
        JSONObject json = new JSONObject();
        try {
            EmpresaFinanceiroService efs = (EmpresaFinanceiroService) UtilContext.getBean(EmpresaFinanceiroService.class);
            EmpresaFinanceiro empresaFinanceiro = efs.obterPorChave(chave);
            if(empresaFinanceiro == null || empresaFinanceiro.getRedeEmpresa() == null){
                json.put("erro", "Não foi possivel obter a rede da empresa.");
            }else{
                RedeEmpresa redeDCC = empresaFinanceiro.getRedeEmpresa();
                redeDCC.setCreditos(redeDCC.getCreditos() - debito);

                HistoricoCreditoRedeDCC historico = new HistoricoCreditoRedeDCC();
                historico.setDataRegistro(Calendario.hoje());
                historico.setSaldo(redeDCC.getCreditos());
                historico.setQuantidade(debito);
                historico.setRedeEmpresa(redeDCC);
                historico.setDebito(true);
                historico.setObservacao("REMESSA");
                historico.setChaveEmpresaDebito(chave);
                historico.setCodEmpresaDebito(codigoEmpresa);
                histDCCDao.insert(historico);

                redeDCC = redeEmpresaDao.update(redeDCC);

                json.put("creditos", redeDCC.getCreditos());
            }
        } catch (Exception e) {
            try {
                json.put("erro", e.getMessage());
            }catch (Exception e1){}

        }
        return json.toString();
    }

    public String addDCCRede(final String chave) throws Exception{
        JSONObject json = new JSONObject();
        try {
            EmpresaFinanceiroService efs = (EmpresaFinanceiroService) UtilContext.getBean(EmpresaFinanceiroService.class);
            EmpresaFinanceiro empresaFinanceiro = efs.obterPorChave(chave);
            if(empresaFinanceiro == null || empresaFinanceiro.getRedeEmpresa() == null){
                json.put("erro", "Não foi possivel obter a rede da empresa.");
            }else{
                RedeEmpresa redeDCC = empresaFinanceiro.getRedeEmpresa();
                redeDCC.setCreditos(redeDCC.getCreditos() + redeDCC.getQtdCreditoRenovar());
                redeDCC.setDtultimacobrancapacto(Calendario.hoje());

                HistoricoCreditoRedeDCC historico = new HistoricoCreditoRedeDCC();
                historico.setDataRegistro(Calendario.hoje());
                historico.setSaldo(redeDCC.getCreditos());
                historico.setQuantidade(redeDCC.getQtdCreditoRenovar());
                historico.setRedeEmpresa(redeDCC);
                historico.setDebito(false);
                historico.setChaveEmpresaDebito(chave);
                historico.setObservacao("RENOVACAO AUTOMATICA");
                histDCCDao.insert(historico);

                redeDCC = redeEmpresaDao.update(redeDCC);

                json.put("creditos", redeDCC.getCreditos());
            }
        } catch (Exception e) {
            try {
                json.put("erro", e.getMessage());
            }catch (Exception e1){}

        }
        return json.toString();
    }

    public List<RedeEmpresa> obterRedesApp(String nome) throws Exception {
        List<RedeEmpresa> redeEmpresas;
        if (UteisValidacao.emptyString(nome)) {
            redeEmpresas = getRedeEmpresaDao().findAll();
        } else {
            redeEmpresas = getRedeEmpresaDao().findByNome(nome);
        }
        for (RedeEmpresa redeEmpresa : redeEmpresas) {
            redeEmpresa.setListaEmpresa(getEmpresaFinanceiroDao().consultarEmpresasRede(redeEmpresa.getId()));
        }
        return redeEmpresas;
    }

    public RedeEmpresa consultarPorChaveZWOAMD(String chaveZW) throws Exception{
        return redeEmpresaDao.consultarPorChaveZWOAMD(chaveZW);
    }

    public List<EmpresaFinanceiro> consultarUnidadesDaRedeOAMD(RedeEmpresa redeEmpresa) throws Exception{
        return redeEmpresaDao.consultarUnidadesDaRedeOAMD(redeEmpresa);
    }


    @Override
    public String limpaCacheRede() throws ServiceException {
        mapaDiscovery = new HashMap<>();
        return "Executado com Sucesso";
    }
    @Override
    public ClientDiscoveryDataDTO obterUrls(String key) throws ServiceException {
        return obterUrls(key, false);
    }

    @Override
    public ClientDiscoveryDataDTO obterUrls(final String key, boolean rede) throws ServiceException {
        boolean temChave = isNotEmpty(key);

        Paths paths = pathsService.findDefault();
        ClientDiscoveryDataDTO client = new ClientDiscoveryDataDTO();
        client.setServiceUrls(new ServiceMapDTO(paths));
        if (temChave) {

            if (mapaDiscovery.containsKey(key)){
                return mapaDiscovery.get(key);
            }

            Empresa empresa = obterEmpresa(key);
            configs(empresa, client, paths);
            if(empresa != null){

                client.getServiceUrls().setZwUrl(empresa.getRoboControle());
                client.getServiceUrls().setPlanoMsUrl(planoMsUrl(empresa));
                client.getServiceUrls().setCadastroAuxiliarUrl(cadastroAuxiliarUrl(empresa));
                client.setEmpresas(empresasChave(key, client));
                client.getServiceUrls().setTreinoApiUrl(empresa.getUrlTreino() + "/prest");
                client.getServiceUrls().setTreinoUrl(empresa.getUrlTreino());
                client.getServiceUrls().setPactoPayDashUrl(pactoPayDashUrl(empresa));
                client.getServiceUrls().setPactoPayMsUrl(pactoPayMsUrl(empresa));
                client.getServiceUrls().setZwFrontUrl(empresa.getZwfront());
                client.getServiceUrls().setTreinoFrontUrl(empresa.getTreinofront());
                client.getServiceUrls().setAdmCoreUrl(admCoreMsUrl(empresa));
                client.getServiceUrls().setPessoaMsUrl(pessoaMsUrl(empresa));
                client.getServiceUrls().setProdutoMsUrl(produtoMsUrl(empresa));

                try {
                    client.setFinanceiroEmpresas(obterEmpresaFinanceiro(key));
                    if(rede) {
                        redeempresa(client, null);
                    }
                } catch (Exception e) {
                    System.out.printf("Ignorando exceção %s ao obter Empresa Financeiro %s%n", e.getMessage(), key);
                }
            }
            mapaDiscovery.put(key, client);
        }
        return client;
    }

    @Override
    public List<PlanoRedeEmpresaDTO> obterEmpresasReplicarPlano(String key, Integer planoOrigem, FiltroPlanoRedeEmpresaJSON filters, String token) throws ServiceException {
        boolean temChave = isNotEmpty(key);
        List<PlanoRedeEmpresaDTO> unidadesReplicar = new ArrayList<>();
        try {
            if (temChave) {
                Empresa empresa = obterEmpresa(key);
                if (empresa != null) {
                    String urlPlanoMs = planoMsUrl(empresa);

                    HttpGet httpGet = new HttpGet(urlPlanoMs + "/planoRedeEmpresa/plano/" + planoOrigem);
                    httpGet.setHeader("Content-Type", "application/json;");
                    httpGet.setHeader("Authorization", token);

                    HttpClient httpClient = ExecuteRequestHttpService.createConnector();
                    String resposta = EntityUtils.toString(httpClient.execute(httpGet).getEntity());

                    List<PlanoRedeEmpresaDTO> replicacoesPlanoOrigemDTO = JSONMapper.getList(new JSONObject(resposta).getJSONArray("content"), PlanoRedeEmpresaDTO.class);
                    ClientDiscoveryDataDTO client = new ClientDiscoveryDataDTO();
                    client.setFinanceiroEmpresas(obterEmpresaFinanceiro(key));
                    redeempresa(client, null);
                    List<RedeDTO> redes = new ArrayList<>();
                    client.getRedeEmpresas().stream().forEach(rede -> {
                        if (!rede.getChave().equals(key)) {
                            redes.add(rede);
                        }
                    });
                    int qtdeEmpresasRede = redes.size();
                    AtomicInteger qtdePlanosReplicados = new AtomicInteger();
                    AtomicInteger qtdePlanosNaoReplicados = new AtomicInteger();
                    List<PlanoRedeEmpresaDTO> disponiveisReplicar = new ArrayList<>();
                    redes.forEach(redeDTO -> {
                        Optional<PlanoRedeEmpresaDTO> rep = replicacoesPlanoOrigemDTO.stream()
                                .filter(map -> redeDTO.getChave().equals(map.getChave()) && redeDTO.getEmpresaZw().equals(map.getCodigoEmpresaDestino())).findFirst();
                        if (rep.isPresent() && isNotBlank(rep.get().getMensagemSituacao()) && rep.get().getMensagemSituacao().startsWith("REPLICADO EM")) {
                            PlanoRedeEmpresaDTO repp = rep.get();
                            repp.setCodigoEmpresaDestino(redeDTO.getEmpresaZw());
                            repp.setNomeUnidade(redeDTO.getNomeFantasia());
                            qtdePlanosReplicados.getAndIncrement();
                            disponiveisReplicar.add(repp);
                        } else {
                            qtdePlanosNaoReplicados.getAndIncrement();
                            PlanoRedeEmpresaDTO repp = rep.orElseGet(PlanoRedeEmpresaDTO::new);
                            repp.setCodigoEmpresaDestino(redeDTO.getEmpresaZw());
                            repp.setNomeUnidade(redeDTO.getNomeFantasia());
                            repp.setChave(redeDTO.getChave());
                            repp.setMensagemSituacao(isBlank(repp.getMensagemSituacao()) ? "Não replicado" : repp.getMensagemSituacao());
                            disponiveisReplicar.add(repp);
                        }
                    });

                    if (!disponiveisReplicar.isEmpty()) {
                        if (StringHelper.isNotEmpty(filters.getSituacao()) || StringHelper.isNotEmpty(filters.getParametro())) {
                            disponiveisReplicar.forEach(dr -> {
                                boolean replicado = dr.getMensagemSituacao() != null && dr.getMensagemSituacao().toUpperCase().startsWith("REPLICADO EM");

                                if (!StringHelper.isNotEmpty(filters.getParametro()) || (dr.getNomeUnidade() != null && dr.getNomeUnidade().contains(filters.getParametro()))) {
                                    if (!StringHelper.isNotEmpty(filters.getSituacao()) || (filters.getSituacao().equals("REPLICADO") && replicado) ||
                                            (filters.getSituacao().equals("NAO_REPLICADO") && !replicado)) {
                                        unidadesReplicar.add(dr);
                                    }
                                }
                            });
                        } else {
                            unidadesReplicar.addAll(disponiveisReplicar);
                        }

                        if (!unidadesReplicar.isEmpty()) {
                            unidadesReplicar.get(0).setQtdeEmpresasRede(qtdeEmpresasRede);
                            unidadesReplicar.get(0).setQtdePlanosReplicados(qtdePlanosReplicados.get());
                            unidadesReplicar.get(0).setQtdePlanosNaoReplicados(qtdePlanosNaoReplicados.get());
                        }
                    }

                }
            }
        } catch (Exception e) {
            throw new ServiceException(String.format("ERRO: %s ao obter empresas replicar %s%n", e.getMessage(), key));
        }
        return unidadesReplicar;
    }

    @Override
    public List<ConfiguracaoTreinoRedeEmpresaDTO> obterEmpresasReplicarConfiguracaoTreino(String key, String filters) throws ServiceException {
        boolean temChave = isNotEmpty(key);

        List<ConfiguracaoTreinoRedeEmpresaDTO> unidadesReplicar = new ArrayList<>();
        try {
            if (temChave) {
                Empresa empresa = obterEmpresa(key);
                if (empresa != null) {
                    String urlTreino = empresa.getUrlTreino() + "/prest";

                    HttpGet httpGet = new HttpGet(urlTreino + "/replicar-empresa/configuracao/findByChaveOrigem?chaveOrigem=" + key);
                    httpGet.setHeader("Content-Type", "application/json;");

                    HttpClient httpClient = ExecuteRequestHttpService.createConnector();
                    String resposta = EntityUtils.toString(httpClient.execute(httpGet).getEntity());

                    List<ConfiguracaoTreinoRedeEmpresaDTO> replicacoesConfiguracaoTreinoOrigemDTO = JSONMapper.getList(new JSONObject(resposta).getJSONArray("content"), ConfiguracaoTreinoRedeEmpresaDTO.class);
                    ClientDiscoveryDataDTO client = new ClientDiscoveryDataDTO();
                    client.setFinanceiroEmpresas(obterEmpresaFinanceiro(key));
                    redeempresa(client, filters);
                    List<RedeDTO> redes = new ArrayList<>();
                    client.getRedeEmpresas().stream().forEach(rede -> {
                        if (!rede.getChave().equals(key)) {
                            redes.add(rede);
                        }
                    });
                    int qtdeEmpresasRede = redes.size();
                    AtomicInteger qtdeConfigsReplicadas = new AtomicInteger();
                    AtomicInteger qtdeConfigsNaoReplicadas = new AtomicInteger();
                    List<ConfiguracaoTreinoRedeEmpresaDTO> disponiveisReplicar = new ArrayList<>();
                    redes.forEach(redeDTO -> {
                        Optional<ConfiguracaoTreinoRedeEmpresaDTO> rep = replicacoesConfiguracaoTreinoOrigemDTO.stream()
                                .filter(map -> redeDTO.getChave().equals(map.getChaveDestino())).findFirst();
                        if (rep.isPresent() && isNotBlank(rep.get().getMensagemSituacao()) && rep.get().getMensagemSituacao().startsWith("REPLICADO EM")) {
                            ConfiguracaoTreinoRedeEmpresaDTO repp = rep.get();
                            repp.setNomeUnidade(redeDTO.getNomeFantasia());
                            qtdeConfigsReplicadas.getAndIncrement();
                            disponiveisReplicar.add(repp);
                        } else {
                            qtdeConfigsNaoReplicadas.getAndIncrement();
                            ConfiguracaoTreinoRedeEmpresaDTO repp = rep.orElseGet(ConfiguracaoTreinoRedeEmpresaDTO::new);
                            repp.setNomeUnidade(redeDTO.getNomeFantasia());
                            repp.setChaveDestino(redeDTO.getChave());
                            repp.setMensagemSituacao(isBlank(repp.getMensagemSituacao()) ? "Não replicado" : repp.getMensagemSituacao());
                            disponiveisReplicar.add(repp);
                        }
                    });

                    if (!disponiveisReplicar.isEmpty()) {
                        disponiveisReplicar.get(0).setQtdeEmpresasRede(qtdeEmpresasRede);
                        disponiveisReplicar.get(0).setQtdeConfigsReplicadas(qtdeConfigsReplicadas.get());
                        disponiveisReplicar.get(0).setQtdeConfigsNaoReplicadas(qtdeConfigsNaoReplicadas.get());

                        String filtro = "";
                        try {
                            if (new JSONObject(filters).getJSONArray("situacao").length() > 1) {
                                throw new Exception("Filtrar todas situações");
                            }
                            filtro = new JSONObject(filters).getJSONArray("situacao").get(0).toString();
                        } catch (Exception ignore) {
                        }
                        if (isNotBlank(filtro)) {
                            String finalFiltro = filtro;
                            disponiveisReplicar.forEach(dR -> {
                                if (isNotBlank(dR.getMensagemSituacao()) && dR.getMensagemSituacao().startsWith("REPLICADO EM") && finalFiltro.equals("REPLICADO")) {
                                    unidadesReplicar.add(dR);
                                }
                            });
                            disponiveisReplicar.forEach(dR -> {
                                if (finalFiltro.equals("NAO_REPLICADO") && (isBlank(dR.getMensagemSituacao()) || !dR.getMensagemSituacao().startsWith("REPLICADO EM"))) {
                                    unidadesReplicar.add(dR);
                                }
                            });
                        } else {
                            unidadesReplicar.addAll(disponiveisReplicar);
                        }
                    }

                }
            }
        } catch (Exception e) {
            throw new ServiceException(String.format("ERRO: %s ao obter empresas replicar %s%n", e.getMessage(), key));
        }
        return unidadesReplicar;
    }


    private void redeempresa(ClientDiscoveryDataDTO client, String filters) {
        HashMap<Integer, RedeDTO> mapRede = new HashMap<>();
        client.setRedeEmpresas(new ArrayList<>());
        if (client.getFinanceiroEmpresas() != null) {
            for (EmpresaFinanceiroDTO empresaFinanceiroDTO : client.getFinanceiroEmpresas()) {
                if (mapRede.containsKey(empresaFinanceiroDTO.getRedeEmpresaId())) {
                    continue;
                }
                if (empresaFinanceiroDTO.getRedeEmpresaId() != null) {
                    List<EmpresaFinanceiroDetailDTO> chaveNomeEmpresas = new ArrayList<>();
                    try {
                        if (isNotBlank(filters) && !filters.equals("{}")) {
                            chaveNomeEmpresas = empresaFinanceiroDao.findAllByRedeEmpresaIdDadosResumidosSearchText(empresaFinanceiroDTO.getRedeEmpresaId(), filters);
                        } else {
                            chaveNomeEmpresas = empresaFinanceiroDao.findAllByRedeEmpresaIdDadosResumidos(empresaFinanceiroDTO.getRedeEmpresaId());
                        }
                    } catch (Exception e) {
                        System.out.printf("Erro ao carregar Rede de Empresas %s%n", empresaFinanceiroDTO.getRedeEmpresaId());
                        System.out.println(e.getMessage());
                    }
                    for (EmpresaFinanceiroDetailDTO empresaDetailed : chaveNomeEmpresas) {
                        RedeDTO redeDTO = getRedeDTO(empresaDetailed);
                        client.getRedeEmpresas().add(redeDTO);
                        mapRede.put(empresaFinanceiroDTO.getRedeEmpresaId(), redeDTO);
                    }
                }
            }
        }
    }

    private static RedeDTO getRedeDTO(EmpresaFinanceiroDetailDTO empresaDetailed) {
        RedeDTO redeDTO = new RedeDTO();
        redeDTO.setEmpresaZw(empresaDetailed.getEmpresaZw());
        redeDTO.setChave(empresaDetailed.getChaveZw());
        redeDTO.setNomeFantasia(empresaDetailed.getNomeFantasia());
        redeDTO.setPlanoMsUrl(empresaDetailed.getUrlPlanoMsNormalized());
        redeDTO.setUrlCadAuxMs(empresaDetailed.getUrlCadAuxMsNormalized());
        redeDTO.setAdmCoreMsUrl(empresaDetailed.getUrlAdmCoreMsNormalized());
        redeDTO.setPessoaMsUrl(empresaDetailed.getUrlPessoaMsNormalized());
        redeDTO.setProdutoMsUrl(empresaDetailed.getUrlProdutoMsNormalized());
        redeDTO.setUrlTreino(empresaDetailed.getUrlTreino());
        return redeDTO;
    }


    private List<EmpresaFinanceiroDTO> obterEmpresaFinanceiro(String chave) throws Exception {
        List<EmpresaFinanceiroDTO> empsFin = new ArrayList<>();
        List<EmpresaFinanceiro> list = empresaFinanceiroDao.consultarPorChave(chave);
        for (EmpresaFinanceiro empresaFinanceiro : list) {
            EmpresaFinanceiroDTO empfin = new EmpresaFinanceiroDTO(empresaFinanceiro);
            empsFin.add(empfin);
        }
        return empsFin;
    }

    public Empresa obterEmpresa(String chave) throws ServiceException {
        return es.obterObjetoPorChave(chave);
    }

    private ClientDiscoveryDataDTO configs(Empresa empresa, ClientDiscoveryDataDTO client, Paths paths) {
        if (empresa != null) {
            client.setModulosHabilitados(Modulo.modulos(empresa.getModulos()));
            client.getServiceUrls().setAlunoMsUrl(empresa.getMsalunos());
            client.getServiceUrls().setColaboradorMsUrl(empresa.getMscolaborador());
            client.getServiceUrls().setTreinoApiUrl(empresa.getUrlTreino() + "/prest");
            client.getServiceUrls().setZwUrl(empresa.getRoboControle());
        }
        client.getServiceUrls().setGraduacaoMsUrl(paths.getGraduacao());
        client.getServiceUrls().setLoginMsUrl(paths.getAutenticacao());
        client.getServiceUrls().setPersonagemMsUrl(paths.getPersonagem());
        client.getServiceUrls().setFrontPersonal(paths.getFrontPersonal());
        return client;
    }

    private String cadastroAuxiliarUrl(Empresa empresa){
        if(!Uteis.nullOrEmpty(empresa.getUrlCadAuxMs())){
            return empresa.getUrlCadAuxMs();
        }else if(empresa.getRoboControle() != null){
            return empresa.getRoboControle().replace("/app", "/cad-aux-ms");
        }else{
            return null;
        }
    }

    private String planoMsUrl(Empresa empresa){
        if(!Uteis.nullOrEmpty(empresa.getUrlPlanoMs())){
            return empresa.getUrlPlanoMs();
        }else if(empresa.getRoboControle() != null){
            return empresa.getRoboControle().replace("/app", "/plano-ms");
        }else{
            return null;
        }
    }

    private String produtoMsUrl(Empresa empresa){
        if(!Uteis.nullOrEmpty(empresa.getUrlProdutoMs())){
            return empresa.getUrlProdutoMs();
        }else if(empresa.getRoboControle() != null){
            return empresa.getRoboControle().replace("/app", "/produto-ms");
        }else{
            return null;
        }
    }

    private String pactoPayDashUrl(Empresa empresa){
        if(!Uteis.nullOrEmpty(empresa.getUrlPactoPayDash())){
            return empresa.getUrlPactoPayDash();
        }else if(empresa.getRoboControle() != null){
            return empresa.getRoboControle().replace("/app", "/pay-dash-api");
        }else{
            return null;
        }
    }

    private String pactoPayMsUrl(Empresa empresa){
        if(!Uteis.nullOrEmpty(empresa.getUrlPactoPayMs())){
            return empresa.getUrlPactoPayMs();
        }else if(empresa.getRoboControle() != null){
            return empresa.getRoboControle().replace("/app", "/pactopay-ms");
        }else{
            return null;
        }
    }

    private String admCoreMsUrl(Empresa empresa) {
        if (!Uteis.nullOrEmpty(empresa.getUrlAdmCoreMs())) {
            return empresa.getUrlAdmCoreMs();
        } else if (empresa.getRoboControle() != null) {
            return empresa.getRoboControle().replace("/app", "/adm-core-ms");
        } else {
            return null;
        }
    }

    private String pessoaMsUrl(Empresa empresa) {
        if (!Uteis.nullOrEmpty(empresa.getUrlPessoaMs())) {
            return empresa.getUrlPessoaMs();
        } else if (empresa.getRoboControle() != null) {
            return empresa.getRoboControle().replace("/app", "/pessoa-ms");
        } else {
            return null;
        }
    }


    private List<EmpresaDTO> empresasChave(String chave, ClientDiscoveryDataDTO client) {
        if(client.getServiceUrls().getZwUrl() == null || client.getServiceUrls().getZwUrl().isEmpty()){
            return new ArrayList<>();
        }
        List<EmpresaDTO> empresas = new ArrayList<>();
        try {
            if(Arrays.asList(client.getModulosHabilitados()).contains(Modulo.ZILLYON_WEB.getSiglaModulo())){
                //obter empresas da chave
                ResponseEntity<String> resposta = httpServico.doJson(client.getServiceUrls().getZwUrl()
                                + "/prest/empresa/todas?chave=" + chave,
                        "",
                        HttpMethod.GET,
                        "");
                JSONArray array = new JSONArray(resposta.getBody());
                for (int i = 0; i < array.length(); i++) {
                    empresas.add(new EmpresaDTO(array.getJSONObject(i)));
                }
            }else {
                //obter empresas do banco do treino
                ResponseEntity<String> resposta = httpServico.doJson(client.getServiceUrls().getTreinoApiUrl()
                                + "/config/"+chave+"/empresas",
                        "",
                        HttpMethod.GET,
                        "");
                JSONArray array = new JSONObject(resposta.getBody()).getJSONArray("return");
                for (int i = 0; i < array.length(); i++) {
                    empresas.add(new EmpresaDTO(array.getJSONObject(i)));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return empresas;
    }

    public Boolean integranteRedeEmpresa(String chave) throws ServiceException {
        try {
            return empresaFinanceiroDao.findByIntegranteRedeEmpresa(chave);
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public List obterChavesPorChaveRede(String chaveRede) throws ServiceException {
        List<String> array = new ArrayList<>();
        Dao daoOAMD = new Dao();
        try {
            try {
                StringBuilder sb = new StringBuilder().append("select\n").
                        append("\tdistinct e2.chave\n").
                        append("from\n").
                        append("\tempresafinanceiro e\n").
                        append("inner join empresa e2 on\n").
                        append("\te2.chave = e.chavezw\n");

                if (chaveRede != null && chaveRede.contains(",") && chaveRede.split(",").length > 0) {
                    sb.append("where e2.chave in(").append(Uteis.splitFromArray(chaveRede.split(","), true)).append(") ");
                } else {
                    sb.append("inner join redeempresa r2 on\n").
                    append("\tr2.id = e.redeempresa_id\n").
                    append("where r2.chaverede = '").append(chaveRede).append("'");
                }

                ResultSet rs = daoOAMD.criarConsulta(sb.toString());

                while (rs.next()) {
                    array.add(rs.getString("chave"));
                }
            } catch (Exception e) {
                throw new ServiceException(e);
            }
        } finally {
            daoOAMD.fecharConexao();
        }
        return array;
    }
}
