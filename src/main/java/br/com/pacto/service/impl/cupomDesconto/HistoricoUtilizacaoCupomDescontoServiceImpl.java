package br.com.pacto.service.impl.cupomDesconto;

import br.com.pacto.bean.campanhaCupomDesconto.CampanhaCupomDesconto;
import br.com.pacto.bean.cupomDesconto.CupomDesconto;
import br.com.pacto.bean.cupomDesconto.HistoricoUtilizacaoCupomDesconto;
import br.com.pacto.bean.cupomDesconto.TipoConsultaCupomDescontoEnum;
import br.com.pacto.dao.intf.cupomDesconto.CupomDescontoDao;
import br.com.pacto.dao.intf.cupomDesconto.HistoricoUtilizacaoCupomDescontoDao;
import br.com.pacto.service.intf.cupomDesconto.HistoricoUtilizacaoCupomDescontoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class HistoricoUtilizacaoCupomDescontoServiceImpl implements HistoricoUtilizacaoCupomDescontoService {

    @Autowired
    HistoricoUtilizacaoCupomDescontoDao historicoUtilizacaoCupomDescontoDao;

    @Autowired
    CupomDescontoDao cupomDescontoDao;

    @Override
    public void incluir(CupomDesconto cupomDesconto, int contrato) throws Exception {
        historicoUtilizacaoCupomDescontoDao.insert(historicoUtilizacaoCupomDescontoDao.montarHistoricoCupom(cupomDesconto, contrato));
    }

    @Override
    public void informarEstorno(int contrato, String chaveZW) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("contrato", contrato);
        params.put("chaveportadorcupom", chaveZW);
        HistoricoUtilizacaoCupomDesconto historicoUtilizacaoCupomDesconto = historicoUtilizacaoCupomDescontoDao.findObjectByParam("SELECT obj FROM HistoricoUtilizacaoCupomDesconto obj WHERE obj.contrato = :contrato and obj.chavePortadorCupom = :chaveportadorcupom", params);
        if(historicoUtilizacaoCupomDesconto != null){
            historicoUtilizacaoCupomDesconto.setContratoEstornado(true);
            historicoUtilizacaoCupomDescontoDao.update(historicoUtilizacaoCupomDesconto);
        }
    }

    @Override
    public List<HistoricoUtilizacaoCupomDesconto> consultarHistoricoCupom(CampanhaCupomDesconto campanhaCupomDesconto, String chaveZW, Integer empresaZW, TipoConsultaCupomDescontoEnum tipoConsultaCupomDescontoEnum, String numeroCupom)throws Exception{
        return historicoUtilizacaoCupomDescontoDao.consultarHistoricoCupom(campanhaCupomDesconto, chaveZW, empresaZW, tipoConsultaCupomDescontoEnum, numeroCupom);

    }
}
