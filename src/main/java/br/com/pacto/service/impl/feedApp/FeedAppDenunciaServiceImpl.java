/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.feedApp;

import br.com.pacto.bean.feedApp.FeedAppDenuncia;
import br.com.pacto.dao.intf.feedApp.FeedAppDenunciaDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.feedApp.FeedAppDenunciaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * <AUTHOR>
 */
@Service
public class FeedAppDenunciaServiceImpl implements FeedAppDenunciaService {

    @Autowired
    private FeedAppDenunciaDao feedAppDenunciaDao;

    @Override
    public FeedAppDenuncia obterPorId(Integer id) throws ServiceException {
        try {
            return feedAppDenunciaDao.findObjectByAttribute("codigo", id);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public FeedAppDenuncia inserir(FeedAppDenuncia object) throws ServiceException {
        try {
            return feedAppDenunciaDao.insert(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public FeedAppDenuncia alterar(FeedAppDenuncia object) throws ServiceException {
        try {
            return feedAppDenunciaDao.update(object);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void excluir(FeedAppDenuncia object) throws ServiceException {
        try {
            feedAppDenunciaDao.delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

}
