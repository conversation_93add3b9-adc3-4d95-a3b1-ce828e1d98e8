package br.com.pacto.service.impl.produtoPacto;

import br.com.pacto.bean.empresa.Paths;
import br.com.pacto.bean.produtoPacto.ConfigProdutoPacto;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.dao.intf.produtoPacto.ConfigProdutoPactoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.configassinatura.PlanoDTO;
import br.com.pacto.service.impl.empresa.DiscoveryService;
import br.com.pacto.service.intf.produtoPacto.ConfigProdutoPactoService;
import br.com.pacto.util.HttpServico;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 04/06/2020
 *
 */
@Service
public class ConfigProdutoPactoServiceImpl implements ConfigProdutoPactoService {

    @Autowired
    private HttpServico httpServico;
    @Autowired
    private ConfigProdutoPactoDao configDAO;
    @Autowired
    private DiscoveryService discoveryService;

    private String urlApi() throws Exception {
        Paths paths = discoveryService.paths();
        return paths.getApiZw();
    }

    @Override
    public ConfigProdutoPacto salvar(ConfigProdutoPacto object, String usuario) throws ServiceException {
        try {
            configDAO.executeNativeSQL("update ConfigProdutoPacto set ativo = false;");
            object.setId(null);
            object.setUsuario(usuario);
            object.setDataRegistro(Calendario.hoje());
            object.setAtivo(true);
            return configDAO.insert(object);
        } catch (Exception e) {
            throw new ServiceException("Problema ao salvar a configuração de ConfigProdutoPacto. Erro em : " + e.getMessage());
        }
    }

    @Override
    public ConfigProdutoPacto obterAtivo(String usuario) throws ServiceException {
        try {
            List<ConfigProdutoPacto> param = configDAO.findByParam("select obj from ConfigProdutoPacto obj where obj.ativo = true order by obj.dataRegistro desc limit 1", new HashMap<>());
            if (param != null && !param.isEmpty()) {
                return param.get(0);
            }
//            return salvar(new ConfigProdutoPacto(), usuario);
            return null;
        } catch (Exception e) {
            throw new ServiceException("Problema ao obter a configuração de ConfigProdutoPacto. Erro em : " + e.getMessage());
        }
    }

    @Override
    public List<EmpresaJSON> empresas(ConfigProdutoPacto config) throws ServiceException {
        try {
            List<EmpresaJSON> empresas = new ArrayList<>();
            String urlApi = urlApi();
            JSONObject retorno = httpServico.postJson(urlApi + "/gempresarial/" + config.getChave() + "/consultarEmpresas",
                    "", null);
            JSONArray jsonArray = retorno.optJSONArray("return");
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    EmpresaJSON empresaJSON = new EmpresaJSON();
                    JSONObject empresa = jsonArray.optJSONObject(i);
                    empresaJSON.setCodigo(empresa.optInt("codigo"));
                    empresaJSON.setNome(empresa.optString("nome"));
                    empresas.add(empresaJSON);
                }
            }
            return empresas;
        } catch (Exception e) {
            throw new ServiceException("Problema ao obter empresas. Erro em : " + e.getMessage());
        }
    }

    @Override
    public List<PlanoDTO> planos(ConfigProdutoPacto config) throws ServiceException {
        try {
            List<PlanoDTO> planos = new ArrayList<>();
            String urlApi = urlApi();
            JSONObject retorno = httpServico.getJson(urlApi + "/v2/vendas/" + config.getChave() + "/planos/" + config.getEmpresa(), null);
            JSONArray jsonArray = retorno.optJSONArray("return");
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    planos.add(new PlanoDTO(jsonArray.optJSONObject(i)));
                }
            }
            return planos;
        } catch (Exception e) {
            throw new ServiceException("Problema ao obter empresas. Erro em : " + e.getMessage());
        }
    }

    @Override
    public List<ProdutoDTO> produtos(ConfigProdutoPacto config) throws ServiceException {
        try {
            List<ProdutoDTO> planos = new ArrayList<>();
            String urlApi = urlApi();
            JSONObject retorno = httpServico.getJson(urlApi + "/v2/vendas/" + config.getChave() + "/produtos/" + config.getEmpresa() + "/0", null);
            JSONArray jsonArray = retorno.optJSONArray("return");
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    planos.add(new ProdutoDTO(jsonArray.optJSONObject(i)));
                }
            }
            return planos;
        } catch (Exception e) {
            throw new ServiceException("Problema ao obter empresas. Erro em : " + e.getMessage());
        }
    }
}
