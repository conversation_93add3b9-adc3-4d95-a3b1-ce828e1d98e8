/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.empresa;

import br.com.pacto.service.intf.empresa.EmpresaSiteValidacao;
import br.com.pacto.bean.empresa.EmpresaSite;
import br.com.pacto.dao.intf.empresa.EmpresaSiteDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.objeto.Validacao;
import br.com.pacto.service.exception.ValidacaoException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class EmpresaSiteValidacaoImpl implements EmpresaSiteValidacao {

    @Autowired
    private EmpresaSiteDao empresaSiteDao;
    @Autowired
    private Validacao v;

    @Override
    public void validar(final EmpresaSite e, final Boolean validarTudo, final boolean validarEmpresaCadastrada) throws ValidacaoException, Exception {
        if (v.emptyString(e.getNomeFantasia())) {
            throw new ValidacaoException("O Nome Fantasia da empresa é obrigatório");
        }
        if (v.emptyString(e.getRazaoSocial())) {
            throw new ValidacaoException("A Razão Social da empresa é obrigatória");
        }
        if (v.emptyString(e.getCidade()) && validarTudo) {
            throw new ValidacaoException("A Cidade da empresa é obrigatória");
        }
        if (v.emptyString(e.getCnpj()) && validarTudo) {
            throw new ValidacaoException("O CNPJ é obrigatório");
        }
        if (v.emptyString(e.getCpf()) && validarTudo) {
            throw new ValidacaoException("O CPF é obrigatório");
        }
        if (v.emptyString(e.getEmail())) {
            throw new ValidacaoException("O E-mail é obrigatório");
        }
        if (v.emptyString(e.getEstado()) && validarTudo) {
            throw new ValidacaoException("O Estado é obrigatório");
        }
        if (v.emptyString(e.getNomeResponsavel())) {
            throw new ValidacaoException("O Nome do Responsável é obrigatório");
        }
        if (v.emptyString(e.getSexo()) && validarTudo) {
            throw new ValidacaoException("O Sexo é obrigatório");
        }
        if (v.emptyString(e.getTelCelular()) && validarTudo) {
            throw new ValidacaoException("O Celular é obrigatório");
        }
        if (validarTudo && !Uteis.validarTelefoneCelular(e.getTelCelular())) {
            throw new ValidacaoException("O Celular é inválido");
        }
        if (v.emptyString(e.getTelFixo()) && validarTudo) {
            throw new ValidacaoException("O Telefone Fixo é obrigatório");
        }
        if (e.getDataNasc() != null && validarTudo) {
            throw new ValidacaoException("O Telefone Fixo é obrigatório");
        }

        if (validarEmpresaCadastrada) {
            StringBuilder sb = new StringBuilder();
            sb.append("select obj from EmpresaSite obj where email = :email or cpf = :cpf or cnpj = :cnpj");
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("email", e.getEmail());
            p.put("cpf", e.getCpf());
            p.put("cnpj", e.getCnpj());
            List<EmpresaSite> existentes = empresaSiteDao.findByParam(sb.toString(), p);
            if (!v.emptyList(existentes)) {
                throw new ValidacaoException("Empresa já cadastrada!");
            }
        }
    }
}
