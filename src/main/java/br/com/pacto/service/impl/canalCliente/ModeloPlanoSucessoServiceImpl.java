package br.com.pacto.service.impl.canalCliente;

import br.com.pacto.bean.canalCliente.MaterialApoio;
import br.com.pacto.bean.canalCliente.ModeloPlanoSucesso;
import br.com.pacto.bean.canalCliente.ModeloPlanoSucessoAcao;
import br.com.pacto.controller.utils.PaginadorDTO;
import br.com.pacto.dao.intf.canalCliente.MaterialApoioDao;
import br.com.pacto.dao.intf.canalCliente.ModeloPlanoSucessoAcaoDao;
import br.com.pacto.dao.intf.canalCliente.ModeloPlanoSucessoDao;
import br.com.pacto.service.intf.canalCliente.ModeloPlanoSucessoService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ModeloPlanoSucessoServiceImpl implements ModeloPlanoSucessoService {

    @Autowired
    private ModeloPlanoSucessoDao modeloPlanoSucessoDao;
    @Autowired
    private ModeloPlanoSucessoAcaoDao modeloPlanoSucessoAcaoDao;
    @Autowired
    private MaterialApoioDao materialApoioDao;

    @Override
    @Deprecated
    public List<ModeloPlanoSucesso> consultarPlanosSucessoPorChave(String chave) {
        return consultarPlanosSucessoPorChave(chave, null, null);
    }

    @Override
    public List<ModeloPlanoSucesso> consultarPlanosSucessoPorChave(String chave, PaginadorDTO paginadorDTO, JSONObject filtros) {
        List<ModeloPlanoSucesso> modelos = modeloPlanoSucessoDao.consultarPorChave(chave);

        if (filtros != null) {
            boolean usarFiltrados = false;
            List<ModeloPlanoSucesso> planosFiltrados = new ArrayList<>();
            String quickSearch = filtros.optString("quicksearchValue");
            if (!UteisValidacao.emptyString(quickSearch)) {
                usarFiltrados = true;
                for (ModeloPlanoSucesso modelo : modelos) {
                    if (modelo.getNome().toLowerCase().contains(quickSearch.toLowerCase())) {
                        planosFiltrados.add(modelo);
                    }
                }
            }
            if (usarFiltrados) {
                modelos = planosFiltrados;
            }

        }

        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos((long) modelos.size());
            paginadorDTO.setSize((long) modelos.size());
            paginadorDTO.setPage((long) 0);
        }
        return modelos;
    }

    @Override
    public List consultarTodos() throws Exception {
        return modeloPlanoSucessoDao.consultarTodos();
    }

    @Override
    public ModeloPlanoSucesso incluir(ModeloPlanoSucesso modeloPlanoSucesso) throws Exception {
        for (ModeloPlanoSucessoAcao acao : modeloPlanoSucesso.getAcoes()) {
            acao.setModeloPlanoSucesso(modeloPlanoSucesso);
            for (MaterialApoio materialApoio : acao.getMateriaisApoio()) {
                materialApoio.setModeloPlanoSucessoAcao(acao);
            }
        }

        for (ModeloPlanoSucessoAcao acao : modeloPlanoSucesso.getAcoes()) {
            for (MaterialApoio materialApoio : acao.getMateriaisApoio()) {
                materialApoio.setModeloPlanoSucessoAcao(acao);
            }
        }

        modeloPlanoSucesso = modeloPlanoSucessoDao.insert(modeloPlanoSucesso);
        return modeloPlanoSucesso;
    }

    @Override
    public ModeloPlanoSucesso obterPorCodigo(Integer codigo) {
        return modeloPlanoSucessoDao.findById(codigo);
    }

    @Override
    public void excluir(ModeloPlanoSucesso modeloPlanoSucesso) throws Exception {
        modeloPlanoSucessoDao.delete(modeloPlanoSucesso);
    }

    @Override
    public ModeloPlanoSucesso atualizar(ModeloPlanoSucesso modeloPlanoSucesso) throws Exception {
        return modeloPlanoSucessoDao.update(modeloPlanoSucesso);
    }

    @Override
    public ModeloPlanoSucesso alterar(ModeloPlanoSucesso modeloPlanoSucesso) throws Exception {

        // @todo Esta é uma Solução provisoria. Será necessário migrar o hibernate para uma versão mais nova ou encontrar a solução do problema de cascade
        // No metodo incluir dessa classe também tem um problema desse.
        ModeloPlanoSucesso modeloPlanoSucessoAtual = modeloPlanoSucessoDao.findById(modeloPlanoSucesso.getCodigo());

        List<Integer> codigosAcoesAlteradas = new ArrayList<>();
        for (ModeloPlanoSucessoAcao acao : modeloPlanoSucesso.getAcoes()) {
            codigosAcoesAlteradas.add(acao.getCodigo());
        }

        for (ModeloPlanoSucessoAcao acaoAtual : modeloPlanoSucessoAtual.getAcoes()) {
            if (!codigosAcoesAlteradas.contains(acaoAtual.getCodigo())) {
                modeloPlanoSucessoAcaoDao.delete(acaoAtual);
            }
        }

        for (ModeloPlanoSucessoAcao acao : modeloPlanoSucesso.getAcoes()) {
            if (acao.getCodigo() == null) {
                acao.setModeloPlanoSucesso(modeloPlanoSucesso);
                modeloPlanoSucessoAcaoDao.insert(acao);
            } else {
                modeloPlanoSucessoAcaoDao.update(acao);
            }
        }


        for (ModeloPlanoSucessoAcao acao : modeloPlanoSucesso.getAcoes()) {
            materialApoioDao.deleteComParam(new String[]{"modeloPlanoSucessoAcao.codigo"}, new Object[]{acao.getCodigo()});
            for (MaterialApoio materialApoio : acao.getMateriaisApoio()) {
                materialApoio.setCodigo(null);
                materialApoio.setModeloPlanoSucessoAcao(acao);
                materialApoioDao.insert(materialApoio);
            }
        }

        return modeloPlanoSucessoDao.update(modeloPlanoSucesso);
    }
}
