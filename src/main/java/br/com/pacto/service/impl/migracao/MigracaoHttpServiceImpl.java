package br.com.pacto.service.impl.migracao;

import br.com.pacto.bean.migracao.modelo.ConfiguracaoImportacao;
import br.com.pacto.bean.migracao.modelo.RegistroMigracao;
import br.com.pacto.bean.migracao.json.RespostaExportacaoRegistroMigracaoJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.service.intf.migracao.MigracaoHttpService;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpResponseException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;

/**
 * Descrição: Serviço responsáveis pelas chamadas HTTP realizadas pelo módulo de Migração
 * Projeto: NewOAMD
 *
 * <AUTHOR> - 24/abr/2018 às 15:56
 * Pacto Soluções - Todos os direitos reservados
 */
@Service
@Transactional(readOnly = true)
public class MigracaoHttpServiceImpl implements MigracaoHttpService {

    private final static String URL_API_ZW = Aplicacao.getProp(Aplicacao.URL_API_ZW);

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public RespostaExportacaoRegistroMigracaoJSON exportarRegistroMigracao(RegistroMigracao registroMigracao) {
        ConfiguracaoImportacao configuracaoImportacao = registroMigracao.getConfiguracaoImportacao();
        // URL_API/importacao/chave/endpoint?empresa=codigo
        String url = String.format("%s/importacao/%s/%s?empresa=%d",
                URL_API_ZW,
                configuracaoImportacao.getEmpresa().getChave(),
                registroMigracao.getTipo().getEndpointZW(),
                configuracaoImportacao.getCodigoEmpresa());
        try {
            return Request.Post(url)
                    .bodyString(registroMigracao.getConteudo(), ContentType.APPLICATION_JSON)
                    .execute()
                    .handleResponse(new HttpResponseHandler());
        } catch (Throwable e) {
            return new RespostaExportacaoRegistroMigracaoJSON(e.getMessage());
        }
    }
    
     @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public RespostaExportacaoRegistroMigracaoJSON excluirRegistroMigracaoZW(RegistroMigracao registroMigracao) {
        ConfiguracaoImportacao configuracaoImportacao = registroMigracao.getConfiguracaoImportacao();
        // URL_API/importacao/chave/endpoint?empresa=codigo
        String url = String.format("%s/importacao/%s/%s?empresa=%d",
                URL_API_ZW,
                configuracaoImportacao.getEmpresa().getChave(),
                registroMigracao.getTipo().getEndpointExclusaoZW(),
                configuracaoImportacao.getCodigoEmpresa());
        try {
            return Request.Post(url)
                    .bodyString(registroMigracao.getConteudo(), ContentType.APPLICATION_JSON)
                    .execute()
                    .handleResponse(new HttpResponseHandler());
        } catch (Throwable e) {
            return new RespostaExportacaoRegistroMigracaoJSON(e.getMessage());
        }
    }

    private final static class HttpResponseHandler implements ResponseHandler<RespostaExportacaoRegistroMigracaoJSON> {

        private final static Gson GSON = new Gson();
        @Override
        public RespostaExportacaoRegistroMigracaoJSON handleResponse(HttpResponse response) throws ClientProtocolException, IOException {
            StatusLine statusLine = response.getStatusLine();
            HttpEntity entity = response.getEntity();
            if (statusLine.getStatusCode() >= 300) {
                throw new HttpResponseException(statusLine.getStatusCode(), statusLine.getReasonPhrase());
            }
            if (entity == null) {
                throw new ClientProtocolException("A resposta nao contem conteudo.");
            }

            ContentType contentType = ContentType.getOrDefault(entity);

            if (!contentType.getMimeType().equals(ContentType.APPLICATION_JSON.getMimeType())) {
                throw new ClientProtocolException("Content type inexperado:" + contentType);
            }

            StringWriter writer = new StringWriter();
            IOUtils.copy(entity.getContent(), writer, contentType.getCharset());
            RespostaExportacaoRegistroMigracaoJSON resposta = GSON.fromJson(writer.toString(), RespostaExportacaoRegistroMigracaoJSON.class);
            if (resposta.getCodigoRegistroZW() == null && resposta.getStatus() == null) {
                resposta.setMensagem("A resposta recebida nao esta no formato esperado");
            }
            return resposta;
        }
    }
}
