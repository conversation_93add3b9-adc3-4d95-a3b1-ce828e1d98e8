/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.infra;

import br.com.pacto.bean.empresa.InfoInfraEnum;
import br.com.pacto.bean.infra.EstatisticasInfra;
import br.com.pacto.bean.infra.IpLocationInfo;
import br.com.pacto.bean.infra.UsuarioLogado;
import br.com.pacto.dao.intf.infra.EstatisticasInfraDao;
import br.com.pacto.dao.intf.infra.IpLocationInfoDao;
import br.com.pacto.dao.intf.infra.UsuarioLogadoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.HttpRequestUtil;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.objeto.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.infra.UsuarioLogadoService;
import br.com.pacto.util.ViewUtils;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class UsuarioLogadoServiceImpl implements UsuarioLogadoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private UsuarioLogadoDao usuarioLogadoDao;
    @Autowired
    private EstatisticasInfraDao estatisticasInfraDao;
    @Autowired
    private IpLocationInfoDao ipLocationInfoDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public UsuarioLogadoDao getUsuarioLogadoDao() {
        return this.usuarioLogadoDao;
    }

    public void setUsuarioLogadoDao(UsuarioLogadoDao usuarioLogadoDao) {
        this.usuarioLogadoDao = usuarioLogadoDao;
    }

    public UsuarioLogado alterar(UsuarioLogado object) throws ServiceException {
        try {
            return getUsuarioLogadoDao().update(object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void excluir(UsuarioLogado object) throws ServiceException {
        try {
            getUsuarioLogadoDao().delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public UsuarioLogado inserir(UsuarioLogado object) throws ServiceException {
        try {
            return getUsuarioLogadoDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public UsuarioLogado obterObjetoPorParam(String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getUsuarioLogadoDao().findObjectByParam(query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public UsuarioLogado obterPorId(Integer id) throws ServiceException {
        try {
            return getUsuarioLogadoDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<UsuarioLogado> obterPorParam(String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getUsuarioLogadoDao().findByParam(query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<UsuarioLogado> obterPorParam(String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getUsuarioLogadoDao().findByParam(query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public List<UsuarioLogado> obterTodos() throws ServiceException {
        try {
            return getUsuarioLogadoDao().findAll();
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void persistirDados(UsuarioLogado u) throws ServiceException {
        try {
            final StringBuilder hql = new StringBuilder(
                    "select o from UsuarioLogado o where infoInfra =:infra ")
                    .append("and sessionId =:id");
            Map<String, Object> p = new HashMap();
            p.put("infra", u.getInfoInfra());
            p.put("id", u.getSessionId());
//            System.out.println("persistirDados => " + p.toString());
            UsuarioLogado userExist = usuarioLogadoDao.findObjectByParam(
                    hql.toString(), p);
            if (userExist != null && userExist.getCodigo() != null) {
                userExist.setBrowser(u.getBrowser());
                userExist.setDtCriacao(u.getDtCriacao());
                userExist.setEmpresa(u.getEmpresa());
                userExist.setSessionId(u.getSessionId());
                userExist.setInfoInfra(u.getInfoInfra());
                userExist.setInstancia(u.getInstancia());
                userExist.setIp(u.getIp());
                userExist.setLastURI(u.getLastURI());
                userExist.setProcId(u.getProcId());
                userExist.setUltAcesso(u.getUltAcesso());
                userExist.setUsuario(u.getUsuario());
                userExist.setCodigoEmpresa(u.getCodigoEmpresa());
                userExist.setCnpj(u.getCnpj());
                userExist.setChave(u.getChave());
                alterar(userExist);
            } else {
                inserir(u);
            }
        } catch (Exception ex) {
            Logger.getLogger(UsuarioLogadoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            //ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public void persistirDadosInfra(InfoInfraEnum infra, final String serverName, final String dados) {
        try {
            JSONObject o = new JSONObject(dados);
            if (!o.isNull("diskSize")) {
                EstatisticasInfra e = null;
                if (infra == InfoInfraEnum.OUTRO) {
                    e = estatisticasInfraDao.findObjectByAttributes(new String[]{"infoInfra", "serverName"},
                            new Object[]{infra, serverName}, null);
                } else {
                    e = estatisticasInfraDao.findObjectByAttributes(new String[]{"infoInfra"}, new Object[]{infra}, null);
                }
                if (e == null) {
                    e = new EstatisticasInfra();
                    e.setInfoInfra(infra);
                    e.setServerName(serverName);
                    e.setDiskSize(o.getLong("diskSize"));
                    e.setDiskFree(o.getLong("diskFree"));
                    e.setDiskUsed(o.getLong("diskUsed"));
                    e.setDataRegister(Calendario.hoje());
                    e.setLastUpdate(Calendario.hoje());
                    e.setSysRelease(o.optString("sys_release"));
                    e.setSysUname(o.optString("sys_uname"));
                    e.setMemSizeMB(o.getLong("memorySize"));
                    e.setXmxMemSizeMB(o.getLong("totalMemoryUsed"));
                    estatisticasInfraDao.insert(e);
                } else {
                    e.setDiskSize(o.getLong("diskSize"));
                    e.setDiskFree(o.getLong("diskFree"));
                    e.setDiskUsed(o.getLong("diskUsed"));
                    e.setSysRelease(o.optString("sys_release"));
                    e.setSysUname(o.optString("sys_uname"));
                    e.setLastUpdate(Calendario.hoje());
                    e.setMemSizeMB(o.getLong("memorySize"));
                    e.setXmxMemSizeMB(o.getLong("totalMemoryUsed"));
                    estatisticasInfraDao.update(e);
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug("Server " + serverName + " - " + dados);
            Logger.getLogger(UsuarioLogadoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void persistirDadosIpLocation() {
        try {
            ResultSet rs = usuarioLogadoDao.createStatement("select distinct ul.ip from usuariologado ul "
                    + "left join iplocationinfo ipl on ul.ip = ipl.ip"
                    + " where ipl.ip is null and ul.ip <> '' and ul.ip <> 'N/C' order by ul.ip");
            while (rs.next()) {
                long t1 = System.currentTimeMillis();
                final String ip = rs.getString(1);
                final String urlApi = String.format("%s%s", Aplicacao.getProp(Aplicacao.urlAPIIpLocation), ip);
                final String retorno = HttpRequestUtil.executeRequestGET(urlApi, 10000, 5000);
                JSONObject o = new JSONObject(retorno);
                if (o.getString("ip") != null && o.getString("ip").equals(ip)) {
                    IpLocationInfo ipL = new IpLocationInfo(
                            o.getString("ip"),
                            o.getString("country_code"),
                            o.getString("region_code"),
                            o.getString("region_name"),
                            o.getString("city"),
                            o.getString("zip_code"),
                            o.getString("time_zone"),
                            o.getDouble("latitude"),
                            o.getDouble("longitude"),
                            o.getInt("metro_code"));
                    ipLocationInfoDao.insert(ipL);
                }
                long t2 = System.currentTimeMillis();
                Uteis.logar(String.format("Obtido IPLocation de %s em %s (ms). Resultado: %s", urlApi, (t2 - t1)));
            }
        } catch (Exception ex) {
            Logger.getLogger(UsuarioLogadoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    public void limparDados() throws ServiceException {
        try {
            final StringBuilder sql = new StringBuilder(
                    "delete from usuariologado ")
                    .append("where current_timestamp - ultacesso > '31 minutes' ");
            int n = usuarioLogadoDao.executeNativeSQL(sql.toString());
            System.out.println("limparDados => " + n);
        } catch (Exception ex) {
            Logger.getLogger(UsuarioLogadoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            //ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public Long countByIpAndInfoInfra(String ip, InfoInfraEnum infoInfraEnum) throws Exception {
        switch (infoInfraEnum) {
            case OUTRO:
                return usuarioLogadoDao.countByInfraAndIp(infoInfraEnum, ip);
            default:
                return usuarioLogadoDao.countByInfra(infoInfraEnum);
        }
    }  


}
