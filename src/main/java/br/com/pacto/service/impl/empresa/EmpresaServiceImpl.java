/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.empresa;

import br.com.pacto.bean.configuracoes.ConfiguracaoEnum;
import br.com.pacto.bean.empresa.DatacenterEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.EmpresaInfraDTO;
import br.com.pacto.bean.empresa.EmpresaOAMD2;
import br.com.pacto.bean.empresa.FlagSoftwareEnum;
import br.com.pacto.bean.empresa.IdentificadorEmpresaAcesso;
import br.com.pacto.bean.empresa.InfoInfraEnum;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.bean.feed.usuarioapp.UsuarioApp;
import br.com.pacto.bean.tag.Tag;
import br.com.pacto.bean.tag.TagEmpresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.empresa.EmpresaOAMDJSON;
import br.com.pacto.controller.json.ms.CompanyJSON;
import br.com.pacto.dao.Dao;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.dao.intf.empresa.IdentificadorEmpresaAcessoDao;
import br.com.pacto.dao.intf.tag.TagDao;
import br.com.pacto.dao.intf.tag.TagEmpresaDao;
import br.com.pacto.enums.Modulo;
import br.com.pacto.json.ItemEmpresaMovideskJSON;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.ColecaoUtils;
import br.com.pacto.objeto.Criptografia;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.empresafinanceiro.EmpresaFinanceiroService;
import br.com.pacto.service.intf.oamd.OAMDService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.usuarioapp.UsuarioAppService;
import br.com.pacto.service.intf.zw.UpdateServletService;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.Ordenacao;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.enumeradores.CarteirasEnum;
import br.com.pacto.util.enumeradores.SchemaBancoEnum;
import br.com.pacto.util.enumeradores.TipoCobrancaPactoEnum;
import br.com.pacto.util.server.CharsetEnum;
import br.com.pacto.util.server.Constants;
import br.com.pacto.util.server.GeradorScripts;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.movidesk.body.MensagensEnviadas;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.security.Principal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR>
 */
@Service
public class EmpresaServiceImpl implements EmpresaService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private GeradorScripts geradorScript;
    @Autowired
    private TagDao tagDao;
    @Autowired
    private TagEmpresaDao tagEmpresaDao;
    @Autowired
    private IdentificadorEmpresaAcessoDao idDao;
    @Autowired
    private OAMDService oamdService;
    @Autowired
    private UsuarioAppService uService;
    @Autowired
    private UpdateServletService updateServletService;
    @Autowired
    private EmpresaFinanceiroService empresaFinanceiroService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private DiscoveryService discoveryService;

    public EmpresaServiceImpl() {
//        InputStream inZWSchema = this.getClass().getResourceAsStream("/br/com/pacto/util/resources/scripts/ZW_SCHEMA_POVOADOR.sql");
//        InputStream inZWData = this.getClass().getResourceAsStream("/br/com/pacto/util/resources/scripts/ZW-DATA.sql");
//        InputStream inZWUpdates = this.getClass().getResourceAsStream("/br/com/pacto/util/resources/scripts/ZW-ATUALIZACOES.sql");

//        try {
//            schemaZW = Uteis.convertStreamToString(inZWSchema, CharsetEnum.UTF8.getNomeJava());
//            dadosZW += Uteis.convertStreamToString(inZWData, CharsetEnum.UTF8.getNomeJava());
//            dadosZW += Uteis.convertStreamToString(inZWUpdates, CharsetEnum.UTF8.getNomeJava());
//        } catch (IOException ex) {
//            Logger.getLogger(Principal.class.getName()).log(Level.SEVERE, null, ex);
//        }
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public EmpresaDao getEmpresaDao() {
        return this.empresaDao;
    }

    public void setEmpresaDao(EmpresaDao empresaDao) {
        this.empresaDao = empresaDao;
    }

    private void validar(Empresa empresa) throws ServiceException {
        if (empresa.getNomeBD().isEmpty()) {
            throw new ServiceException("Informe o nome da Empresa");
        }
        if (empresa.getHostBD().isEmpty()) {
            throw new ServiceException("Informe o host do Banco de Dados");
        }
        if (empresa.getPorta() <= 0) {
            throw new ServiceException("Informe a porta do Banco de Dados");
        }
        if (empresa.getNomeBD().isEmpty()) {
            throw new ServiceException("Informe o nome do Banco de Dados");
        }
        if (empresa.getUserBD().isEmpty()) {
            throw new ServiceException("Informe o usuário do Banco de Dados");
        }
        if (empresa.getPasswordBD().isEmpty()) {
            throw new ServiceException("Informe a senha do usuário do Banco de Dados");
        }
        if (empresa.getModulos().isEmpty() || (!empresa.getModulos().contains("ZW") && !empresa.getModulos().contains("TR") && !empresa.getModulos().contains("FC"))) {
            throw new ServiceException("Informe pelo menos um módulo do Sistema");
        }
        if (empresa.getVersaoSGBD() == null || empresa.getVersaoSGBD().isEmpty()) {
            throw new ServiceException("Informe a versão do SGBD. Por ex.: 9.3. Isso é importante por causa dos backups!");
        }
    }

    @Override
    public Empresa alterar(Empresa object) throws ServiceException {
        return alterar(object, false, null);
    }

    public Empresa alterar(Empresa object, boolean atualizarOAMDInfra, Usuario u) throws ServiceException {
        try {
            List<String> tags = object.getTagsSelecionadas();
            validar(object);
            Empresa newObject = getEmpresaDao().update(object);
            newObject.setHostBDSubir(object.getHostBDSubir());
            newObject.setPortaBDSubir(object.getPortaBDSubir());
            object = newObject;
            prepararTags(object, tags);

            if (atualizarOAMDInfra) {
                try {
                    atualizarOAMDInfra(object.getNomeBD(), object.getInfoInfra(), object);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                    if (object.getModulos().contains("TR")) {
                        atualizarOAMD2Infra(object.getNomeBD(), object.getInfoInfra(), object);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                try {
                   atualizarDadosEmpresaOAMDInfra(object, u);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            return object;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        } finally {
            discoveryService.reload(object.getChave());
            discoveryService.reloadLogin(object.getChave());
            refreshConfigs(object);
        }
    }

    public void refreshConfigs(Empresa empresa) {
        try {
            ExecuteRequestHttpService.executeRequestGET(empresa.getRoboControle() + "/UpdateServlet?op=refreshConfig&propagable=s&key=" + empresa.getChave());
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao atualizar configurações da empresa: " + empresa.getChave());
            ex.printStackTrace();
        }
    }

    @Override
    public void excluir(Empresa object) throws ServiceException {
        try {
            getEmpresaDao().delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Empresa inserir(Empresa object) throws ServiceException {
        try {
            List<String> tags = object.getTagsSelecionadas();
            object.setNomeBD(FlagSoftwareEnum.ZW.getPrefixoBanco() + object.getNomeEmpresa().toLowerCase());
            object.setChave(Criptografia.encryptMD5(object.getNomeEmpresa().toUpperCase()));
            object.setDataCadastro(Calendario.hoje());
            validar(object);
            try {
                Empresa tmp = obterPorId(object.getChave());
                if (tmp != null) {
                    object.setNomeBD(FlagSoftwareEnum.ZW.getPrefixoBanco() + object.getNomeEmpresa().toLowerCase() + object.getDataCadastro().getTime());
                    object.setChave(Criptografia.encryptMD5(object.getNomeEmpresa().toUpperCase() + object.getDataCadastro().getTime()));
                }
                if (UteisValidacao.emptyString(object.getDirSlaveBackup())) {
                    ResultSet rs = empresaDao.createStatement("select dirslavebackup, count(dirslavebackup) from empresa where dirslavebackup is not null group by dirslavebackup order by dirslavebackup");
                    Map<String, Integer> m = new HashMap<>();
                    for (int i = 1; i <= Constants.MAX_SLAVES_BACKUP; i++) {
                        m.put(String.format(Constants.PREFIXO_SLAVES_BACKUP, i), 0);
                    }
                    while (rs.next()) {
                        final String nomeSlave = rs.getString("dirslavebackup");
                        final Integer qtd = rs.getInt("count");
                        m.put(nomeSlave, qtd);
                    }
                    String nomeMenorQtd = null;
                    Integer qtdMenor = null;
                    for (Map.Entry<String, Integer> entry : m.entrySet()) {
                        final String nomeSlave = entry.getKey();
                        Integer qtd = entry.getValue();
                        if ((qtdMenor == null) || (qtd < qtdMenor)) {
                            qtdMenor = qtd;
                            nomeMenorQtd = nomeSlave;
                        }
                    }
                    if (nomeMenorQtd == null && !m.isEmpty()) {
                        nomeMenorQtd = m.keySet().iterator().next();
                        qtdMenor = m.get(nomeMenorQtd);
                    }
                    object.setDirSlaveBackup(nomeMenorQtd);
                }
                object = getEmpresaDao().insert(object);
                prepararTags(object, tags);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return object;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public Empresa obterObjetoPorParam(String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getEmpresaDao().findObjectByParam(query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Empresa obterObjetoPorChave(String chave)
            throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("Select obj from Empresa obj where obj.chave = :chave");
            HashMap<String, Object> param = new HashMap<String, Object>();
            param.put("chave", chave);
            return getEmpresaDao().findObjectByParam(sql.toString(), param);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Empresa obterPorId(String id) throws ServiceException {
        try {
            return getEmpresaDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Empresa> obterPorParam(String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getEmpresaDao().findByParam(query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Empresa> obterPorParam(String query,
                                       Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getEmpresaDao().findByParam(query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Empresa> obterTodos(boolean incluirEmpresasPacto) throws ServiceException {
        return obterTodos(incluirEmpresasPacto, null, false, null, null);
    }

    @Override
    public List<Empresa> obterTodos(boolean incluirEmpresasPacto, Boolean somenteAtivas) throws ServiceException {
        return obterTodos(incluirEmpresasPacto, null, false, null, null, somenteAtivas, null);
    }

    @Override
    public List<Empresa> obterTodosComInativas(boolean incluirEmpresasPacto) throws ServiceException {
        return obterTodos(incluirEmpresasPacto, null, false, null, null, false, null);
    }

    @Override
    public List<EmpresaOAMDJSON> obterTodosComInativasJSON(boolean incluirEmpresasPacto) throws Exception {
        List<EmpresaOAMDJSON> json = new ArrayList<EmpresaOAMDJSON>();
        StringBuilder sql = new StringBuilder("select * from empresa ");
        if (!incluirEmpresasPacto) {
            sql.append(" where usoInterno = false and ").
                    append("(\"nomeBD\" not in ('bdzillyontreinamento', 'bdzillyonpactosp') or \"nomeBD\" is null) ");
        }
        sql.append(" order by CASE WHEN identificadorEmpresa IS NULL THEN '' ELSE identificadorEmpresa END || \"nomeBD\"");
        ResultSet rs = getEmpresaDao().createStatement(sql.toString());
        while (rs.next()) {
            json.add(new EmpresaOAMDJSON(new Empresa(rs)));
        }
        return json;
    }

    @Override
    public List<Empresa> obterTodos(boolean incluirEmpresasPacto, Integer filtroCarteira,
                                    boolean somenteEmpresasUsoInterno, CarteirasEnum carteira, InfoInfraEnum infra) throws ServiceException {
        return obterTodos(incluirEmpresasPacto, null, false, null, infra, true, null);
    }

    public List<Empresa> obterTodos(boolean incluirEmpresasPacto, Integer filtroCarteira,
                                    boolean somenteEmpresasUsoInterno, CarteirasEnum carteira, InfoInfraEnum infra,
                                    boolean somenteAtivas, String arrayChaves) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<>();
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT obj FROM Empresa obj ");

            StringBuilder where = new StringBuilder(somenteAtivas ? " where obj.ativa = true" : " where 1 = 1 ");
            if (carteira != null) {
                where.append("  and   :carteira in obj.carteiras ");
                params.put("carteira", carteira);
            }
            if (somenteEmpresasUsoInterno) {
                where.append(" and obj.usoInterno = true ");
            }

            if (!incluirEmpresasPacto) {
                where.append(" and (nomeBD not in ('bdzillyontreinamento', 'bdzillyonpactosp') or nomeBD is null) ");
            }

            if (filtroCarteira != null && filtroCarteira == 0) {
                where.append(" and obj.usuarioCarteira.codigo is null ");
            } else if (filtroCarteira != null) {
                where.append(" and obj.usuarioCarteira.codigo = ").append(filtroCarteira);
            }
            if (infra != null) {
                where.append(" and infoInfra = ").append(":infra");
                params.put("infra", infra);
            }
            if (arrayChaves != null && arrayChaves.split(",").length > 0) {
                where.append(String.format(" and (obj.chave in (%s)) ", Uteis.splitFromArray(arrayChaves.split(","), true)));
            }
            where.append(" order by CASE WHEN identificadorEmpresa IS NULL THEN '' ELSE identificadorEmpresa END || nomeBD");
            sql.append(where);

            return obterPorParam(sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private Connection obterConexao(final String hostServidor, final String porta,
                                    final String user, final String pwd, String nomeBD) throws Exception {
        return Uteis.getConexao(hostServidor, porta, nomeBD, user, pwd);
    }

    @Override
    public void gerarEmpresa(InfoInfraEnum infra, FlagSoftwareEnum flag,
                             Empresa empresa, SchemaBancoEnum schemaBancoEnum) throws Exception {

        final Usuario usuarioAPI = usuarioService.validarUsuario(Constants.LOGIN_API_OAMD, Constants.SENHA_API_OAMD, false);
        if (flag == FlagSoftwareEnum.ZW) {
            gerarEmpresaZW(infra, flag, empresa, schemaBancoEnum, usuarioAPI);
        } else if (flag == FlagSoftwareEnum.TR) {
            gerarEmpresaTreino(infra, flag, empresa, schemaBancoEnum, usuarioAPI);
        } else if (flag == FlagSoftwareEnum.FC) {
            gerarEmpresaFacilite(infra, flag, empresa);
        }
    }

    @Override
    public Empresa descobrir(final String key) throws ServiceException {
        Empresa empresa = obterPorId(key);
        if (empresa != null && empresa.getAtiva()) {
            return empresa;
        } else {
            throw new ServiceException(String.format("%s - %s",
                    viewUtils.getMensagem("validacao.empresa.naoencontrada"),
                    !UteisValidacao.emptyString(key) && key.length() > 8 ? key.substring(0, 8) : key));
        }
    }

    @Override
    public void updateScriptsServicos(Empresa empresa, InfoInfraEnum infra) throws Exception {
        updateServices(empresa, infra);
    }

    private Connection obterConexaoBancoEmpresa(Empresa e, final String nomeBanco) throws Exception {
        return obterConexaoBancoEmpresa(e, nomeBanco, null);
    }

    private Connection obterConexaoBancoEmpresa(Empresa e, final String nomeBanco, String userBD) throws Exception {
        return this.obterConexao(e.getHostBDSubir() == null ? e.getHostBD() : e.getHostBDSubir(), e.getPortaBDSubir(), !UteisValidacao.emptyString(userBD) ? userBD : e.getUserBD(), e.getPasswordBD(), nomeBanco);
    }

    private void alterarZillyonWebSuperUser(String nomeBanco, Empresa empresa) throws SQLException {

        Uteis.logarDebug("ALTERAR USUÁRIO ZILLYONWEB");

        Connection con = null;
        try {
            con = obterConexaoBancoEmpresa(empresa, nomeBanco, "postgres");
            Statement stm = con.createStatement();

            boolean superUser = false;
            String sql = "SELECT u.usesuper FROM pg_catalog.pg_user u where u.usename = 'zillyonweb'";
            ResultSet rs = stm.executeQuery(sql);
            if (rs.next()) {
                superUser = rs.getBoolean(1);
            }
            if (!superUser) {
                stm.execute("ALTER USER zillyonweb WITH SUPERUSER;");
            }
        } catch (Exception e) {
            Uteis.logarDebug("WARN: " + e.getMessage());
        } finally {
            if (con != null) {
                con.close();
            }
        }
    }

    private void gerarEmpresaZW(InfoInfraEnum infra, FlagSoftwareEnum flag, Empresa empresa,
                                SchemaBancoEnum schemaBancoEnum, Usuario usuario) throws Exception {

        Uteis.logarDebug("SUBIR BANCO ZILLYONWEB");

        Connection con = null;
        String nomeBanco = flag.getPrefixoBanco() + empresa.getName().toLowerCase();
        con = obterConexaoBancoEmpresa(empresa, infra.getDatabaseDefault());
        try {
            Statement stm = con.createStatement();

            String sql = "CREATE DATABASE " + nomeBanco
                    + " ENCODING '" + CharsetEnum.UTF8.getNomePostgreSQL() + "'"
                    + " TEMPLATE=template0"
                    + " CONNECTION LIMIT = -1;";
            stm.execute(sql);
            con.close();

            alterarZillyonWebSuperUser(nomeBanco, empresa);

            Connection conLanguage = obterConexaoBancoEmpresa(empresa, nomeBanco, "postgres");
            try {
                stm = conLanguage.createStatement();
                stm.execute("DROP EXTENSION plpgsql; CREATE EXTENSION unaccent;");
            } catch (Exception e) {
                Uteis.logarDebug("WARN: " + e.getMessage());
            } finally {
                conLanguage.close();
            }

            conLanguage = obterConexaoBancoEmpresa(empresa, nomeBanco, "postgres");
            try {
                stm = conLanguage.createStatement();
                try {
                    stm.execute("DROP EXTENSION pg_trgm; CREATE EXTENSION unaccent;");
                } catch (Exception e) {
                    Uteis.logarDebug("WARN0: " + e.getMessage());
                }
            } catch (Exception e) {
                Uteis.logarDebug("WARN1: " + e.getMessage());
            } finally {
                conLanguage.close();
            }

            Connection conCreate = obterConexaoBancoEmpresa(empresa, nomeBanco);
            try {
                stm = conCreate.createStatement();
                if (schemaBancoEnum == null) {
                    schemaBancoEnum = SchemaBancoEnum.PADRAO;
                }
                stm.execute(obterSchema(schemaBancoEnum.getArquivoZW()));
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logarDebug("WARN2: " + e.getMessage());
            } finally {
                conCreate.close();
            }

            //updates que devem ser realizados após subir o banco.
            //exemplo.. atualização de senha dos usuários
            //by Luiz Felipe 22/04/2020
            List<String> listaUpdate = obterListaUpdateBancoZW();
            if (!UteisValidacao.emptyList(listaUpdate)) {
                Connection conUpdate = null;
                try {
                    conUpdate = obterConexaoBancoEmpresa(empresa, nomeBanco);
                    stm = conUpdate.createStatement();

                    for (String update : listaUpdate) {
                        try {
                            stm.execute(update);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    Uteis.logarDebug("WARN3: " + e.getMessage());
                } finally {
                    if (conUpdate != null) {
                        conUpdate.close();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(String.format("Erro: %s. Verifique se o banco do ZillyonWeb realmente está operante: ", e.getMessage()));
        } finally {
            atualizarOAMDInfra(nomeBanco, infra, empresa);
            atualizarBancoZW(empresa, usuario);
            atualizarBancoZWConfigCobranca(empresa, usuario);
        }
    }

    private void atualizarBancoZWConfigCobranca(Empresa empresa, Usuario usuario) {
        //atualiza valores de acordo com documento de boas praticas de cobrança
        try {
            List<String> listaUpdate = new ArrayList<>();
            listaUpdate.add("update empresa set agruparparcelasporcartao = true,agruparparcelasporcartaovalorlimite = 300,somenteumenviocartaotentativa = true, qtddiaslimitecobrancaparcelasrecorrencia = 95, qtddiasrepetircobrancaparcelasrecorrencia = 3");

            for (String update : listaUpdate) {
                try {
                    updateBancoZillyonWeb(empresa, usuario, update);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private List<String> obterListaUpdateBancoZW() {
        List<String> listaUpdate = new ArrayList<>();
        try {
            String pwdPactoBR = configuracaoSistemaService.obterValorPorConfiguracao(ConfiguracaoEnum.SENHA_PACTOBR);
            String pwdAdmin = configuracaoSistemaService.obterValorPorConfiguracao(ConfiguracaoEnum.SENHA_ADMIN);
            listaUpdate.add("update usuario set ultimoacesso = null, dataexibirmodalinativarusuers = null, dataalteracaosenha = now();"); //para evitar que ao logar sistema apresente senha expirada
            listaUpdate.add("update usuario set ultimoacesso = null, dataexibirmodalinativarusuers = null, dataalteracaosenha = now(), senha = '" + Uteis.encriptar(pwdAdmin) + "' where username ilike 'admin';");
            listaUpdate.add("update usuario set ultimoacesso = null, dataexibirmodalinativarusuers = null, dataalteracaosenha = now(), senha = '" + Uteis.encriptar(pwdPactoBR) + "' where username ilike 'pactobr';");
            listaUpdate.add("update empresa set gerarCobrancaAutomaticaPacto = false, tipocobrancapacto = "+ TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo() +", creditodcc = 0;");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return listaUpdate;
    }

    private void atualizarOAMDInfra(String nomeBanco, InfoInfraEnum infra, Empresa empresa) throws ServiceException {
        try {
            Uteis.logarDebug("Vou atualizarOAMDInfra | Banco " + nomeBanco + " | Chave " + empresa.getChave());

            String urlConexao = String.format("****************************",
                    empresa.getHostBDSubir() == null ? empresa.getHostBD() : empresa.getHostBDSubir(),
                    empresa.getPortaBDSubir() == null ? empresa.getPorta() : empresa.getPortaBDSubir());

            Uteis.logarDebug("Vou atualizarOAMDInfra | URL " + urlConexao + " | User " + infra.getUserPGOAMD() + " | PWD " + infra.getPwdPGOAMD());
            Dao daoOAMD = new Dao(urlConexao, infra.getUserPGOAMD(), infra.getPwdPGOAMD());
            try {
                List<Empresa> lista = daoOAMD.consultar(
                        Empresa.class, "chave = '" + empresa.getChave() + "'");
                if (lista == null || lista.isEmpty()) {
                    Empresa e = new Empresa();
                    e.setChave(empresa.getChave());
                    e.setNomeBD(nomeBanco);
                    e.setAtiva(Boolean.TRUE);
                    e.setUsarBDLocal(Boolean.TRUE);
                    e.setPorta(empresa.getPorta());
                    e.setPasswordBD(empresa.getPasswordBD());
                    e.setHostBD(empresa.getHostBD());
                    e.setUserBD(empresa.getUserBD());
                    e.setModulos(empresa.getModulos());
                    e.setPermiteMultiEmpresas(empresa.getPermiteMultiEmpresas());
                    e.setUsarBDTreinoLocal(Boolean.TRUE);
                    e.setRoboControle(empresa.getRoboControle());
                    e.setUrlTreino(empresa.getUrlTreino());
                    e.setUrlTreinoWeb(empresa.getUrlTreinoWeb());
                    e.setUrlTreinoMobile(empresa.getUrlTreinoMobile());
                    e.setUrlTreinoIntegracaoZW(empresa.getUrlTreinoIntegracaoZW());
                    e.setUrlIntegracaoWS(empresa.getUrlIntegracaoWS());
                    e.setUrlZaw(empresa.getUrlZaw());
                    e.setUrlZawWrite(empresa.getUrlZawWrite());
                    e.setUrlGame(empresa.getUrlGame());
                    e.setUrlLogin(empresa.getUrlLogin());
                    e.setTreinofront(infra.getUrlFrontend());
                    e.setZwfront(infra.getUrlFrontend());
                    e.setInfoInfra(infra);
                    e.setPortaSsh(empresa.getPortaSsh());
                    e.setPwdSsh(empresa.getPwdSsh());
                    e.setUserSsh(empresa.getUserSsh());
                    e.setDataCadastro(empresa.getDataCadastro());
                    e.setUsoInterno(empresa.getUsoInterno());
                    e.setDataCadastro(empresa.getDataCadastro());
                    e.setDataTrial(empresa.getDataTrial());
                    e.setMsAutenticacao(infra.getUrlMsAutenticacao());
                    e.setIntegracaoSesiCe(empresa.getIntegracaoSesiCe());
                    e.setIntegracaoSesiSc(empresa.getIntegracaoSesiSc());
                    daoOAMD.inserir(e);
                    //se cadastrou corretamente, gerar o scripts dos serviços e backup
                    if (e.getChave() != null && !e.getChave().trim().isEmpty()) {
                        updateServices(empresa, infra);
                        updateScriptsBackupUmaEmpresa(empresa);
                    }
                } else {
                    Empresa e = lista.get(0);
                    if (e.getChave() != null && !e.getChave().trim().isEmpty()) {
                        updateServices(empresa, infra);
                        updateScriptsBackupUmaEmpresa(empresa);
                    }
                }
            } finally {
                daoOAMD.fecharConexao();
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("Erro ao atualizar OAMD infra: " + e.getMessage());
        }
    }

    private void gerarEmpresaTreino(InfoInfraEnum infra, FlagSoftwareEnum flag, Empresa empresa,
                                    SchemaBancoEnum schemaBancoEnum, Usuario usuario) throws Exception {

        Uteis.logarDebug("SUBIR BANCO TREINO");

        Connection con = null;
        try {
            String nomeBanco = flag.getPrefixoBanco() + empresa.getName().toLowerCase();
            con = obterConexaoBancoEmpresa(empresa, infra.getDatabaseDefault());
            Statement stm = con.createStatement();

            boolean bancoExiste = false;
            try {
                String sql = "select datname from pg_database where datname = '" + nomeBanco + "'";
                ResultSet result = stm.executeQuery(sql);

                if (result.next()) {
                    bancoExiste = true;
                }

                if (!bancoExiste) {
                    sql = "CREATE DATABASE " + nomeBanco
                            + " ENCODING '" + CharsetEnum.UTF8.getNomePostgreSQL() + "'"
                            + " TEMPLATE=template0"
                            + " CONNECTION LIMIT = -1;";
                    stm.execute(sql);
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw e;
            }

            if (schemaBancoEnum != null && !schemaBancoEnum.equals(SchemaBancoEnum.PADRAO) && !bancoExiste) {

                alterarZillyonWebSuperUser(nomeBanco, empresa);

                Connection conLanguage = obterConexaoBancoEmpresa(empresa, nomeBanco, "postgres");
                try {
                    stm = conLanguage.createStatement();
                    stm.execute("DROP EXTENSION plpgsql; CREATE EXTENSION unaccent;");
                } catch (Exception e) {
                    Uteis.logarDebug("WARN: " + e.getMessage());
                } finally {
                    conLanguage.close();
                }

                conLanguage = obterConexaoBancoEmpresa(empresa, nomeBanco, "postgres");
                try {
                    stm = conLanguage.createStatement();
                    stm.execute("DROP EXTENSION pg_trgm; CREATE EXTENSION unaccent;");
                } catch (Exception e) {
                    Uteis.logarDebug("WARN: " + e.getMessage());
                } finally {
                    conLanguage.close();
                }

                Connection conCreate = obterConexaoBancoEmpresa(empresa, nomeBanco);
                try {
                    stm = conCreate.createStatement();
                    stm.execute(obterSchema(schemaBancoEnum.getArquivoTR()));
                } catch (Exception e) {
                    Uteis.logarDebug("WARN4: " + e.getMessage());
                } finally {
                    conCreate.close();
                }
            }

            atualizarOAMD2Infra(nomeBanco, infra, empresa);
            sincronizarUsuarioTreinoZW(empresa, usuario, "PACTOBR", 0);

        } catch (Exception ex) {
            Logger.getLogger(EmpresaServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            try {
                if (con != null) {
                    con.rollback();
                }
            } catch (SQLException ex1) {
                Logger.getLogger(EmpresaServiceImpl.class.getName()).log(Level.SEVERE, null, ex1);
                throw ex1;
            }
            throw ex;
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException ex) {
                Logger.getLogger(EmpresaServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private void atualizarOAMD2Infra(String nomeBanco, InfoInfraEnum infra, Empresa empresa) {
        try {
            Dao daoOAMD2 = new Dao(String.format("*****************************",
                    new Object[]{empresa.getHostBDSubir() == null ? empresa.getHostBD() : empresa.getHostBDSubir(),
                            empresa.getPortaBDSubir()}),
                    infra.getUserPGOAMD2(), infra.getPwdPGOAMD2());
            try {
                List<EmpresaOAMD2> lista = daoOAMD2.consultar(
                        EmpresaOAMD2.class, "chave = '" + empresa.getChave() + "'");

                if (lista == null || lista.isEmpty()) {
                    EmpresaOAMD2 eOAMD2 = new EmpresaOAMD2();
                    eOAMD2.setChave(empresa.getChave());
                    eOAMD2.setUrlZillyonWeb(empresa.getRoboControle());
                    eOAMD2.setUrlZillyonWebInteg(empresa.getRoboControleCorrigindoProtocolo());
                    eOAMD2.setNomeBD(nomeBanco);
                    eOAMD2.setAtiva(true);
                    eOAMD2.setHostBD(infra.getHostSGBD_TR());
                    eOAMD2.setPorta(Integer.valueOf(infra.getPortaPG()));
                    eOAMD2.setUserBD(infra.getUserPGSistema());
                    eOAMD2.setPasswordBD(infra.getPwdPGSistema());
                    eOAMD2.setUsarBDLocal(true);
                    if (!empresa.getModulos().contains("TR")) {
                        eOAMD2.setModulos(empresa.getModulos() + ",TR");
                    } else {
                        eOAMD2.setModulos(empresa.getModulos());
                    }
                    daoOAMD2.inserir(eOAMD2);
                    if (eOAMD2.getChave() != null && !eOAMD2.getChave().trim().isEmpty()) {
                        executeScripts(infra, empresa);
                    }
                } else {
                    EmpresaOAMD2 eOAMD2 = lista.get(0);
                    if (eOAMD2.getChave() != null && !eOAMD2.getChave().trim().isEmpty()) {
                        executeScripts(infra, empresa);
                    }
                }
                ExecuteRequestHttpService.executeRequestGET(empresa.getInfoInfra().getUrlTreinoWebDefault() + "/prest/config/reload?key=" + empresa.getChave());
                if (!empresa.getModulos().contains("TR")) {
                    empresa.setModulos(empresa.getModulos() + ",TR");
                    alterar(empresa);
                }
                ExecuteRequestHttpService.executeRequest(empresa.getInfoInfra().getUrlTreinoWebDefault() + "/prest/config/" + empresa.getChave() + "/force-ddl", null);
                ExecuteRequestHttpService.executeRequest(empresa.getInfoInfra().getUrlTreinoWebDefault() + "/prest/config/" + empresa.getChave() + "/updateBD", null);
            } finally {
                daoOAMD2.fecharConexao();
            }
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(EmpresaServiceImpl.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    private void executeScripts(InfoInfraEnum infra, Empresa empresa) {
        Thread thread;
        thread = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    updateScriptsBackupUmaEmpresa(empresa);
                    updateServices(empresa, infra);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        thread.start();
    }

    private void gerarEmpresaFacilite(InfoInfraEnum infra, FlagSoftwareEnum flag, Empresa empresa) throws Exception {
        Connection con = null;
        try {
            String nomeBanco = flag.getPrefixoBanco() + empresa.getName().toLowerCase();
            con = obterConexaoBancoEmpresa(empresa, infra.getDatabaseDefault());
            try {
                Statement stm = con.createStatement();
                String sql = "CREATE DATABASE " + nomeBanco
                        + " ENCODING '" + CharsetEnum.UTF8.getNomePostgreSQL() + "'"
                        + " TEMPLATE=template0"
                        + " CONNECTION LIMIT = -1;";
                stm.execute(sql);
            } catch (Exception e) {
                viewUtils.mensErro(e.getMessage());
            }

            try {
                Dao daoOAMD2 = new Dao(String.format("****************************3",
                        new Object[]{empresa.getHostBDSubir(), empresa.getPortaBDSubir()}),
                        infra.getUserPGOAMD3(), infra.getPwdPGOAMD3());
                try {
                    List<EmpresaOAMD2> lista = daoOAMD2.consultar(
                            EmpresaOAMD2.class, "chave = '" + empresa.getChave() + "'");
                    if (lista == null || lista.isEmpty()) {
                        EmpresaOAMD2 eOAMD2 = new EmpresaOAMD2();
                        eOAMD2.setChave(empresa.getChave());
                        eOAMD2.setUrlZillyonWeb(empresa.getRoboControle());
                        eOAMD2.setUrlZillyonWebInteg(empresa.getRoboControleCorrigindoProtocolo());
                        eOAMD2.setNomeBD(nomeBanco);
                        eOAMD2.setHostBD(infra.getHostSGBD_FC());
                        eOAMD2.setUserBD(infra.getUserPGSistema());
                        eOAMD2.setPasswordBD(infra.getPwdPGSistema());
                        daoOAMD2.inserir(eOAMD2);
                        if (empresa.getChave() != null && !empresa.getChave().trim().isEmpty()) {
                            geradorScript.gerarScriptBackup(infra, empresa, flag, true);
                        }
                    }
                    if (!UteisValidacao.emptyString(empresa.getUrlFacilite())) {
                        ExecuteRequestHttpService.executeRequestGET(empresa.getUrlFacilite() + "/prest/config/reload");
                    }
                    if (!empresa.getModulos().contains("FC")) {
                        empresa.setModulos(empresa.getModulos() + ",FC");
                        alterar(empresa);
                    }
                    viewUtils.mensInfo("Efetue Login no " + flag.getDescricao() + " para que os dados iniciais sejam inseridos.");
                } finally {
                    daoOAMD2.fecharConexao();
                }
            } catch (Exception e) {
                viewUtils.mensErro("Erro ao ativar Empresa no Facilite: " + e.getMessage());
            }

        } catch (Exception ex) {
            Logger.getLogger(EmpresaServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            try {
                if (con != null) {
                    con.rollback();
                }
            } catch (SQLException ex1) {
                Logger.getLogger(EmpresaServiceImpl.class.getName()).log(Level.SEVERE, null, ex1);
                throw ex1;
            }
            throw ex;
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException ex) {
                Logger.getLogger(EmpresaServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    public void updateServices(Empresa empresa, InfoInfraEnum infra) throws Exception {
        List<Empresa> listaEmp = obterTodos(true);
        List<Empresa> listaTemp = new ArrayList<Empresa>();
        String roboControle = null;
        String host = "";
        if (infra == InfoInfraEnum.OUTRO  && empresa != null && !empresa.getRoboControle().isEmpty()) {
            roboControle = empresa.getRoboControle();
            URL u = new URL(roboControle);
            host = u.getHost();
        }
        for (Empresa e : listaEmp) {
            if (e.getInfoInfra() == null) {
                Uteis.logar("Empresa " + e.getName().toUpperCase()
                        + " não possui campo InfoInfra preenchido! Por favor, corrija e tente novamente.");
                continue;
            }
            if (!e.getInfoInfra().equals(InfoInfraEnum.OUTRO)) {
                if (e.getInfoInfra().equals(infra)) {
                    listaTemp.add(e);
                }
            } else if (e.getInfoInfra().equals(InfoInfraEnum.OUTRO)
                    && roboControle != null && !roboControle.isEmpty()
                    && host != null && !host.isEmpty()) {
                if (e.getRoboControle() != null && e.getRoboControle().contains(host)) {
                    listaTemp.add(e);
                }
            }
        }
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "robot-worker", GeradorScripts.ServicoEnum.ROBOT);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "acesso-offline-worker", GeradorScripts.ServicoEnum.ACESSO_OFFLINE);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "remessa-service-worker", GeradorScripts.ServicoEnum.REMESSA);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "extrato-service-worker", GeradorScripts.ServicoEnum.EXTRATO);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "adm-service-worker", GeradorScripts.ServicoEnum.ADMRUNNER);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "dw-service-worker", GeradorScripts.ServicoEnum.DWRUNNER);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "atualizabd-worker", GeradorScripts.ServicoEnum.ATUALIZABD);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "dadosgerenciais-worker", GeradorScripts.ServicoEnum.DADOSGERENCIAIS);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "scheduling-worker1", GeradorScripts.ServicoEnum.TW_SCHEDULING_1);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "scheduling-worker2", GeradorScripts.ServicoEnum.TW_SCHEDULING_2);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "scheduling-worker3", GeradorScripts.ServicoEnum.TW_SCHEDULING_3);
        geradorScript.gerarScriptService(empresa, infra, listaTemp, "pix-service-worker", GeradorScripts.ServicoEnum.PIX);
    }

    @Override
    public void updateScriptsBackupPorInfra(InfoInfraEnum infra) throws ServiceException {
        try {
            List<Empresa> listaTemp = obterTodos(true, null, false, null, infra, true, null);
            if (listaTemp != null) {
                geradorScript.gerarScriptsBackup(infra, listaTemp);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public void updateScriptsBackupUmaEmpresa(Empresa emp) throws ServiceException {
        try {
            geradorScript.gerarScriptBackupUmaEmpresa(emp);
            updateWorkersScriptsBackup(emp.getInfoInfra());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    private void gerarWorkersShell(List<Empresa> listaEmpresasPorInfra, InfoInfraEnum infra) throws ServiceException {
        List<Empresa> listaEmpresasInfraPacto = new ArrayList<>(listaEmpresasPorInfra);
        List<Empresa> listaEmpresasInfraOutro = new ArrayList<>(listaEmpresasPorInfra);
        ColecaoUtils.filter(listaEmpresasInfraPacto, o -> ((Empresa) o).getInfoInfra() != null && !((Empresa) o).getInfoInfra().equals(InfoInfraEnum.OUTRO));
        if (!listaEmpresasInfraPacto.isEmpty()) {
            listaEmpresasInfraPacto = Ordenacao.ordenarLista(listaEmpresasInfraPacto, "nomeBD");
            updateScriptsBackupPorInfra(infra);
            geradorScript.gerarScriptsBackupWorkers(infra,
                    listaEmpresasInfraPacto, "backup-worker-pacto", GeradorScripts.ServicoEnum.BACKUPS);
        }
        ColecaoUtils.filter(listaEmpresasInfraOutro, o -> ((Empresa) o).getInfoInfra() != null && ((Empresa) o).getInfoInfra().equals(InfoInfraEnum.OUTRO));
        if (!listaEmpresasInfraOutro.isEmpty()) {
            listaEmpresasInfraOutro = Ordenacao.ordenarLista(listaEmpresasInfraOutro, "nomeBD");
            geradorScript.gerarScriptsBackupWorkers(InfoInfraEnum.OUTRO,
                    listaEmpresasInfraOutro, "backup-worker-ext", GeradorScripts.ServicoEnum.BACKUPS_EXTERNOS);
        }
    }

    @Override
    public void updateWorkersScriptsBackup(InfoInfraEnum infra) throws ServiceException {
        try {
            List<InfoInfraEnum> infras = new ArrayList<>();
            List<Empresa> listaTempPorInfra = new ArrayList<>();

            switch (infra.getDatacenter()) {
                case AWS:
                    infras.addAll(InfoInfraEnum.getListaAtivasByDatacenter(DatacenterEnum.AWS));
                    break;
                case LOCAWEB:
                    infras.addAll(InfoInfraEnum.getListaAtivasByDatacenter(DatacenterEnum.LOCAWEB));
                    break;
                default:
                    infras.add(infra);
            }

            for (InfoInfraEnum inf : infras) {
                listaTempPorInfra.addAll(obterTodos(true, null,
                        false, null, inf, true, null));
            }

            gerarWorkersShell(listaTempPorInfra, infra);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
    }

    @Override
    public String lerArquivo(InfoInfraEnum infra, Empresa empresa,
                             final String pathFileRemote) throws ServiceException {
        try {
            return geradorScript.readFile(infra, empresa, pathFileRemote);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public String executarCommando(InfoInfraEnum infra, Empresa empresa,
                                   List<String> commandos) throws ServiceException {
        try {
            StringBuffer sb = geradorScript.executarComando(empresa, infra, commandos);
            return sb != null ? sb.toString() : "";
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void gravarArquivo(InfoInfraEnum infra, Empresa empresa,
                              final String contentFile, final String pathFileRemote) throws ServiceException {
        try {
            if (contentFile != null && !contentFile.trim().isEmpty()) {
                geradorScript.saveFile(infra, empresa, contentFile, pathFileRemote);
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void prepararTags(Empresa object, List<String> tags) throws ServiceException {
        try {
            object.setTags(obterTagsEmpresa(object));
            for (TagEmpresa te : object.getTags()) {
                tagEmpresaDao.delete(te);
            }
            object.setTags(new ArrayList<TagEmpresa>());
            empresaDao.update(object);
            for (String nomeTag : tags) {
                object.getTags().add(tagEmpresaDao.insert(new TagEmpresa(obterTag(nomeTag), object)));
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public Tag obterTag(String nomeTag) throws ServiceException {
        try {
            Tag tag = tagDao.findObjectByAttribute("tag", nomeTag);
            if (tag == null) {
                return tagDao.insert(new Tag(nomeTag));
            }
            return tag;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<String> consultarTags(String nome) throws ServiceException {
        try {
            return tagDao.listOfObjects("SELECT tag FROM tag WHERE LOWER(tag) like '%" + nome.toLowerCase() + "%' ORDER BY tag");
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<String> setarTags(Empresa object) throws ServiceException {
        try {
            object.setTagsSelecionadas(new ArrayList<String>());
            object.setTags(obterTagsEmpresa(object));
            for (TagEmpresa te : object.getTags()) {
                if (!object.getTagsSelecionadas().contains(te.getTag().getTag())) {
                    object.getTagsSelecionadas().add(te.getTag().getTag());
                }
            }
            return object.getTagsSelecionadas();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public List<TagEmpresa> obterTagsEmpresa(Empresa object) throws ServiceException {
        try {
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("empresa", object.getChave());
            return tagEmpresaDao.findByParam("SELECT obj FROM TagEmpresa obj WHERE obj.empresa.chave = :empresa", p);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void povoarEmailsUsuarios() {
        try {
            List<Empresa> empresas = obterTodos(true);
            for (Empresa e : empresas) {
                try {
                    String json = ExecuteRequestHttpService.executeRequest(e.getUrlTreino() + "/prest/config/" + e.getChave() + "/obterEmailUsuarios",
                            new HashMap<String, String>());
                    JSONObject jsonObj = new JSONObject(json);
                    String emails = jsonObj.getString("return").replaceAll("\"", "").replaceAll("\\[", "").replaceAll("\\]", "");
                    String[] arrayEmail = emails.split("\\,");
                    for (String email : arrayEmail) {
                        UsuarioApp user = uService.obterPorEmail(email);
                        if (user == null || user.getEmail() == null || user.getEmail().isEmpty()) {
                            user = new UsuarioApp();
                            user.setEmail(email);
                            user.setEmpresa(e);
                            uService.inserir(user);
                            System.out.println(email + " - " + e.getChave());
                        }
                    }
                } catch (Exception es) {
                    System.out.println(e.getName() + " erro: " + es.getMessage());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateScriptsInfraServicos(Empresa empresa, InfoInfraEnum infra, List<Empresa> listaEmpresas) throws Exception {
        for (Empresa e : listaEmpresas) {
            List<Empresa> listaTemp = new ArrayList<Empresa>();
            listaTemp.add(e);
            geradorScript.gerarScriptService(empresa, e.getInfoInfra(), listaTemp, "verificador-worker", GeradorScripts.ServicoEnum.VERIFICADOR);
        }
    }

    private String pathScriptBackup(Empresa e, FlagSoftwareEnum flag) throws ServiceException {
        final String nomeCliente = Uteis.firstLetterUpper(e.getName().toLowerCase());
        boolean externo = InfoInfraEnum.OUTRO == e.getInfoInfra();
        if (flag == FlagSoftwareEnum.TR || flag == FlagSoftwareEnum.ZW) {
            return externo ?
                    e.getInfoInfra().getDirScriptsBackup() + "/_backup" + flag.getPrefixoBackup() + nomeCliente + "_externo.sh" :
                    e.getInfoInfra().getDirScriptsBackup() + "/backup" + flag.getPrefixoBackup() + nomeCliente + ".sh";
        }
        throw new ServiceException("Nenhum Flag de Software válido foi informado (ZW/TR");
    }

    @Override
    public List<String> executarBackups(Empresa e) throws ServiceException {
        try {
            List<String> resultado = new ArrayList<>();
            if (e.getModulos().contains("ZW")) {
                int resultZW = geradorScript.executarScriptBackup(e, e.getInfoInfra(),
                        pathScriptBackup(e, FlagSoftwareEnum.ZW));
                resultado.add("Backup ZW => " + (resultZW == 0 ? "Sucesso" : "Falhou"));
            }
            if (e.getModulos().contains("TR")) {
                int resultTR = geradorScript.executarScriptBackup(e, e.getInfoInfra(),
                        pathScriptBackup(e, FlagSoftwareEnum.TR));
                resultado.add("Backup TR => " + (resultTR == 0 ? "Sucesso" : "Falhou"));
            }
            return resultado;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<String> removerBancos(Empresa e, final Usuario u) throws ServiceException {
        try {
            List<String> resultado = new ArrayList<>();
            if (e.getModulos().contains("ZW")) {
                try {
                    oamdService.desativarBancoRenomeando(e, FlagSoftwareEnum.ZW, u);
                    resultado.add("Desativar Banco ZW => Sucesso");

                } catch (Exception ex) {
                    resultado.add("Desativar Banco ZW => Falhou: " + ex.getMessage());
                }
                try {
                    oamdService.removerEmpresaOAMD(e, FlagSoftwareEnum.ZW, false, u);
                    resultado.add("Desativar Chave OAMD ZW => Sucesso");
                } catch (Exception ex) {
                    resultado.add("Desativar Chave OAMD ZW => Falhou: " + ex.getMessage());
                }
            }
            if (e.getModulos().contains("TR")) {
                try {
                    oamdService.desativarBancoRenomeando(e, FlagSoftwareEnum.TR, u);
                    resultado.add("Desativar Banco TR => Sucesso");
                } catch (Exception ex) {
                    resultado.add("Desativar Banco TR => Falhou: " + ex.getMessage());
                }
                try {
                    oamdService.removerEmpresaOAMD(e, FlagSoftwareEnum.TR, false, u);
                    resultado.add("Desativar Chave OAMD TR => Sucesso");
                } catch (Exception ex) {
                    resultado.add("Desativar Chave OAMD TR => Falhou: " + ex.getMessage());
                }
            }
            try {
                oamdService.removerEmpresaOAMDPrincipal(e, u);
                resultado.add("Desabilitar Chave OAMD PRINCIPAL => Sucesso");
            } catch (Exception ex) {
                resultado.add("Desabilitar Chave OAMD PRINCIPAL => Falhou: " + ex.getMessage());
            }
            return resultado;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void finalizarRemocaoBancos(Empresa e, final Usuario u, List<String> resultados) throws ServiceException {
        oamdService.adicionarLogRemocaoEmpresaOAMDPrincipal(e, u, resultados);
    }

    @Override
    public String buscarIdentificadorAcesso(String chave) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("key", chave);
        List<IdentificadorEmpresaAcesso> identificadores = idDao.findByParam("select obj from IdentificadorEmpresaAcesso obj where obj.empresa.chave = :key", params);
        if (identificadores.isEmpty()) {
            throw new Exception("Não existe um identificador para essa chave");
        } else {
            return identificadores.get(0).getIdentificador();
        }
    }

    @Override
    public void gravarIdentificadorAcesso(final String chave, final String identificador) throws Exception {
        if (identificador == null || identificador.isEmpty()) {
            throw new Exception("Identificador não pode ser vazio");
        }
        Empresa empresa = obterPorId(chave);
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("key", chave);
        List<IdentificadorEmpresaAcesso> identificadores = idDao.findByParam("select obj from IdentificadorEmpresaAcesso obj where obj.empresa.chave = :key", params);
        if (identificadores.isEmpty()) {
            IdentificadorEmpresaAcesso id = new IdentificadorEmpresaAcesso();
            id.setEmpresa(empresa);
            id.setIdentificador(identificador);
            idDao.insert(id);
        } else {
            throw new Exception("Já existe um identificador para essa chave");
        }
    }

    @Override
    public Map<String, Empresa> obterMapaEmpresas() throws ServiceException {
        Map<String, Empresa> mapaParamConexoes = new HashMap<String, Empresa>();
        List<Empresa> empresaOAMDJSONs = obterTodos(false, true);
        for (Empresa emp : empresaOAMDJSONs) {
            mapaParamConexoes.put(emp.getChave(), emp);
        }
        return mapaParamConexoes;
    }

    @Override
    public List<InfoInfraEnum> obterTotaisEmpresasPorInfra() throws Exception {
        return empresaDao.obterTotaisEmpresasPorInfra();
    }

    @Override
    public Boolean migrarEmpresasParaMoviDesk(final List<Empresa> empresas) throws Exception {
        boolean resultado = true;
        final List<String> chavesEmpresasAtualizadas = new ArrayList<String>();

        for (final Empresa empresa : empresas) {
            if (!empresa.getAtiva() || empresa.getUtilizarMoviDesk()) {
                // se a empresa nao eh ativa ou movidesk ja foi habilitado para a empresa entao nao precisamos atualizar nada
                continue;
            }

            final String retorno = AdmWSConsumer.utilizarMoviDesk(empresa, true);
            if (StringUtils.isBlank(retorno) || !retorno.contains("sucesso")) {
                resultado = false;
                continue;
            }

            chavesEmpresasAtualizadas.add(empresa.getChave());
        }

        getEmpresaDao().executeNativeSQL(new StringBuilder("UPDATE ").append(Empresa.class.getSimpleName())
                .append(" SET utilizar_movi_desk = TRUE")
                .append(" WHERE chave IN ('").append(StringUtils.join(chavesEmpresasAtualizadas, "','")).append("')")
                .toString());

        return resultado;
    }

    @Override
    public Boolean habilitarChatMoviDesk(final List<Empresa> empresas) throws Exception {

        boolean resultado = true;
        final List<String> chavesEmpresasAtualizadas = new ArrayList<String>();

        for (final Empresa empresa : empresas) {
            if (empresa.getUtilizarChatMoviDesk()) {
                // se a empresa nao eh ativa ou o chat do movidesk ja foi habilitado para a empresa entao nao precisamos atualizar nada
                continue;
            }

            final String retorno = AdmWSConsumer.utilizarChatMoviDesk(empresa, true);
            if (StringUtils.isBlank(retorno) || !retorno.contains("sucesso")) {
                resultado = false;
                continue;
            }

            chavesEmpresasAtualizadas.add(empresa.getChave());
        }

        getEmpresaDao().executeNativeSQL(new StringBuilder("UPDATE ").append(Empresa.class.getSimpleName())
                .append(" SET utilizar_chat_movi_desk = TRUE")
                .append(" WHERE chave IN ('").append(StringUtils.join(chavesEmpresasAtualizadas, "','")).append("')")
                .toString());

        return resultado;
    }

    @Override
    public Boolean migrarEmpresasParaMoviDeskSilenciosamente(final List<Empresa> empresas) {
        boolean resultado = true;
        final List<String> chavesEmpresasAtualizadas = new ArrayList<String>();

        for (final Empresa empresa : empresas) {
            try {
                if (!empresa.getAtiva() || empresa.getUtilizarMoviDesk()) {
                    // se a empresa nao eh ativa ou movidesk ja foi habilitado para a empresa entao nao precisamos atualizar nada
                    continue;
                }

                final String retorno = AdmWSConsumer.utilizarMoviDesk(empresa, true);
                if (StringUtils.isBlank(retorno) || !retorno.contains("sucesso")) {
                    resultado = false;
                    continue;
                }

                chavesEmpresasAtualizadas.add(empresa.getChave());
            } catch (Exception e) {
                Uteis.logar(e, EmpresaServiceImpl.class);
                resultado = false;
            }
        }

        try {
            getEmpresaDao().executeNativeSQL(new StringBuilder("UPDATE ").append(Empresa.class.getSimpleName())
                    .append(" SET utilizar_movi_desk = TRUE")
                    .append(" WHERE chave IN ('").append(StringUtils.join(chavesEmpresasAtualizadas, "','")).append("')")
                    .toString());
        } catch (Exception e) {
            Uteis.logar(e, EmpresaServiceImpl.class);
        }

        return resultado;
    }

    @Override
    public Boolean habilitarChatMoviDeskSilenciosamente(final List<Empresa> empresas) {

        boolean resultado = true;
        final List<String> chavesEmpresasAtualizadas = new ArrayList<String>();

        for (final Empresa empresa : empresas) {
            try {
                if (empresa.getUtilizarChatMoviDesk()) {
                    // se a empresa nao eh ativa ou o chat do movidesk ja foi habilitado para a empresa entao nao precisamos atualizar nada
                    continue;
                }

                final String retorno = AdmWSConsumer.utilizarChatMoviDesk(empresa, true);
                if (StringUtils.isBlank(retorno) || !retorno.contains("sucesso")) {
                    resultado = false;
                    continue;
                }

                chavesEmpresasAtualizadas.add(empresa.getChave());
            } catch (Exception e) {
                Uteis.logar(e, EmpresaServiceImpl.class);
                resultado = false;
            }
        }

        try {
            getEmpresaDao().executeNativeSQL(new StringBuilder("UPDATE ").append(Empresa.class.getSimpleName())
                    .append(" SET utilizar_chat_movi_desk = TRUE")
                    .append(" WHERE chave IN ('").append(StringUtils.join(chavesEmpresasAtualizadas, "','")).append("')")
                    .toString());
        } catch (Exception e) {
            Uteis.logar(e, EmpresaServiceImpl.class);
        }

        return resultado;
    }

    @Override
    public List<ItemEmpresaMovideskJSON> consultarListaMovidesk(final String token) throws Exception {
        List<ItemEmpresaMovideskJSON> lista = new ArrayList<>();
        Map<String, Empresa> mapaParamConexoes = obterMapaEmpresas();
        final List<EmpresaFinanceiro> listaEmpresaFinanceiro = empresaFinanceiroService.consultarTodas();

        for (Empresa empresa : mapaParamConexoes.values()) {
            if (empresa.getNomeBD().contains("bdfacili")) {
                continue;
            }
            List<ItemEmpresaMovideskJSON> itensMovidesk = (List<ItemEmpresaMovideskJSON>) updateServletService.selectOne(empresa,
                    "SELECT \n" +
                            "\temp.codigo, \n" +
                            "\tnome, \n" +
                            "\tcnpj, \n" +
                            "\temp.email, \n" +
                            "\temp.email_movidesk, \n" +
                            "\test.sigla as uf, \n" +
                            "\temp.razaosocial,\n" +
                            "\tcol.codigo as cod_colaborador,\n" +
                            "\tecol.email\n" +
                            "FROM empresa emp\n" +
                            "\tLEFT JOIN estado est ON emp.estado = est.codigo\n" +
                            "\tLEFT JOIN colaborador col ON col.empresa = emp.codigo AND col.situacao = 'AT'\n" +
                            "\tLEFT JOIN email ecol ON col.pessoa = ecol.pessoa\n" +
                            "WHERE ativa is true", json -> {
                        try {
                            JSONArray arrayResult = new JSONArray(json);
                            List<ItemEmpresaMovideskJSON> itens = new ArrayList<>();
                            for (int i = 0; i < arrayResult.length(); i++) {
                                JSONObject object = arrayResult.getJSONObject(i);
                                ItemEmpresaMovideskJSON item = new ItemEmpresaMovideskJSON();
                                item.setChave(empresa.getChave());
                                item.setNome(object.getString("nome"));
                                item.setCodEmpresa(object.getInt("codigo"));
                                item.setCnpj(object.getString("cnpj"));
                                item.setEstado(object.getString("uf"));
                                item.setRazaoSocial(object.getString("razaosocial"));
                                String email = object.getString("email");
                                String moviDesk = object.getString("email");
                                item.setEmail(!UteisValidacao.emptyString(moviDesk) ? moviDesk : email);
                                item.setCodigoMovidesk(empresa.getGrupoChatMoviDesk());
                                int codFinanceiro = 0;
                                for (EmpresaFinanceiro ef : listaEmpresaFinanceiro) {
                                    if (ef.getChaveZw() != null && ef.getChaveZw().equals(empresa.getChave()) && ef.getEmpresazw() != null && ef.getEmpresazw().equals(item.getCodEmpresa())) {
                                        codFinanceiro = ef.getCodigoFinanceiro();
                                        break;
                                    }
                                }
                                item.setCodFinanceiro(codFinanceiro);
                                item.setCodColaborador(object.getInt("cod_colaborador"));
                                item.setCodColaboradorMovidesk(item.getChave() + "-" + item.getCodEmpresa() + "-" + item.getCodColaborador());
                                itens.add(item);
                            }
                            return itens;
                        } catch (Exception ex) {
                            return new ArrayList<>();
                        }
                    }, token);

            lista.addAll(itensMovidesk);
        }

        return lista;
    }

    public List<Empresa> obterEmpresas(boolean incluirEmpresasPacto, boolean somenteAtivas, String chave,
                                       Integer max, Integer index, String orderBY, InfoInfraEnum infoInfraEnum) throws ServiceException {
        try {
            StringBuilder sql = obterSQLEmpresas(incluirEmpresasPacto, somenteAtivas, chave, false, orderBY, infoInfraEnum);
            return obterPorParam(sql.toString(), new HashMap<>(), max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Empresa> pesquisarEmpresas(String chaveOuNomeEmpresa, Integer limite) throws ServiceException {
        try {
            String sql = "SELECT e FROM Empresa e " +
                    "WHERE e.chave like '%" + chaveOuNomeEmpresa + "%' " +
                    "OR e.nomeBD like '%" + chaveOuNomeEmpresa + "%' ";

            return obterPorParam(sql, new HashMap<>(), limite, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Integer obterEmpresasTotal(boolean incluirEmpresasPacto, boolean somenteAtivas,
                                      String chave, InfoInfraEnum infoInfraEnum) throws ServiceException {
        try {
            StringBuilder sql = obterSQLEmpresas(incluirEmpresasPacto, somenteAtivas, chave, true, "", infoInfraEnum);
            return getEmpresaDao().count(sql.toString());
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private StringBuilder obterSQLEmpresas(boolean incluirEmpresasPacto, boolean somenteAtivas,
                                           String chave, boolean count, String orderBY, InfoInfraEnum infoInfraEnum) {
        StringBuilder sql = new StringBuilder();
        if (count) {
            sql.append("SELECT COUNT(obj) ");
        } else {
            sql.append("SELECT obj ");
        }

        sql.append(" FROM Empresa obj ");
        sql.append(somenteAtivas ? " where obj.ativa = true" : " where 1 = 1 ");
        if (!incluirEmpresasPacto) {
            sql.append(" AND obj.usoInterno = false ");
            sql.append(" AND (nomeBD not in ('bdzillyontreinamento', 'bdzillyonpactosp') or nomeBD is null) ");
        }

        if (!UteisValidacao.emptyString(chave)) {
            sql.append(" AND obj.chave = '").append(chave).append("' ");
        }

        if (infoInfraEnum != null) {
            sql.append(" AND obj.infoInfra = ").append(infoInfraEnum.ordinal()).append(" ");
        }

        if (!count && !UteisValidacao.emptyString(orderBY)) {
            sql.append(" ORDER BY obj.").append(orderBY);
        }
        return sql;
    }

    public void atualizarModulosOAMDInfra(Empresa empresa, Usuario u) throws Exception {
        try {
            if (UteisValidacao.emptyString(empresa.getChave()) || u == null) {
                return;
            }

            StringBuilder sql = new StringBuilder();
            sql.append(String.format("update empresa set modulos = '%s' where chave = '%s';", empresa.getModulos(), empresa.getChave()));
            if (empresa.getModulos().contains("NTR")) { //novo treino
                sql.append(String.format("update empresa set treinofront = '%s' where chave = '%s';", empresa.getTreinofront(), empresa.getChave()));
            }

            executeUpdateOAMDInfra(empresa, u, sql.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Não foi possível atualizar módulos OAMD Infra: " + ex.getMessage());
        }
    }

    public void atualizarDadosEmpresaOAMDInfra(Empresa empresa, Usuario u) throws Exception {
        try {
            if (UteisValidacao.emptyString(empresa.getChave()) || u == null) {
                return;
            }

            StringBuilder sql = new StringBuilder();
            sql.append(String.format("update empresa set modulos = '%s'", empresa.getModulos()));
            if (empresa.getModulos().contains("NTR")) { //novo treino
                sql.append(String.format(", treinofront = '%s'", empresa.getTreinofront()));
            }
            sql.append(String.format(", integracaosesice = %s", empresa.getIntegracaoSesiCe()));
            sql.append(String.format(", integracaosesisc = %s", empresa.getIntegracaoSesiSc()));
            sql.append(String.format(" where chave = '%s';", empresa.getChave()));

            executeUpdateOAMDInfra(empresa, u, sql.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Não foi possível atualizar dados empresa no OAMD Infra: " + ex.getMessage());
        }
    }

    public void atualizarFerramentaAtendimentoEmpresaOAMDInfra(Empresa empresa, Usuario u) throws Exception {
        try {
            if (UteisValidacao.emptyString(empresa.getChave()) || u == null) {
                return;
            }

            StringBuilder sql = new StringBuilder();
            sql.append(String.format("UPDATE empresa SET utilizar_movi_desk = '%s'", empresa.getUtilizarMoviDesk()));
            sql.append(String.format(", utilizar_chat_movi_desk = '%s'", empresa.getUtilizarChatMoviDesk()));
            sql.append(String.format(", grupo_chat_movidesk = '%s'", empresa.getGrupoChatMoviDesk()));
            sql.append(String.format(", utilizar_octadesk = '%s'", empresa.getUtilizarOctadesk()));
            sql.append(String.format(", utilizar_gymbot = '%s' ", empresa.getUtilizarGymbot()));
            sql.append(String.format(" where chave = '%s';", empresa.getChave()));

            executeUpdateOAMDInfra(empresa, u, sql.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Não foi possível atualizar dados empresa no OAMD Infra: " + ex.getMessage());
        }
    }

    public void atualizarEmpresaAtivaOAMDInfra(Empresa empresa, String chave, boolean statusAtivo, Integer oamd) throws Exception {
        try {
            StringBuilder sqlAlterarStatusAtivoEmpresaOamd = new StringBuilder();
            sqlAlterarStatusAtivoEmpresaOamd.append("UPDATE empresa SET ativa = " + statusAtivo + " WHERE chave = '" + chave + "'");
            Usuario usuarioAPI = usuarioService.validarUsuario(Constants.LOGIN_API_OAMD, Constants.SENHA_API_OAMD, false);
            if(oamd == 1) {
                executeUpdateOAMDInfra(empresa, usuarioAPI, sqlAlterarStatusAtivoEmpresaOamd.toString());
            } else if (oamd == 2) {
                executeUpdateOAMD2Infra(empresa, usuarioAPI, sqlAlterarStatusAtivoEmpresaOamd.toString());
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Não foi possível atualizar a coluna ativa da empresa: " + ex.getMessage());
        }
    }

    private void executeUpdateOAMDInfra(Empresa empresa, Usuario u, String sql) throws Exception {
        ExecuteRequestHttpService req = new ExecuteRequestHttpService();
        req.timeout = 5000;

        final String url = String.format("%s/UpdateServlet", empresa.getRoboControleSemHTTPS());

        Map<String, String> p = new HashMap<>();
        p.put("op", "updateONE");
        p.put("hostPG", empresa.getHostBD());
        p.put("portaPG", empresa.getPorta().toString());
        p.put("userPG", empresa.getUserBD());
        p.put("pwdPG", empresa.getPasswordBD());
        p.put("bd", "OAMD");
        p.put("format", "json");
        p.put("sql", sql);
        p.put("lgn", Uteis.obterTokenUsuario(u.getUserName(), u.getPassTrans()));
        final String retorno = req.executeRequestInner(url, p);
        if (!retorno.toLowerCase().contains("comando executado com sucesso")) {
            throw new Exception(retorno);
        }
    }

    private void executeUpdateOAMD2Infra(Empresa empresa, Usuario u, String sql) throws Exception {
        ExecuteRequestHttpService req = new ExecuteRequestHttpService();
        req.timeout = 5000;

        final String url = String.format("%s/UpdateServlet", empresa.getRoboControleSemHTTPS());

        Map<String, String> p = new HashMap<>();
        p.put("op", "updateONE");
        p.put("hostPG", empresa.getHostBD());
        p.put("portaPG", empresa.getPorta().toString());
        p.put("userPG", empresa.getUserBD());
        p.put("pwdPG", empresa.getPasswordBD());
        p.put("bd", "OAMD2");
        p.put("format", "json");
        p.put("sql", sql);
        p.put("lgn", Uteis.obterTokenUsuario(u.getUserName(), u.getPassTrans()));
        final String retorno = req.executeRequestInner(url, p);
        if (!retorno.toLowerCase().contains("comando executado com sucesso")) {
            throw new Exception(retorno);
        }
    }

    private void atualizarBancoZW(Empresa empresa, Usuario u) throws Exception {
        try {
            if (UteisValidacao.emptyString(empresa.getChave())) {
                return;
            }

            ExecuteRequestHttpService req = new ExecuteRequestHttpService();
            req.timeout = 120000;

            final String url = String.format("%s/UpdateServlet", empresa.getRoboControleSemHTTPS());

            Map<String, String> p = new HashMap();
            p.put("op", "updatePGONE");
            p.put("hostPG", empresa.getHostBD());
            p.put("portaPG", empresa.getPorta().toString());
            p.put("userPG", empresa.getUserBD());
            p.put("pwdPG", empresa.getPasswordBD());
            p.put("bd", empresa.getNomeBD());
            p.put("lgn", Uteis.obterTokenUsuario(u.getUserName(), u.getPassTrans()));
            p.put("format", "json");
            System.out.println("EmpresaServiceImpl@atualizarBancoZW: -> Request " + url);
            final String retorno = req.executeRequestInner(url, p);
            System.out.println("EmpresaServiceImpl@atualizarBancoZW: -> Result " + retorno);
            if (!retorno.toLowerCase().contains("executada com sucesso")
                && !retorno.toLowerCase().contains("corrente do banco de dados do sistema")) {
                throw new Exception(retorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Não foi possível atualizar o banco do ZW: " + ex.getMessage());
        }
    }

    public void sincronizarUsuarioTreinoZW(Empresa empresa, Usuario u, String username, Integer codigoUsuario) throws Exception {
        try {
            if (!empresa.getModulos().toUpperCase().contains(Modulo.ZILLYON_WEB.getSiglaModulo()) ||
                    UteisValidacao.emptyString(empresa.getChave())) {
                return;
            }

            ExecuteRequestHttpService req = new ExecuteRequestHttpService();
            req.timeout = 20000;

            final String url = String.format("%s/sincronizarUsuario", empresa.getRoboControleSemHTTPS());

            Map<String, String> p = new HashMap();
            p.put("key", empresa.getChave());
            p.put("username", username);
            p.put("codusuario", codigoUsuario.toString());
            p.put("lgn", Uteis.obterTokenUsuario(u.getUserName(), u.getPassTrans()));
            final String retorno = req.executeRequestInner(url, p);
            if (!retorno.toLowerCase().contains("sincronizado com sucesso")) {
                throw new Exception(retorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Não foi possível sincronizar o usuário PACTOBR: " + ex.getMessage());
        }
    }

    public org.json.JSONObject consultarExisteCNPJFinanceiroPacto(InfoInfraEnum infraEnum, String cnpj) throws Exception {
        if (infraEnum == null) {
            throw new Exception("Informe a infra.");
        }

        if (UteisValidacao.emptyString(Uteis.removerMascara(cnpj))) {
            throw new Exception("Informe o CNPJ.");
        }

        ExecuteRequestHttpService req = new ExecuteRequestHttpService();
        req.timeout = 20000;

        final String url = String.format("%s/prest/canalCliente/consultar-cnpj", infraEnum.getUrlZWDefault());
//        final String url = String.format("%s/prest/canalCliente/consultar-cnpj", "http://localhost:8084/zw");

        Map<String, String> p = new HashMap();
        p.put("cnpj", Uteis.removerMascara(cnpj));
        final String retorno = req.executeRequestInner(url, p);
        return new org.json.JSONObject(retorno);
    }

    public boolean bancoJaExiste(String nome) throws Exception {
        nome = nome.toLowerCase();

        if (nome.contains("bdzillyon") || nome.contains("bdmusc")) {
            nome = nome.replaceAll("bdzillyon", "").replaceAll("bdmusc", "");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select exists ( \n");
        sql.append("select chave from ( \n");
        sql.append("select  \n");
        sql.append("chave, \n");
        sql.append("\"nomeBD\", \n");
        sql.append("replace(replace(\"nomeBD\", 'bdzillyon', ''), 'bdzillyon', '') as nomebanco \n");
        sql.append("from empresa) as sql \n");
        sql.append("where LOWER(sql.nomebanco) = '").append(nome.toLowerCase()).append("') \n");
        ResultSet rs = getEmpresaDao().createStatement(sql.toString());
        if (rs.next()) {
            return rs.getBoolean(1);
        }
        return false;
    }

    public String obterSchema(String arquivo) {
        //        InputStream inZWSchema = this.getClass().getResourceAsStream("/br/com/pacto/util/resources/scripts/ZW_SCHEMA_POVOADOR.sql");
        try {
            InputStream inZWSchema = this.getClass().getResourceAsStream("/br/com/pacto/util/resources/scripts/" + arquivo);
            return Uteis.convertStreamToString(inZWSchema, CharsetEnum.UTF8.getNomeJava());
        } catch (IOException ex) {
            Logger.getLogger(Principal.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public void updateBancoZillyonWeb(Empresa empresa, Usuario u, String sql) throws Exception {
        try {
            if (UteisValidacao.emptyString(empresa.getChave()) || u == null) {
                return;
            }

            ExecuteRequestHttpService req = new ExecuteRequestHttpService();
            req.timeout = 5000;

            final String url = String.format("%s/UpdateServlet", empresa.getRoboControleSemHTTPS());
            Map<String, String> p = new HashMap();
            p.put("op", "updateONE");
            p.put("hostPG", empresa.getHostBD());
            p.put("portaPG", empresa.getPorta().toString());
            p.put("userPG", empresa.getUserBD());
            p.put("pwdPG", empresa.getPasswordBD());
            p.put("bd", empresa.getNomeBD());
            p.put("format", "json");
            p.put("sql", sql);
            p.put("lgn", Uteis.obterTokenUsuario(u.getUserName(), u.getPassTrans()));
            final String retorno = req.executeRequestInner(url, p);
            if (!retorno.toLowerCase().contains("comando executado com sucesso")) {
                throw new Exception(retorno);
            }
        } catch (Exception ex) {
            throw new Exception("Não foi possível atualizar o banco do ZillyonWeb: " + ex.getMessage());
        }
    }

    public void updateBancoTreinoWeb(Empresa empresa, Usuario u, String sql) throws Exception {
        try {
            if (UteisValidacao.emptyString(empresa.getChave()) || u == null) {
                return;
            }

            ExecuteRequestHttpService req = new ExecuteRequestHttpService();
            req.timeout = 5000;

            final String url = String.format("%s/UpdateServlet", empresa.getRoboControleSemHTTPS());
            Map<String, String> p = new HashMap();
            p.put("op", "updateONE");
            p.put("hostPG", empresa.getHostBD());
            p.put("portaPG", empresa.getPorta().toString());
            p.put("userPG", empresa.getUserBD());
            p.put("pwdPG", empresa.getPasswordBD());
            p.put("bd", empresa.getNomeBDTreino());
            p.put("format", "json");
            p.put("sql", sql);
            p.put("lgn", Uteis.obterTokenUsuario(u.getUserName(), u.getPassTrans()));
            final String retorno = req.executeRequestInner(url, p);
            if (!retorno.toLowerCase().contains("comando executado com sucesso")) {
                throw new Exception(retorno);
            }
        } catch (Exception ex) {
            throw new Exception("Não foi possível atualizar o banco do ZillyonWeb: " + ex.getMessage());
        }
    }

    @Override
    public boolean alertaZWAtivo() {
        StringBuilder query = new StringBuilder("select c.codigo, c.ativa, c.nome, c.estado, c.pais, c.linguagem from campanha c ");
        query.append("inner join itemcampanha i on i.campanha_codigo = c.codigo ");
        query.append("where i.link = 'https://sistemapacto.com.br/tela-de-login-aviso-indisponibilidade/' ");
        query.append("and c.ativa = true");
        try {
            ResultSet rs = getEmpresaDao().createStatement(query.toString());
            return rs.next();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public void ativaAlertaZW(boolean ativo) {
        String query = "select codigo from campanha where ativa = true";
        try {
            ResultSet rs = getEmpresaDao().createStatement(query);
            if (rs.next()) {
                if (ativo) {
                    query = "insert into campanhaativa (campanha, dataalteracao) values (" + rs.getInt("codigo") + " , '" + new Date() + "')";
                    getEmpresaDao().executarSqlNativo(query);
                    query = "update campanha set ativa = false";
                    getEmpresaDao().executarSqlNativo(query);
                    query = "update campanha set ativa = true where codigo = (select campanha_codigo from itemcampanha where  link = 'https://sistemapacto.com.br/tela-de-login-aviso-indisponibilidade/' limit 1)";
                    getEmpresaDao().executarSqlNativo(query);
                } else {
                    query = "select campanha from campanhaativa order by dataalteracao desc limit 1";
                    rs = getEmpresaDao().createStatement(query);
                    if (rs.next()) {
                        int codigo = rs.getInt("campanha");
                        query = "update campanha set ativa = false";
                        getEmpresaDao().executarSqlNativo(query);
                        query = "update campanha set ativa = true where codigo = " + codigo;
                        getEmpresaDao().executarSqlNativo(query);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void salvaMensagemEnviada(MensagensEnviadas mensagensEnviadas) {
        StringBuilder query = new StringBuilder("INSERT INTO mensagensenviadas (dataenvio, mensagem, numerochamado, telefone) VALUES('");
        query.append(mensagensEnviadas.getDataEnvio());
        query.append("', '");
        query.append(mensagensEnviadas.getMensagem());
        query.append("', ");
        query.append(mensagensEnviadas.getNumeroChamado());
        query.append(", '");
        query.append(mensagensEnviadas.getTelefone());
        query.append("');");
        try {
            getEmpresaDao().executarSqlNativo(query.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<MensagensEnviadas> buscaMensagensEnviadas(Long numeroChamado) {
        String query = "select codigo, dataenvio, mensagem, numerochamado, telefone from mensagensenviadas where numerochamado = " + numeroChamado
                + " order by codigo";
        try {
            List<MensagensEnviadas> lista = new ArrayList<MensagensEnviadas>();
            ResultSet rs = getEmpresaDao().createStatement(query);
            while (rs.next()) {
                MensagensEnviadas mensagensEnviadas = new MensagensEnviadas();

                mensagensEnviadas.setCodigo(rs.getLong("codigo"));
                mensagensEnviadas.setDataEnvio(rs.getTimestamp("dataenvio"));
                mensagensEnviadas.setMensagem(rs.getString("mensagem"));
                mensagensEnviadas.setNumeroChamado(rs.getLong("numerochamado"));
                mensagensEnviadas.setTelefone(rs.getString("telefone"));

                lista.add(mensagensEnviadas);
            }
            return lista;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public List<String> betaTesters() throws ServiceException {
        try {
            ResultSet rs = getEmpresaDao().createStatement("SELECT obj.chave FROM Empresa obj where obj.betaTester");
            return new ArrayList(){{
                while(rs.next()){
                    add(rs.getString("chave"));
                }
            }};
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public String health(String chave) throws Exception {
        if(getEmpresaDao().findById(chave) == null){
            throw new Exception("Error on find key "+chave);
        }else{
            return "ok";
        }
    }

    @Override
    public EmpresaInfraDTO findInfraByKey(String key) {
        Empresa empresa = empresaDao.findById(key);
        if (empresa == null) {
            return null;
        }
        return new EmpresaInfraDTO(empresa);
    }

    public List<Empresa> consultarEmpresas(String nomeChave, InfoInfraEnum infoInfraEnum, Boolean ativa) throws ServiceException {
        try {
            return empresaDao.consultarEmpresa(nomeChave, infoInfraEnum, ativa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<CompanyJSON> findAllByDatacenter(DatacenterEnum datacenter) throws ServiceException {
        try {
            List<CompanyJSON> companies = new ArrayList<>();
            List<Empresa> empresasByDatacenter = empresaDao.findAllByDatacenter(datacenter);
            for (Empresa empresa : empresasByDatacenter) {
                CompanyJSON companyJSON = new CompanyJSON();
                companyJSON.setChave(empresa.getChave());
                companyJSON.setAtiva(empresa.getAtiva());
                companyJSON.setHostBD(empresa.getHostBD());
                companyJSON.setPorta(empresa.getPorta());
                companyJSON.setNomeBD(empresa.getNomeBD());
                companyJSON.setPasswordBD(empresa.getPasswordBD());
                companyJSON.setUserBD(empresa.getUserBD());
                companyJSON.setIntegracaoSesiCe(empresa.getIntegracaoSesiCe());
                companyJSON.setModulos(empresa.getModulos());
                companies.add(companyJSON);
            }
            return companies;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
