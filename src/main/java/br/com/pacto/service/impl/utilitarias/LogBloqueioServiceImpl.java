package br.com.pacto.service.impl.utilitarias;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.utilitarias.LogBloqueio;
import br.com.pacto.dao.intf.utilitarias.LogBloqueioDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.utilitarias.LogBloqueioService;
import br.com.pacto.util.ViewUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by GlaucoT on 29/06/2016
 */

@Service
@Qualifier(value = "logBloqueioService")
public class LogBloqueioServiceImpl implements LogBloqueioService {

    @Autowired
    private LogBloqueioDao logBloqueioDao;
    @Autowired
    private ViewUtils viewUtils;

    @Override
    public LogBloqueio inserir(LogBloqueio object) throws ServiceException {
        try {
            return getLogBloqueioDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public LogBloqueio inserir(Empresa e, int idEmpresa, Usuario usuario, String acao, Date expiracao) throws ServiceException {
        return inserir(e, idEmpresa, usuario, acao, expiracao, false);
    }

        public LogBloqueio inserir(Empresa e, int idEmpresa, Usuario usuario, String acao, Date expiracao, boolean suspensao) throws ServiceException {
        LogBloqueio logBloqueio = new LogBloqueio();
        logBloqueio.setEmpresa(e);
        logBloqueio.setIdEmpresa(idEmpresa);
        logBloqueio.setDataAlteracao(Calendario.hoje());
        logBloqueio.setResponsavel((usuario == null) ? "WEBSERVICE" : usuario.getUserName());
        logBloqueio.setAcao(acao);
        if(suspensao)
            logBloqueio.setObservacao(e.getNomeEmpresa());
        else
            logBloqueio.setObservacao((expiracao != null) ? "Bloqueio para: " + Uteis.getData(expiracao) : "");
        return inserir(logBloqueio);
    }

    @Override
    public void obterPor(Empresa e) throws Exception {
        String s = "SELECT obj FROM LogBloqueio obj WHERE obj.empresa.chave = :chave ORDER BY obj.dataAlteracao DESC";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("chave", e.getChave());

        e.setLogBloqueios(getLogBloqueioDao().findByParam(s, params));
    }

    public LogBloqueioDao getLogBloqueioDao() {
        return logBloqueioDao;
    }

    public void setLogBloqueioDao(LogBloqueioDao logBloqueioDao) {
        this.logBloqueioDao = logBloqueioDao;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }
}
