/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.game;

import br.com.pacto.bean.game.DadosGame;
import br.com.pacto.dao.intf.game.DadosGameDao;
import br.com.pacto.service.intf.game.DadosGameService;
import java.util.ArrayList;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class DadosGameServiceImpl implements DadosGameService{

    @Autowired
    private DadosGameDao dadosDao;
    
    @Override
    public String gravarDadosGame(DadosGame dado) throws Exception {
        dadosDao.deleteComParam(new String[]{"agrupador"}, new Object[]{dado.getAgrupador()});
        dadosDao.insert(dado);
        return "ok";
    }
    
    @Override
    public JSONArray obterDadosGame() throws Exception {
        List<JSONObject> objs = new ArrayList<JSONObject>();
        List<DadosGame> all = dadosDao.findAll();
        for(DadosGame dado : all){
            JSONObject obj = new JSONObject();
            obj.put("agrupador", dado.getAgrupador());
            obj.put("dados", dado.getDados());
            objs.add(obj);
        }
        return new JSONArray(objs);
    }
    
}
