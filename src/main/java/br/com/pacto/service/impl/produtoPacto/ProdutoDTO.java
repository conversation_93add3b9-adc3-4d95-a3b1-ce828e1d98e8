package br.com.pacto.service.impl.produtoPacto;

import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 04/06/2020
 *
 */
public class ProdutoDTO {

    private Integer codigo;
    private Integer maxDivisao;
    private String descricao;
    private Double valor;


    public ProdutoDTO(){

    }

    public ProdutoDTO(JSONObject json){
        this.codigo = json.optInt("codigo");
        this.maxDivisao = json.optInt("maxDivisao");
        this.descricao = json.optString("descricao");
        this.valor = json.optDouble("valor");
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getMaxDivisao() {
        return maxDivisao;
    }

    public void setMaxDivisao(Integer maxDivisao) {
        this.maxDivisao = maxDivisao;
    }
}
