package br.com.pacto.service.impl.empresa;

import org.json.JSONObject;

public class RetornoSituacaoPersonalGestorDTO {

    private String situacao;
    private String atraso;
    private String dataCancelamento;

    public RetornoSituacaoPersonalGestorDTO(JSONObject retorno) {
        situacao = retorno.getString("situacao");
        atraso = retorno.getString("atraso");
        dataCancelamento = retorno.getString("dataCancelamento");
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getAtraso() {
        return atraso;
    }

    public void setAtraso(String atraso) {
        this.atraso = atraso;
    }

    public String getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(String dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }
}
