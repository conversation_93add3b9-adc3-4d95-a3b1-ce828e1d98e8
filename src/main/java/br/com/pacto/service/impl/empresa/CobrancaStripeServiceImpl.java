package br.com.pacto.service.impl.empresa;

import br.com.pacto.bean.assinaturas.ConfigAssinatura;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.EmpresaSite;
import br.com.pacto.bean.empresa.Paths;
import br.com.pacto.controller.json.dto.CriarTrialNovoTreinoDTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dtos.PersonaDTO;
import br.com.pacto.security.interfaces.RequisicaoServico;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configassinatura.ConfigAssinaturaService;
import br.com.pacto.service.intf.empresa.EmpresaSiteService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class CobrancaStripeServiceImpl {

    @Autowired
    private DiscoveryService discoveryService;
    @Autowired
    private ConfigAssinaturaService configAssinaturaService;
    @Autowired
    private RequisicaoServico requisicaoServico;
    @Autowired
    private EmpresaSiteService empresaSiteService;

    private String urlApi() throws Exception {
        Paths paths = discoveryService.paths();
        return paths.getStripeMsUrl();
    }

    public JSONObject sendToBase(ConfigAssinatura configAssinatura, CriarTrialNovoTreinoDTO trial) throws ServiceException {
        try {
            JSONObject dto = new JSONObject();
            dto.put("price", configAssinatura.getPriceKeyStripe());
            dto.put("nome", trial.getNome());
            dto.put("email", trial.getEmail());
            dto.put("celular", trial.getCelular());
            dto.put("cartao", trial.getNumerocartao());
            dto.put("titular", trial.getNomecartao());
            dto.put("validade", trial.getValidade());
            dto.put("codigoSeguranca", trial.getCvv());
            dto.put("diasTeste", configAssinatura.getDiasTeste());
            String urlApi = urlApi();
            return Uteis.doPost(dto,
                    urlApi + "/assinatura",
                    null,
                    configAssinatura.getApiKeyStripe());
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }

    }

    public void dadosPrice(ConfigAssinatura configAssinatura) throws ServiceException {
        try {
            String urlApi = urlApi();
            JSONObject jsonObject = Uteis.doGet(urlApi + "/assinatura/price?priceKey=" + configAssinatura.getPriceKeyStripe(),
                    null,
                    configAssinatura.getApiKeyStripe());

            configAssinatura.setMoeda(jsonObject.getString("moeda").toUpperCase());
            configAssinatura.setMensalidade(jsonObject.optDouble("mensalidades"));
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }

    }

}
