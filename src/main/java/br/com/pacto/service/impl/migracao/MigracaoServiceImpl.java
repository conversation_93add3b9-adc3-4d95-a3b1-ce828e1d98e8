package br.com.pacto.service.impl.migracao;

import br.com.pacto.bean.migracao.builder.RegistroMigracaoBuilder;
import br.com.pacto.bean.migracao.enums.StatusSolicitacaoMigracao;
import br.com.pacto.bean.migracao.enums.TipoSolicitacaoMigracao;
import br.com.pacto.bean.migracao.json.RegistroMigracaoJSON;
import br.com.pacto.bean.migracao.modelo.ConfiguracaoImportacao;
import br.com.pacto.bean.migracao.modelo.FonteImportacao;
import br.com.pacto.bean.migracao.factory.FonteImportacaoFactory;
import br.com.pacto.bean.migracao.modelo.RegistroMigracao;
import br.com.pacto.bean.migracao.json.RespostaExportacaoRegistroMigracaoJSON;
import br.com.pacto.bean.migracao.enums.StatusRegistroMigracao;
import br.com.pacto.bean.migracao.enums.TipoRegistroMigracao;
import br.com.pacto.bean.migracao.modelo.SolicitacaoMigracao;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.migracao.RegistroMigracaoDao;
import br.com.pacto.objeto.MigracaoUtil;
import br.com.pacto.service.intf.migracao.MigracaoHttpService;
import br.com.pacto.service.intf.migracao.MigracaoService;
import br.com.pacto.service.intf.migracao.SolicitacaoMigracaoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.JSFUtilities;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Descrição: Serviço responsável pela migração de dados
 * Projeto: NewOAMD
 *
 * <AUTHOR> - 12/abr/2018 às 14:29
 * Pacto Soluções - Todos os direitos reservados
 */
@Service
@Transactional(readOnly = true)
public class MigracaoServiceImpl implements MigracaoService {

    @Autowired
    private RegistroMigracaoDao registroMigracaoDao;

    @Autowired
    private SolicitacaoMigracaoService solicitacaoMigracaoService;

    @Autowired
    private MigracaoHttpService migracaoHttpService;
    
    private static final int porcentagemNotificacao = 10;


    @Transactional
    @Override
    @Async
    public void migrarRegistros(SolicitacaoMigracao solicitacaoMigracao) {
        TipoSolicitacaoMigracao tipo = solicitacaoMigracao.getTipo();
        switch (tipo) {

            case IMPORTACAO:
                importarRegistros(solicitacaoMigracao);
                break;
            case EXPORTACAO:
                exportarRegistros(solicitacaoMigracao);
                break;
        }
    }

    private void importarRegistros(SolicitacaoMigracao solicitacaoMigracao) {
        solicitacaoMigracaoService.atualizarStatus(solicitacaoMigracao, StatusSolicitacaoMigracao.EM_EXECUTACAO);

        List<TipoRegistroMigracao> tiposRegistroParaImportacao = Lists.newArrayList(TipoRegistroMigracao.values());
        if (solicitacaoMigracao.getTipoRegistroMigracao() != null) {
            tiposRegistroParaImportacao.clear();
            tiposRegistroParaImportacao.add(solicitacaoMigracao.getTipoRegistroMigracao());
        }
        try {
            for (TipoRegistroMigracao tipo : tiposRegistroParaImportacao) {
                importarRegistros(solicitacaoMigracao, tipo);
            }
        } catch (Exception e) {
            solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, "Erro: " + e.getMessage());
        }
        solicitacaoMigracaoService.finalizar(solicitacaoMigracao);

    }

    private void importarRegistros(SolicitacaoMigracao solicitacaoMigracao, TipoRegistroMigracao tipo) throws Exception {
        String descricaoInicial = "Iniciando importacao de registros do tipo[" + tipo.name() + "]";
        solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, descricaoInicial);

        ConfiguracaoImportacao configuracaoImportacao = solicitacaoMigracao.getConfiguracaoImportacao();
        FonteImportacao fonteImportacao = FonteImportacaoFactory.construir(configuracaoImportacao);
        List<? extends RegistroMigracaoJSON> registrosJSON = Lists.newArrayList();
        switch (tipo){
            case ACESSO:
                registrosJSON = fonteImportacao.importarRegistrosAcessos();
                break;
            case ATESTADO:
                registrosJSON = fonteImportacao.importarRegistrosAtestados();
                break;
            case CLIENTE:
                registrosJSON = fonteImportacao.importarRegistrosClientes();
                break;
            case FERIAS:
                registrosJSON = fonteImportacao.importarRegistrosFerias();
                break;
            case PAGAMENTOS:
                registrosJSON = fonteImportacao.importarRegistrosFormasPagamento();
                break;
            case VENDAAVULSA:
                registrosJSON = fonteImportacao.importarRegistrosMovimentacoes();
                break;
            case CONTRATO:
                registrosJSON = fonteImportacao.importarRegistrosContratos();
                break;
            case PRODUTO:
                registrosJSON = fonteImportacao.importarRegistrosProdutos();
                break;
        }

        String descricaoQuantidadeEncontrada = "Quantidade de registros encontrados: " + registrosJSON.size();
        solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, descricaoQuantidadeEncontrada);

        Gson gson = new Gson();
        Date dataImportacao = new Date();

        Set<String> listaMD5 = Sets.newHashSet(registroMigracaoDao.listarMD5(configuracaoImportacao, tipo));
        Session sessao = registroMigracaoDao.getCurrentSession();
        Usuario usuarioLogado = (Usuario) JSFUtilities.getFromSession(JSFUtilities.LOGGED);
        int limiteNotificacao = registrosJSON.size()/porcentagemNotificacao;
        int indicePorcentagem = 0;
        for (int i = 0; i< registrosJSON.size(); i++) {
            RegistroMigracaoJSON registroJSON = registrosJSON.get(i);
            RegistroMigracao registroMigracao = new RegistroMigracaoBuilder()//
                    .configuracaoImportacao(configuracaoImportacao) //
                    .codigoExterno(registroJSON.getIdExterno()) //
                    .codigoCliente(registroJSON.getCodigoChaveEstrangeira()) //
                    .conteudo(gson.toJson(registroJSON))//
                    .dataImportacao(dataImportacao)//
                    .status(StatusRegistroMigracao.IMPORTADO)//
                    .criador(usuarioLogado) //
                    .tipo(tipo)//
                    .build();
            MigracaoUtil.encriptarConteudoRegistroMigracao(registroMigracao);
            
            if (i % limiteNotificacao == 0 && solicitacaoMigracao != null) {
                String descricaoRegistrosConvertidos = "Gravando "+ (porcentagemNotificacao * indicePorcentagem++)  +"% de " +registrosJSON.size();
                solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, descricaoRegistrosConvertidos);
            }
            if (listaMD5.add(registroMigracao.getMd5())) {
                sessao.save(registroMigracao);
            }
        }
        String descricaoEncerramento = String.format("Importacao de registros finalizada para o tipo [" + tipo.name() + "]");
        solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, descricaoEncerramento);
    }

    private void exportarRegistros(SolicitacaoMigracao solicitacaoMigracao) {
        TipoRegistroMigracao tipo = solicitacaoMigracao.getTipoRegistroMigracao();
        ConfiguracaoImportacao configuracaoImportacao = solicitacaoMigracao.getConfiguracaoImportacao();

        solicitacaoMigracaoService.atualizarStatus(solicitacaoMigracao, StatusSolicitacaoMigracao.EM_EXECUTACAO);
        solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, "Iniciando exportacao de registros");

        try {
            List<RegistroMigracao> registrosMigracao = registroMigracaoDao.listarParaExportacao(configuracaoImportacao, tipo);

            String descricaoQuantidadeRegistrosExportacao = "Quantidade de registros econtrados: " + registrosMigracao.size();
            solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, descricaoQuantidadeRegistrosExportacao);

            exportarRegistrosMigracao(Lists.newArrayList(registrosMigracao), solicitacaoMigracao);

            solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, "Finalizando exportacao de registros");
        } catch (Exception ex) {
            solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, "Erro: " + ex.getMessage());
        }

        solicitacaoMigracaoService.finalizar(solicitacaoMigracao);
    }

    @Transactional
    @Override
    public void exportarRegistroMigracao(RegistroMigracao registroMigracao) {
        RegistroMigracao registroMigracaoConsultado = registroMigracaoDao.findById(registroMigracao.getCodigo());
        exportarRegistrosMigracao(Lists.newArrayList(registroMigracaoConsultado), null);
    }
    
    @Transactional
    @Override
    public void excluirRegistroMigracao(RegistroMigracao registroMigracao) {
        RegistroMigracao registroMigracaoConsultado = registroMigracaoDao.findById(registroMigracao.getCodigo());
        excluirRegistros(Lists.newArrayList(registroMigracaoConsultado), null);
    }

    private void exportarRegistrosMigracao(List<RegistroMigracao> registrosMigracao, SolicitacaoMigracao solicitacaoMigracao) {
        Date dataAtual = new Date();
        int limiteNotificacao = registrosMigracao.size()/porcentagemNotificacao;
        int indicePorcentagem = 0;
        for (int i = 0; i< registrosMigracao.size(); i++) {

            RegistroMigracao registro = registrosMigracao.get(i);
            RespostaExportacaoRegistroMigracaoJSON resposta = migracaoHttpService.exportarRegistroMigracao(registro);

            registro.setStatus(resposta.getStatus().equals("error") ? StatusRegistroMigracao.ERRO_EXPORTACAO : StatusRegistroMigracao.EXPORTADO);
            String mensagem = UteisValidacao.emptyString(registro.getMensagem()) ? "ADD: " + resposta.getMensagem() : registro.getMensagem()+"|ADD: " + resposta.getMensagem();
            registro.setMensagem(mensagem);
            registro.setDataExportacao(dataAtual);
            if(resposta.getCodigoRegistroZW() != null){
                registro.setCodigoInterno(resposta.getCodigoRegistroZW().toString());
            }
            registroMigracaoDao.update(registro);
            
            if (i % limiteNotificacao == 0 & solicitacaoMigracao != null) {
                String descricaoRegistrosConvertidos = "Exportando "+ (porcentagemNotificacao * indicePorcentagem++)  +"% de " +registrosMigracao.size();
                solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, descricaoRegistrosConvertidos);
            }
            
        }
    }
    
    public void excluirRegistros(List<RegistroMigracao> registrosMigracao, SolicitacaoMigracao solicitacaoMigracao) {
        Date dataAtual = new Date();
        int limiteNotificacao = registrosMigracao.size()/porcentagemNotificacao;
        int indicePorcentagem = 0;
        for (int i = 0; i< registrosMigracao.size(); i++) {

            RegistroMigracao registro = registrosMigracao.get(i);
            RespostaExportacaoRegistroMigracaoJSON resposta = migracaoHttpService.excluirRegistroMigracaoZW(registro);
            registro.setStatus(resposta.getStatus().equals("sucess") ? StatusRegistroMigracao.IMPORTADO :  registro.getStatus());
            String mensagem = UteisValidacao.emptyString(registro.getMensagem()) ? "DLT: " + resposta.getMensagem() : registro.getMensagem()+"|DLT: " + resposta.getMensagem();
            registro.setMensagem(mensagem);
            registro.setDataExportacao(dataAtual);
            registro.setCodigoInterno(resposta.getCodigoRegistroZW());
            registroMigracaoDao.update(registro);
            if(resposta.getStatus().equals("sucess")){
                for(TipoRegistroMigracao t : TipoRegistroMigracao.getTiposRelacionados(registro.getTipo())){
                    try {
                        ajustarRegistrosRelacionado(registro.getCodigoExterno(), t);
                    } catch (Exception ex) {
                        Logger.getLogger(MigracaoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }
             if (i % limiteNotificacao == 0 & solicitacaoMigracao != null) {
                 String descricaoRegistrosConvertidos = "Excluindo "+ (porcentagemNotificacao * indicePorcentagem++)  +"% de " +registrosMigracao.size();
                solicitacaoMigracaoService.atualizarDescricao(solicitacaoMigracao, descricaoRegistrosConvertidos);
            }
            
        }
    }

    private void ajustarRegistrosRelacionado(String codigoExterno, TipoRegistroMigracao t) throws Exception {
        List<RegistroMigracao> relacionados = registroMigracaoDao.findListByAttributes(new String[]{"chaveestrangeira","tipo","status"}, new Object[]{codigoExterno,t.name(),StatusRegistroMigracao.IMPORTADO}, "codigoexterno DESC", 0);
        for(RegistroMigracao relacionado : relacionados){
            relacionado.setStatus(StatusRegistroMigracao.IMPORTADO);
            String mensagem = UteisValidacao.emptyString(relacionado.getMensagem()) ? "DLT: Estorno entidade relacionada"  : relacionado.getMensagem()+"|DLT: Estorno entidade relacionada";
            relacionado.setMensagem(mensagem);
            registroMigracaoDao.update(relacionado);
            for(TipoRegistroMigracao tipo : TipoRegistroMigracao.getTiposRelacionados(relacionado.getTipo())){
                try {
                    ajustarRegistrosRelacionado(relacionado.getCodigoExterno(), tipo);
                } catch (Exception ex) {
                    Logger.getLogger(MigracaoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }
    }

}
