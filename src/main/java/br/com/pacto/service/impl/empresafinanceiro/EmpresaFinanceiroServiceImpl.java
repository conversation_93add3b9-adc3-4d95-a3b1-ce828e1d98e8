/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.empresafinanceiro;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.InfoInfraEnum;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiroJSON;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.controller.json.empresafinanceiro.OamdNichoDTO;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.dao.intf.empresafinanceiro.EmpresaFinanceiroDao;
import br.com.pacto.enums.Modulo;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.empresafinanceiro.EmpresaFinanceiroService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.server.Constants;
import edu.emory.mathcs.backport.java.util.Arrays;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.adm.client.EmpresaWS;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Service
public class EmpresaFinanceiroServiceImpl implements EmpresaFinanceiroService {

    @Autowired
    private EmpresaFinanceiroDao empresaDao;
    @Autowired
    private EmpresaDao empresaOamdDao;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private UsuarioService usuarioService;
    public static ConcurrentHashMap<String, List<EmpresaJSON>> CACHE_EMPRESA_REDE = new ConcurrentHashMap<>();
    public static ConcurrentHashMap<String, List<EmpresaWS>> CACHE_EMPRESAS = new ConcurrentHashMap<>();


    public EmpresaFinanceiro incluir(final EmpresaFinanceiro empresa) throws Exception {
        return empresaDao.insert(empresa);
    }

    public EmpresaFinanceiro alterar(final EmpresaFinanceiro empresa) throws Exception {
        return empresaDao.update(empresa);
    }

    public void alterarChaveCodigoZw(final EmpresaFinanceiro empresa) throws Exception {
        empresaDao.updateAlgunsCampos(new String[]{"chavezw", "empresazw"}, new Object[]{empresa.getChaveZw(), empresa.getEmpresazw()},
                new String[]{"codigo"}, new Object[]{empresa.getCodigo()});
    }

    @Override
    public List<EmpresaFinanceiro> consultarEmpresasRede(Integer rede) throws Exception {
        return empresaDao.consultarEmpresasRede(rede);
    }

    @Override
    public EmpresaFinanceiro obterPorChave(String key) throws Exception {
        return empresaDao.findObjectByAttribute("chavezw", key);
    }

    public List<EmpresaJSON> consultarEmpresasRede(String key) throws Exception {
        List<EmpresaJSON> empresas = new ArrayList<EmpresaJSON>();
        Empresa empOamd = empresaOamdDao.findById(key);

        if (!empOamd.getAtiva())
            return null;

        EmpresaFinanceiro empresaFinanceiro = obterPorChave(key);

        if (empresaFinanceiro.getRedeEmpresa() != null
                && !UteisValidacao.emptyNumber(empresaFinanceiro.getRedeEmpresa().getId())) {
            List<EmpresaFinanceiro> empresasFinanceiro = empresaDao.consultarEmpresasRede(empresaFinanceiro.getRedeEmpresa().getId());
            List<String> adds = new ArrayList<String>();
            for (EmpresaFinanceiro empfinan : empresasFinanceiro) {
                if (UteisValidacao.emptyString(empfinan.getChaveZw())) {
                    continue;
                }
                Empresa emp = empresaService.obterObjetoPorChave(empfinan.getChaveZw());
                if (!emp.getAtiva())
                    continue;

                List<EmpresaWS> empresasWS = null;
                if (CACHE_EMPRESAS.containsKey(emp.getChave())) {
                    empresasWS = CACHE_EMPRESAS.get(emp.getChave());
                } else {
                    empresasWS = AdmWSConsumer.obterEmpresasZWComSituacao(emp, empfinan.getChaveZw(), true);
                }

                for (EmpresaWS e : empresasWS) {

                    if (adds.contains(empfinan.getChaveZw() + "-" + e.getCodigo())) {
                        continue;
                    }

                    EmpresaJSON empresaJSON = new EmpresaJSON();
                    empresaJSON.setNome(e.getNome());
                    empresaJSON.setChave(empfinan.getChaveZw());
                    empresaJSON.setCodigo(e.getCodigo());
                    empresaJSON.setLatitude(e.getLatitude());
                    empresaJSON.setLongitude(e.getLongitude());
                    empresas.add(empresaJSON);
                    adds.add(empfinan.getChaveZw() + "-" + e.getCodigo());
                }
                if (!CACHE_EMPRESAS.containsKey(emp.getChave()) && !empresasWS.isEmpty()) {
                    CACHE_EMPRESAS.put(empfinan.getChaveZw(), empresasWS);
                }
            }
        }

        if (empresas.isEmpty()) {
            Empresa emp = empresaService.obterObjetoPorChave(key);
            if (emp.getAtiva()) {
                List<EmpresaWS> empresasWS = null;

                if (CACHE_EMPRESAS.containsKey(emp.getChave())) {
                    empresasWS = CACHE_EMPRESAS.get(emp.getChave());
                } else {
                    empresasWS = AdmWSConsumer.obterEmpresasZWComSituacao(emp, key, true);
                }

                for (EmpresaWS e : empresasWS) {
                    EmpresaJSON empresaJSON = new EmpresaJSON();
                    empresaJSON.setNome(e.getNome());
                    empresaJSON.setChave(key);
                    empresaJSON.setCodigo(e.getCodigo());
                    empresaJSON.setLatitude(e.getLatitude());
                    empresaJSON.setLongitude(e.getLongitude());
                    empresas.add(empresaJSON);
                }
                if (!CACHE_EMPRESAS.containsKey(emp.getChave()) && !empresasWS.isEmpty()) {
                    CACHE_EMPRESAS.put(emp.getChave(), empresasWS);
                }
            }
        }
        return empresas;
    }

    @Override
    public List<Empresa> consultarEmpresas() throws Exception {
        return empresaOamdDao.findAll();
    }

    public List<EmpresaFinanceiro> consultarTodas() throws Exception {
        return empresaDao.findAll();
    }

    public Map<String, EmpresaFinanceiro> obterMapaEmpresas() throws Exception {
        Map<String, EmpresaFinanceiro> mapaEmpresaFinanceiro = new HashMap<String, EmpresaFinanceiro>();
        List<EmpresaFinanceiro> empresasFinanceiro = consultarTodas();
        for (EmpresaFinanceiro emp : empresasFinanceiro) {
            mapaEmpresaFinanceiro.put(emp.getChaveZw() + "-" + emp.getEmpresazw(), emp);
        }
        return mapaEmpresaFinanceiro;
    }

    @Override
    public List<EmpresaFinanceiro> obterEmpresasCodigoFinanceiro() throws Exception {
        return empresaDao.consultarEmpresaCodigoFinanceiro();
    }

    public List<EmpresaFinanceiro> obterPorCnpj(String cnpj) throws Exception {
        String query = "SELECT emp FROM EmpresaFinanceiro emp WHERE emp.cnpj = :cnpj";
        HashMap<String, Object> params = new HashMap<>();
        params.put("cnpj", cnpj);
        return empresaDao.findByParam(query, params);

    }

    public int consultarEmpresaFinanceiroGrupo() throws Exception {
        String query = "select count(ef.codigo) from " + EmpresaFinanceiro.class.getName() + " ef where grupofavorecido like 'ACADEMIA ZILLYON WEB%' ";

        return empresaDao.count(query);

    }

    @Override
    public List<EmpresaFinanceiro> consultarEmpresasPorChave(String chave) throws Exception {

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("chavezw", chave);

        return empresaDao.findByParam("SELECT obj " +
                "FROM EmpresaFinanceiro obj " +
                "WHERE chavezw = :chavezw", params);
    }

    @Override
    public EmpresaFinanceiro obterEmpresa(Integer codigoEmpresaZw, String chave) throws Exception {

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("chavezw", chave);
        params.put("empresazw", codigoEmpresaZw);

        List<EmpresaFinanceiro> empresas = empresaDao.findByParam("SELECT obj " +
                "FROM EmpresaFinanceiro obj " +
                "WHERE empresazw = :empresazw AND chavezw = :chavezw", params);

        if (!empresas.isEmpty()) {
            return empresas.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<EmpresaFinanceiro> consultarPorNome(String nome) {
        return empresaDao.consultarPorNome(nome);
    }

    @Override
    public EmpresaFinanceiro obterPorCodigo(int codigo) {
        return empresaDao.findById(codigo);
    }

    @Override
    public List<EmpresaFinanceiro> obeterTodasEmpresasPlanoSucesso() throws Exception {
        return empresaDao.consultarEmpresaPlanoSucesso();
    }

    public String consultarNomeEmpresaPorChave(String chaveZW) throws Exception {
        EmpresaFinanceiro empresaFinanceiro = empresaDao.findObjectByAttribute("chaveZw", chaveZW);
        return empresaFinanceiro.getNomeFantasia();
    }

    public void alterarDataExpiracao(String chave, Integer empresa, Date dataExpiracao) throws Exception {
        try {
            EmpresaFinanceiro empFinan = obterEmpresa(empresa, chave);
            if (empFinan == null) {
                throw new Exception("Empresa financeiro não encontrado!");
            }
            empFinan.setDataExpiracaoZw(dataExpiracao);
            alterar(empFinan);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public EmpresaFinanceiro findByCodFinanceiro(Integer codFinanceiro) throws ServiceException {
        try {
            return empresaDao.findByCodFinanceiro(codFinanceiro);
        } catch (Exception ex) {
            Uteis.logar("------ Erro findByCodFinanceiro: " + ex.getMessage());
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Deprecated
    public void atualizarFinanceiro(EmpresaFinanceiroJSON empresaFinanceiro) throws ServiceException {
        try {
            String url = Aplicacao.getProp(Aplicacao.URL_WSFINANCEIRO);
            HttpPost httpPost = new HttpPost(url + "atualizarFinanceiro.faces");

            JSONObject json = empresaFinanceiro.getJsonFinanceiro();

            List<NameValuePair> postParameters = new ArrayList<>();
            postParameters.add(new BasicNameValuePair("json", json.toString()));
            httpPost.setEntity(new UrlEncodedFormEntity(postParameters, "UTF-8"));

            HttpClient client = HttpClientBuilder.create().build();
            HttpResponse response = client.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            String resposta = EntityUtils.toString(response.getEntity());
            if (statusCode != 200) {
                throw new ServiceException(resposta);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public void atualizarFinanceiroIOF(EmpresaFinanceiroJSON empresaFinanceiro) throws ServiceException {
        try {
            // Obtém a URL do serviço financeiro
            String url = Aplicacao.getProp(Aplicacao.URL_IFINAN);
            HttpPost httpPost = new HttpPost(url + "/favorecidos/atualizar");

            // Converte o objeto financeiro para JSON
            JSONObject json = empresaFinanceiro.getJsonFinanceiroIOF();

            // Define o JSON no corpo da requisição
            StringEntity entity = new StringEntity(json.toString(), "UTF-8");
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-Type", "application/json");

            // Cria o HttpClient e executa a requisição
            try (CloseableHttpClient client = HttpClientBuilder.create().build();
                 CloseableHttpResponse response = client.execute(httpPost)) {

                // Verifica o status da resposta
                int statusCode = response.getStatusLine().getStatusCode();
                String resposta = EntityUtils.toString(response.getEntity());

                // Se o status não for 200, lança exceção
                if (statusCode != 200) {
                    throw new ServiceException(resposta);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException("Erro ao atualizar o financeiro", ex);
        }
    }

    public List<EmpresaFinanceiro> consultarEmpresasChaveZwEmpresaZw(String chaveZW, List<Integer> empresas) throws Exception {
        return empresaDao.consultarEmpresasChaveZwEmpresaZw(chaveZW,empresas);
    }

    public boolean processarNicho(OamdNichoDTO nichoDTO) throws Exception {
        Empresa empresa = empresaOamdDao.findByIdOptional(nichoDTO.getChave())
                .orElseThrow(() -> new ServiceException("Empresa não encontrada"));

        empresa.setModulosEmpresa(new ArrayList<>(Arrays.asList(empresa.getModulos().split(","))));
        empresa.getModulosEmpresa().remove(Modulo.NICHO.getSiglaModulo());
        if (nichoDTO.isNichoAtivo()) {
            empresa.getModulosEmpresa().add(Modulo.NICHO.getSiglaModulo());
        }

        StringBuilder lstModulos = new StringBuilder();
        for (String moduloEmpresa : empresa.getModulosEmpresa()) {
            lstModulos.append(moduloEmpresa.concat(","));
        }
        empresa.setModulos(lstModulos.substring(0, lstModulos.length() - 1));
        empresaOamdDao.update(empresa);

        Usuario usuarioAPI = usuarioService.validarUsuario(Constants.LOGIN_API_OAMD, Constants.SENHA_API_OAMD, false);
        empresaService.atualizarModulosOAMDInfra(empresa, usuarioAPI);

        ExecuteRequestHttpService.executeRequest(empresa.getRoboControleSemHTTPS() + "/prest/pacto?op=invalidar-cache-nicho&propagable=s&chave=" + nichoDTO.getChave(), null);
        return true;
    }

    public int countActiveCompanyByInfra(InfoInfraEnum infoInfraEnum) throws Exception {
        return empresaDao.countActiveCompanyByInfra(infoInfraEnum);
    }
}
