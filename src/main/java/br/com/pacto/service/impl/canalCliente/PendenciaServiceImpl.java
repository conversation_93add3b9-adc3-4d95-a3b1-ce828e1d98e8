package br.com.pacto.service.impl.canalCliente;

import br.com.pacto.bean.canalCliente.Pendencia;
import br.com.pacto.dao.intf.canalCliente.PendenciaDao;
import br.com.pacto.dao.intf.canalCliente.ResponsavelDao;
import br.com.pacto.dao.intf.empresafinanceiro.EmpresaFinanceiroDao;
import br.com.pacto.service.intf.canalCliente.PendenciaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class PendenciaServiceImpl implements PendenciaService {


    @Autowired
    private PendenciaDao pendenciaDao;

    @Override
    public List<Pendencia> consultarPorChave(String chave) throws Exception {
        return pendenciaDao.consultarPorChave(chave);
    }

    @Override
    public List<Pendencia> consultarPorEmpresaFinanceiro(Integer empresaFinanceiro) {
        return pendenciaDao.consultarPorEmpresaFinanceiro(empresaFinanceiro);
    }

}
