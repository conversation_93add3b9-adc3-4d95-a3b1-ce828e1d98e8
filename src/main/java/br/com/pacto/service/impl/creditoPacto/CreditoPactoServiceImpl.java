package br.com.pacto.service.impl.creditoPacto;

import br.com.pacto.bean.creditoPacto.CreditoPacto;
import br.com.pacto.dao.intf.creditoPacto.CreditoPactoDao;
import br.com.pacto.enums.TipoProdutoPacto;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.creditoPacto.CreditoPactoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 25/09/2020.
 */
@Service
@Qualifier(value = "creditoPactoService")
public class CreditoPactoServiceImpl implements CreditoPactoService {


    @Autowired
    private CreditoPactoDao creditoPactoDao;

    public CreditoPactoDao getCreditoPactoDao() {
        return creditoPactoDao;
    }

    public CreditoPacto inserir(CreditoPacto object) throws ServiceException {
        try {
            return getCreditoPactoDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public CreditoPacto alterar(CreditoPacto object) throws ServiceException {
        try {
            return getCreditoPactoDao().update(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(CreditoPacto object) throws ServiceException {
        try {
            getCreditoPactoDao().delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<CreditoPacto> obterTodos() throws ServiceException {
        try {
            return getCreditoPactoDao().findAll();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public CreditoPacto obterPorId(Integer id) throws ServiceException {
        try {
            return getCreditoPactoDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<CreditoPacto> consultar(TipoProdutoPacto tipo, Boolean ativo) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();
            sql.append("SELECT obj FROM ProdutoPacto obj WHERE 1 = 1 ");
            if (tipo != null) {
                sql.append(" AND obj.tipo = :tipo ");
                params.put("tipo", tipo);
            }
            if (ativo != null) {
                sql.append(" AND obj.ativo = :ativo ");
                params.put("ativo", ativo);
            }
            sql.append(" ORDER BY obj.nome ");
            return getCreditoPactoDao().findByParam(sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
