/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.envioprotheus;

import br.com.pacto.bean.envioprotheus.EnvioProtheus;
import br.com.pacto.bean.envioprotheus.StatusEnvioEnum;
import br.com.pacto.dao.intf.envioprotheus.EnvioProtheusDao;
import br.com.pacto.service.intf.envioprotheus.EnvioProtheusService;
import br.com.pacto.util.impl.Ordenacao;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class EnvioProtheusServiceImpl implements EnvioProtheusService {

    @Autowired
    private EnvioProtheusDao envioDao;

    @Override
    public EnvioProtheus incluir(EnvioProtheus envio) throws Exception {
        return envioDao.insert(envio);
    }

    @Override
    public List<EnvioProtheus> historico(Integer limit, StatusEnvioEnum ... status) throws Exception {
        List<EnvioProtheus> lista = new ArrayList<EnvioProtheus>();
        for(StatusEnvioEnum st : status){
            lista.addAll(envioDao.findListByAttributes(new String[]{"status"}, new Object[]{st}, "horaEnvio DESC", limit));
        }
        lista = Ordenacao.ordenarLista(lista, "horaEnvio");
        Collections.reverse(lista);
        return lista;
    }
    
    @Override
    public void alterarStatus(String chavedts, StatusEnvioEnum status, String msg) throws Exception{
        Map<String, Object> mapa = new HashMap<String, Object>();
        mapa.put("chave", chavedts);
        EnvioProtheus obj = envioDao.findObjectByParam("SELECT obj FROM EnvioProtheus obj WHERE codigoRetorno = :chave", mapa);
        obj.setStatus(status);
        obj.setMsErro(msg);
        envioDao.update(obj);
    } 

    @Override
    public Integer obterNumeroEnvios(StatusEnvioEnum envio) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(obj.codigo) FROM EnvioProtheus obj ");
        if (envio != null) {
            sql.append(" WHERE obj.status = ").append(envio.ordinal());
        }
        List<BigInteger> todos = envioDao.listOfObjects(sql.toString());
        return todos == null || todos.isEmpty() ? 0 : todos.get(0).intValue();

    }
}
