package br.com.pacto.service.impl.pactoControle;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.integracao.IntegracaoCadastrosWSConsumer;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.pactoControle.PactoControleService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PactoControleServiceImpl implements PactoControleService {

    @Autowired
    private EmpresaService empresaService;
    public static boolean liberarApis = true;

    public boolean checkFree(){
        return liberarApis;
    }

    public void toggleFreeApis(boolean free){
        liberarApis = free;
    }

    @Override
    public JSONArray consultarInfoFinanceiras(String key, Integer codEmpresa) throws Exception {
        Empresa empresa = empresaService.obterPorId(key);
        String result = IntegracaoCadastrosWSConsumer.consultarInfoFinanceiras(empresa.getRoboControleSemHTTPS(), key, codEmpresa);
        if (result.contains("ERRO")) {
            JSONObject jsonObject = new JSONObject(result);
            throw new Exception((String) jsonObject.get("ERRO"));
        }
        JSONArray jsonArray = new JSONArray(result);
        return jsonArray;
    }

    @Override
    public String downloadBoleto(String key, Integer codigoBoleto, String dataVencimento) throws Exception {
        Empresa empresa = empresaService.obterPorId(key);
        return IntegracaoCadastrosWSConsumer.downloadBoleto(empresa.getRoboControleSemHTTPS(), key, codigoBoleto, dataVencimento);
    }

    @Override
    public JSONArray solicitacoesConcluida(String key, String email) throws Exception {
        Empresa empresa = empresaService.obterPorId(key);
        String result = IntegracaoCadastrosWSConsumer.solicitacoesConcluidas(empresa.getRoboControleSemHTTPS(), key, email);
        return converArray(result);
    }

    @Override
    public JSONArray solicitacoesEmAberto(String key, String email) throws Exception {
        Empresa empresa = empresaService.obterPorId(key);
        String result = IntegracaoCadastrosWSConsumer.solicitacoesEmAberto(empresa.getRoboControleSemHTTPS(), key, email);
        return converArray(result);
    }

    @Override
    public JSONObject consultarEstastisticasSolicitacoes(String key, String email) throws Exception {
        Empresa empresa = empresaService.obterPorId(key);
        String result = IntegracaoCadastrosWSConsumer.consultarEstastisticasSolicitacoes(empresa.getRoboControleSemHTTPS(), email);
        JSONObject jsonObject = new JSONObject(result);
        if (jsonObject.has("ERRO")){
            throw new Exception(jsonObject.get("ERRO").toString());
        }
        return jsonObject;
    }

    private JSONArray converArray(String str) throws Exception{
        try{
            JSONArray jsonArray = new JSONArray(str);
            return jsonArray;
        }catch (Exception e) {
            JSONObject jsonObject = new JSONObject(str);
            throw new Exception((String) jsonObject.get("ERRO"));
        }
    }

    @Override
    public JSONObject consultarEstastisticasTicket(String key, String id) throws Exception {
        final Empresa empresa = empresaService.obterPorId(key);
        if (empresa == null) {
            throw new Exception("Erro: Empresa não encontrada. Chave: "+ key);
        }
        String result = IntegracaoCadastrosWSConsumer.solicitacoesTickets(empresa.getRoboControleSemHTTPS(), id);
        JSONObject jsonObject = new JSONObject(result);
        if (jsonObject.has("ERRO")){
            throw new Exception(jsonObject.get("ERRO").toString());
        }
        return jsonObject;
    }
}
