package br.com.pacto.service.impl.produtoPacto;

import br.com.pacto.bean.produtoPacto.ProdutoPactoCompra;
import br.com.pacto.bean.produtoPacto.ProdutoPactoLog;
import br.com.pacto.controller.json.produtoPacto.EmpresaDTO;
import br.com.pacto.dao.intf.produtoPacto.ProdutoPactoLogDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.produtoPacto.ProdutoPactoLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Lu<PERSON>
 * Date: 09/06/2020
 */
@Service
public class ProdutoPactoLogServiceImpl implements ProdutoPactoLogService {

    @Autowired
    private ProdutoPactoLogDao produtoPactoLogDao;

    public ProdutoPactoLogDao getProdutoPactoLogDao() {
        return produtoPactoLogDao;
    }

    public void setProdutoPactoLogDao(ProdutoPactoLogDao produtoPactoLogDao) {
        this.produtoPactoLogDao = produtoPactoLogDao;
    }

    public void logBaixa(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, String operacao, String log) {
        inserirLog(compra, empresaDTO, operacao, log, "BAIXA");
    }

    public void logAlta(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, String operacao, String log) {
        inserirLog(compra, empresaDTO, operacao, log, "ALTA");
    }

    public void log(ProdutoPactoCompra compra, EmpresaDTO empresaDTO, String operacao, String log) {
        inserirLog(compra, empresaDTO, operacao, log, "LOG");
    }

    private void inserirLog(ProdutoPactoCompra compra, EmpresaDTO empresaDTO,
                            String operacao, String log, String urgencia) {
        try {
            getProdutoPactoLogDao().insert(new ProdutoPactoLog(compra, empresaDTO, operacao, log, urgencia));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public List<ProdutoPactoLog> consultar(Boolean resolvido) throws ServiceException {
        try {
            return getProdutoPactoLogDao().findAll();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
