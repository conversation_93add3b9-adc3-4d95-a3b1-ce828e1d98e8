package br.com.pacto.service.impl.configassinatura;

import br.com.pacto.bean.assinaturas.ConfigAssinatura;
import br.com.pacto.bean.empresa.Paths;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.dao.intf.configassinatura.ConfigAssinaturaDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.empresa.DiscoveryService;
import br.com.pacto.service.intf.configassinatura.ConfigAssinaturaService;
import br.com.pacto.util.HttpServico;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> Alcides
 */
@Service
public class ConfigAssinaturaServiceImpl implements ConfigAssinaturaService {

    @Autowired
    private HttpServico httpServico;
    @Autowired
    private ConfigAssinaturaDao configAssinaturaDao;
    @Autowired
    private DiscoveryService discoveryService;

    private String urlApi() throws Exception {
        Paths paths = discoveryService.paths();
        return paths.getApiZw();
    }

    @Override
    public ConfigAssinatura salvar(ConfigAssinatura object) throws ServiceException {
        try {
            if(object.getId() == null || object.getId() == 0){
                object = configAssinaturaDao.insert(object);
            }else{
                object = configAssinaturaDao.update(object);
            }
            return object;
        } catch (Exception e) {
            throw new ServiceException("Problema ao salvar a configuração de assinaturas. Erro em : " + e.getMessage());
        }
    }

    @Override
    public List<ConfigAssinatura> obterAtivos(String usuario) throws ServiceException {
        try {
            List<ConfigAssinatura> param = configAssinaturaDao.findByParam("select obj from ConfigAssinatura obj where obj.status = 'ativo' order by dataRegistro desc", new HashMap<>());
            if (param != null && !param.isEmpty()) {
                return param;
            }
            return new ArrayList(){{
                add(salvar(new ConfigAssinatura()));
            }};
        } catch (Exception e) {
            throw new ServiceException("Problema ao obter a configuração de assinaturas. Erro em : " + e.getMessage());
        }
    }

    @Override
    public ConfigAssinatura pactoAtiva() throws ServiceException {
        try {
            List<ConfigAssinatura> param = configAssinaturaDao.findByParam("select obj from ConfigAssinatura obj where obj.status = 'ativo' order by dataRegistro desc", new HashMap<>());
            for(ConfigAssinatura config : param){
                if(config.getTipo().equals("pacto")){
                    return config;
                }
            }
            throw new Exception("Configuração Pacto não encontrada");
        } catch (Exception e) {
            throw new ServiceException("Problema ao obter a configuração de assinaturas. Erro em : " + e.getMessage());
        }
    }

    @Override
    public ConfigAssinatura findById(Integer id) throws ServiceException {
        try {
            return configAssinaturaDao.findById(id);
        } catch (Exception e) {
            throw new ServiceException("Problema ao obter a configuração de assinaturas. Erro em : " + e.getMessage());
        }
    }

    @Override
    public List<PlanoDTO> planos(ConfigAssinatura config) throws ServiceException {
        try {
            List<PlanoDTO> planos = new ArrayList<>();
            String urlApi = urlApi();
            JSONObject retorno = httpServico.getJson(urlApi + "/v2/vendas/" + config.getChave() + "/planos/" + config.getEmpresa(),
                    null);
            JSONArray jsonArray = retorno.optJSONArray("return");
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    planos.add(new PlanoDTO(jsonArray.optJSONObject(i)));
                }
            }
            return planos;
        } catch (Exception e) {
            throw new ServiceException("Problema ao obter empresas. Erro em : " + e.getMessage());
        }
    }

    @Override
    public List<EmpresaJSON> empresas(ConfigAssinatura config) throws ServiceException {
        try {
            List<EmpresaJSON> empresas = new ArrayList<>();
            String urlApi = urlApi();
            JSONObject retorno = httpServico.postJson(urlApi + "/gempresarial/" + config.getChave() + "/consultarEmpresas",
                    "", null);
            JSONArray jsonArray = retorno.optJSONArray("return");
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    EmpresaJSON empresaJSON = new EmpresaJSON();
                    JSONObject empresa = jsonArray.optJSONObject(i);
                    empresaJSON.setCodigo(empresa.optInt("codigo"));
                    empresaJSON.setNome(empresa.optString("nome"));
                    empresas.add(empresaJSON);
                }
            }
            return empresas;
        } catch (Exception e) {
            throw new ServiceException("Problema ao obter empresas. Erro em : " + e.getMessage());
        }

    }

}
