package br.com.pacto.service.impl.erro;

import br.com.pacto.bean.erro.Erro;
import br.com.pacto.dao.intf.erro.ErroDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.erro.ErroService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class ErroServiceImpl implements ErroService {

    @Autowired
    private ErroDao erroDao;

    @Override
    public Erro inserir(Erro erro) throws ServiceException {
        try{
            return getErroDao().insert(erro);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public ErroDao getErroDao() {
        return erroDao;
    }

    public void setErroDao(ErroDao erroDao) {
        this.erroDao = erroDao;
    }
}
