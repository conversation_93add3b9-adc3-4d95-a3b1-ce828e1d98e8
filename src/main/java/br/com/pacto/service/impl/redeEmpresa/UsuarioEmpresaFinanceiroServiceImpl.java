package br.com.pacto.service.impl.redeEmpresa;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.redeempresa.UsuarioEmpresaFinanceiro;
import br.com.pacto.bean.empresa.redeempresa.UsuarioRedeEmpresa;
import br.com.pacto.dao.intf.redeempresa.UsuarioEmpresaFinanceiroDao;
import br.com.pacto.integracao.IntegracaoCadastrosWSConsumer;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.redeempresa.UsuarioEmpresaFinanceiroService;
import br.com.pacto.service.intf.redeempresa.UsuarioRedeEmpresaService;
import br.com.pacto.util.UtilContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.UUID;

/**
 * Created by Rafael on 10/03/2017.
 */
@Service
public class UsuarioEmpresaFinanceiroServiceImpl implements UsuarioEmpresaFinanceiroService {

    @Autowired
    UsuarioEmpresaFinanceiroDao usuarioEmpresaFinanceiroDao;
    @Autowired
    UsuarioRedeEmpresaService redeService;

    @Override
    public UsuarioEmpresaFinanceiro incluir(UsuarioEmpresaFinanceiro obj) throws Exception {
       return usuarioEmpresaFinanceiroDao.insert(obj);
    }
    @Override
    public UsuarioEmpresaFinanceiro update(UsuarioEmpresaFinanceiro obj) throws Exception {
        return usuarioEmpresaFinanceiroDao.update(obj);
    }
    public void deletePorUsuarioRede(Integer codigoUsuarioRede) throws Exception{
        usuarioEmpresaFinanceiroDao.deleteComParam(new String[]{"usuarioRedeEmpresa.codigo"}, new Object[]{codigoUsuarioRede});
    }
    public void delete(UsuarioEmpresaFinanceiro obj) throws Exception{
        usuarioEmpresaFinanceiroDao.delete(obj);
    }
    public List<UsuarioEmpresaFinanceiro> obterEmpresasRedeUsuario(Integer usuarioRedeEmpresa) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("Select obj from UsuarioEmpresaFinanceiro obj WHERE obj.usuarioRedeEmpresa.codigo = :usuarioRedeEmpresa");
        HashMap<String,Object> param = new HashMap<String, Object>();
        param.put("usuarioRedeEmpresa",usuarioRedeEmpresa);
        return usuarioEmpresaFinanceiroDao.findByParam(sql.toString(),param);
    }
    public UsuarioEmpresaFinanceiro obterPorUsuarioRede(Integer usuarioRedeEmpresa,String chave) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("Select obj from UsuarioEmpresaFinanceiro obj WHERE obj.usuarioRedeEmpresa.codigo = :usuarioRedeEmpresa and obj.empresaFinanceiro.chaveZw = :chave");
        HashMap<String,Object> param = new HashMap<String, Object>();
        param.put("usuarioRedeEmpresa",usuarioRedeEmpresa);
        param.put("chave",chave);
        return usuarioEmpresaFinanceiroDao.findObjectByParam(sql.toString(),param);
    }

    public String gerarSenhaTemporaria(String email, final Boolean enviarSMS) throws Exception{
        UsuarioRedeEmpresa usuarioRedeEmpresa = redeService.descobrirEmpresaEmail(email);
        if(usuarioRedeEmpresa == null){
            throw new Exception("Não encontrado usuário do aplicativo com este email.");
        }
        String chave = usuarioRedeEmpresa.getEmpresaFinanceiro().getChaveZw();
        String uuid = UUID.randomUUID().toString();
        String senha = uuid.substring(0, uuid.indexOf("-"));
        Empresa empresa = getEmpresaService().obterPorId(chave);
        return IntegracaoCadastrosWSConsumer.modificarSenhaUsuarioEmail(empresa.getRoboControleSemHTTPS(), chave, email, senha, true, null, enviarSMS);
    }

    public String gravarNovaSenha(String email, String senha, String senhaAntiga) throws Exception{
        UsuarioRedeEmpresa usuarioRedeEmpresa = redeService.descobrirEmpresaEmail(email);
        String chave = usuarioRedeEmpresa.getEmpresaFinanceiro().getChaveZw();
        Empresa empresa = getEmpresaService().obterPorId(chave);
        return IntegracaoCadastrosWSConsumer.modificarSenhaUsuarioEmail(empresa.getRoboControleSemHTTPS(), chave, email, senha, false, senhaAntiga, false);
    }

    private EmpresaService getEmpresaService() {
        return (EmpresaService) UtilContext.getBean(EmpresaService.class);
    }
}
