/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.facilite;

/**
 *
 * <AUTHOR>
 */
public enum SituacaoRemessaFaciliteEnum {
    NENHUMA(0, "Nenhuma", "", "", ""),//vazio
    <PERSON>(1, "Gerada", "#00008B"/*azul escuro*/, "Remessa Gerada, precisa ser enviada.", ""),
    RETORNO_PROCESSADO(2, "Retorno Processado", "#008800"/*verde*/, "Retorno processado. Veja Resultado", ""),
    ERRO_RETORNO(3, "Erro Retorno", "#FFD42E", "Houve algum erro de validação do arquivo de Retorno.", ""),
    REMESSA_ENVIADA(4, "Remessa Enviada", "#777777", "Remessa Enviada pelo Processo Automático. Aguarde Retorno Automático.", ""),
    REMESSA_PADRAO(0, "Remessa <PERSON>", "", "Remessa padrão para cobrança.", "remessa-padrao"),
    REMESSA_CANCELAMENTO(0, "Remessa Cancelamento", "", "Remessa de cancelamento de cobrança.", "remessa-cancelamento");
    private int id;
    private String descricao;
    private String cor;
    private String hint;
    private String classe;

    private SituacaoRemessaFaciliteEnum(int id, String descricao, String cor, String hint, String classe) {
        this.id = id;
        this.descricao = descricao;
        this.cor = cor;
        this.hint = hint;
        this.classe = classe;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public static SituacaoRemessaFaciliteEnum valueOf(final int id) {
        SituacaoRemessaFaciliteEnum[] values = SituacaoRemessaFaciliteEnum.values();
        for (SituacaoRemessaFaciliteEnum sit : values) {
            if (sit.id == id) {
                if (sit.equals(SituacaoRemessaFaciliteEnum.REMESSA_PADRAO) || sit.equals(SituacaoRemessaFaciliteEnum.REMESSA_CANCELAMENTO)) {
                    return SituacaoRemessaFaciliteEnum.NENHUMA;
                }
                return sit;
            }
        }
        return SituacaoRemessaFaciliteEnum.NENHUMA;
    }

    public String getClasse() {
        return classe;
    }

}
