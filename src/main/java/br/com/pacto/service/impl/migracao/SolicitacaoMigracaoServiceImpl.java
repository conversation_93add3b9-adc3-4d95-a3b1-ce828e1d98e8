package br.com.pacto.service.impl.migracao;

import br.com.pacto.bean.migracao.builder.SolicitacaoMigracaoBuilder;
import br.com.pacto.bean.migracao.enums.StatusSolicitacaoMigracao;
import br.com.pacto.bean.migracao.enums.TipoRegistroMigracao;
import br.com.pacto.bean.migracao.enums.TipoSolicitacaoMigracao;
import br.com.pacto.bean.migracao.modelo.ConfiguracaoImportacao;
import br.com.pacto.bean.migracao.modelo.SolicitacaoMigracao;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.migracao.SolicitacaoMigracaoDao;
import br.com.pacto.service.intf.migracao.SolicitacaoMigracaoService;
import br.com.pacto.util.impl.JSFUtilities;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Descrição: Serviço responsável por @{link SolicitacaoMigracao}
 * Projeto: NewOAMD
 *
 * <AUTHOR> - 12/abr/2018 às 14:29
 * Pacto Soluções - Todos os direitos reservados
 */
@Service
@Transactional(readOnly = true)
public class SolicitacaoMigracaoServiceImpl implements SolicitacaoMigracaoService {

    @Autowired
    private SolicitacaoMigracaoDao solicitacaoMigracaoDao;

    @Transactional
    public SolicitacaoMigracao criarSolicitacaoMigracao(ConfiguracaoImportacao configuracaoImportacao,
                                      TipoSolicitacaoMigracao tipoSolicitacaoMigracao, TipoRegistroMigracao tipoRegistroMigracao) {
        Usuario usuarioLogado = (Usuario) JSFUtilities.getFromSession(JSFUtilities.LOGGED);
        SolicitacaoMigracao solicitacaoMigracao = new SolicitacaoMigracaoBuilder()//
                .configuracaoImportacao(configuracaoImportacao)//
                .dataCriacao(new Date())//
                .tipo(tipoSolicitacaoMigracao)//
                .tipoRegistroMigracao(tipoRegistroMigracao)//
                .criador(usuarioLogado)//
                .status(StatusSolicitacaoMigracao.CRIADA)//
                .build();
        solicitacaoMigracaoDao.getCurrentSession().save(solicitacaoMigracao);
        return solicitacaoMigracao;
    }

    /**
     * Atualiza a descrição de forma incremental, respeitando o tamanho máximo da coluna descrição
     *
     * @param solicitacaoMigracao
     * @param descricao
     */
    @Transactional
    public void atualizarDescricao(SolicitacaoMigracao solicitacaoMigracao, String descricao) {

        String descricaoSolicitacaoMigracao = solicitacaoMigracao.getDescricao();
        if (StringUtils.isEmpty(descricaoSolicitacaoMigracao)) {
            descricaoSolicitacaoMigracao = descricao;
        } else {
            descricaoSolicitacaoMigracao += " | " + descricao;
        }
        if (descricaoSolicitacaoMigracao.length() > SolicitacaoMigracao.TAMANHO_DESCRICAO) {
            int indexFinal = descricaoSolicitacaoMigracao.length();
            int indexInicial = indexFinal - SolicitacaoMigracao.TAMANHO_DESCRICAO;
            descricaoSolicitacaoMigracao = descricaoSolicitacaoMigracao.substring(indexInicial, indexFinal);
        }
        solicitacaoMigracao.setDescricao(descricaoSolicitacaoMigracao);
        solicitacaoMigracaoDao.update(solicitacaoMigracao);
    }

    @Transactional
    public void atualizarStatus(SolicitacaoMigracao solicitacaoMigracao, StatusSolicitacaoMigracao status) {
        solicitacaoMigracao.setStatus(status);
        solicitacaoMigracaoDao.update(solicitacaoMigracao);
    }

    @Transactional
    public void finalizar(SolicitacaoMigracao solicitacaoMigracao) {
        solicitacaoMigracao.setStatus(StatusSolicitacaoMigracao.FINALIZADA);
        solicitacaoMigracao.setDataTermino(new Date());
        solicitacaoMigracaoDao.update(solicitacaoMigracao);
    }

    public List<SolicitacaoMigracao> listar(ConfiguracaoImportacao configuracaoImportacao, TipoSolicitacaoMigracao tipo){
        return solicitacaoMigracaoDao.listar(configuracaoImportacao, tipo);
    }
    
    public boolean existeSolicitacaoEmAndamento(ConfiguracaoImportacao configuracaoImportacao){
        try {
            return solicitacaoMigracaoDao.count("codigo",new String[]{"configuracaoimportacao","status"}, new Object[]{configuracaoImportacao.getCodigo(),StatusSolicitacaoMigracao.EM_EXECUTACAO}).intValue() > 0;
        } catch (Exception ex) {
            Logger.getLogger(SolicitacaoMigracaoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
        return false;
    }

}
