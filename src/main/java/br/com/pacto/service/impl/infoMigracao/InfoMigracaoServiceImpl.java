package br.com.pacto.service.impl.infoMigracao;

import br.com.pacto.bean.infoMigracao.InfoMigracaoLog;
import br.com.pacto.bean.produtoPacto.ProdutoPacto;
import br.com.pacto.bean.produtoPacto.ProdutoPactoCompra;
import br.com.pacto.bean.produtoPacto.ProdutoPactoCupomDesconto;
import br.com.pacto.dao.intf.infoMigracao.InfoMigracaoDao;
import br.com.pacto.dao.intf.produtoPacto.ProdutoPactoCompraDao;
import br.com.pacto.enums.TipoProdutoPacto;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.infoMigracao.InfoMigracaoService;
import br.com.pacto.service.intf.produtoPacto.ProdutoPactoCompraService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class InfoMigracaoServiceImpl implements InfoMigracaoService {

    @Autowired
    private InfoMigracaoDao dao;

    public InfoMigracaoLog inserir(InfoMigracaoLog object) throws ServiceException {
        try {
            return getDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public InfoMigracaoLog obterPorId(Integer id) throws ServiceException {
        try {
            return getDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<InfoMigracaoLog> consultarTodos() throws ServiceException {
        try {
            return getDao().findAll();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public InfoMigracaoDao getDao() {
        return dao;
    }

    public void setDao(InfoMigracaoDao dao) {
        this.dao = dao;
    }
}
