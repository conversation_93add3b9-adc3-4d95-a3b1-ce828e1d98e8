package br.com.pacto.service.impl.testeProducao;

import br.com.pacto.bean.testeProducao.StatusEnum;
import br.com.pacto.bean.testeProducao.StatusTesteDiario;
import br.com.pacto.dao.intf.testeProducao.StatusTesteDiarioDAO;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.testeProducao.StatusTesteDiarioService;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by fabio on 16/08/2016.
 */
@Service
public class StatusTesteDiarioServiceImpl implements StatusTesteDiarioService{
    @Autowired
    private StatusTesteDiarioDAO statusTesteDiarioDao;

    @Override
    public StatusTesteDiario inserir(StatusTesteDiario object) throws ServiceException {
        try {
            if(Uteis.getDataComHoraZerada(object.getDataExecTeste()).toString().equals(Uteis.getDataComHoraZerada(new Date()).toString())) {
                if(!existeRegistroParaOServicoHoje(object)) {
                    return getStatusTesteDiarioDao().insert(object);
                }
            }
            return null;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public StatusTesteDiario alterarPorServicoEData(StatusTesteDiario object) throws ServiceException{
        try {
            if(Uteis.getDataComHoraZerada(object.getDataExecTeste()).toString().equals(Uteis.getDataComHoraZerada(new Date()).toString())) {
                StringBuilder sql = new StringBuilder();
                sql.append(" SELECT obj from StatusTesteDiario obj");
                sql.append("  WHERE testeServico = :nomeservico ");
                sql.append("    AND dataExecTeste >= :datainiciodia ");
                sql.append("    AND infra = :infra ");

                Map<String, Object> params = new HashMap();
                params.put("nomeservico", object.getTesteServico());
                params.put("infra", object.getInfra());
                Date data = new Date();
                data.setHours(0);
                data.setMinutes(0);
                data.setSeconds(0);
                params.put("datainiciodia", data);
                StatusTesteDiario std = getStatusTesteDiarioDao().findObjectByParam(sql.toString(), params);
                if (object.getStatus() == StatusEnum.RUNNING) {
                    std.setTotalSucesso(std.getTotalSucesso() + 1);
                    std.setObservacao(null);
                    std.setStatus(StatusEnum.RUNNING);
                } else if (object.getStatus() == StatusEnum.UNSTABLE) {
                    std.setTotalFalhaParcial(std.getTotalFalhaParcial() + 1);
                    std.setObservacao(object.getObservacao());
                    std.setStatus(StatusEnum.UNSTABLE);
                } else if (object.getStatus() == StatusEnum.INTERRUPTED) {
                    std.setTotalfalhas(std.getTotalfalhas() + 1);
                    std.setObservacao(object.getObservacao());
                    std.setStatus(StatusEnum.INTERRUPTED);
                }
                getStatusTesteDiarioDao().update(std);
                return std;
            }
            return null;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Boolean existeRegistroParaOServicoHoje(StatusTesteDiario object) throws ServiceException {
        try {
            StringBuilder  sql = new StringBuilder("SELECT obj FROM StatusTesteDiario obj WHERE testeServico =:testeservico AND dataExecTeste >= :datainiciodia and infra = :infra" );
            Map<String, Object> params = new HashMap();
            params.put("testeservico", object.getTesteServico());
            params.put("infra", object.getInfra());
            Date data = new Date();
            data.setHours(0);
            data.setMinutes(0);
            data.setSeconds(0);
            params.put("datainiciodia", data);
            StatusTesteDiario std;
            std = getStatusTesteDiarioDao().findObjectByParam(sql.toString(), params);

            if(std == null){
                return false;
            }
            return true;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void eliminarStatusComMaisDeTrintaDias() throws ServiceException {
        try{
            Date dataAnterior = Uteis.obterDataAnterior(Calendario.hoje(), 30);
            StringBuilder sql = new StringBuilder("SELECT obj from StatusTesteDiario obj WHERE dataExecTeste < :dataAnterior");
            Map<String, Object> params = new HashMap();
            params.put("dataAnterior", dataAnterior);
            List<StatusTesteDiario> listStd = getStatusTesteDiarioDao().findByParam(sql.toString(), params);

            if(listStd != null || !listStd.isEmpty()) {
                for (StatusTesteDiario std : listStd) {
                    getStatusTesteDiarioDao().delete(std);
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public StatusTesteDiarioDAO getStatusTesteDiarioDao() {
        return statusTesteDiarioDao;
    }

    public void setStatusTesteDiarioDao(StatusTesteDiarioDAO statusTesteDiarioDao) {
        this.statusTesteDiarioDao = statusTesteDiarioDao;
    }

    @Override
    public List<StatusTesteDiario> listarComDataDeHoje() {
        List<StatusTesteDiario> listStd = new ArrayList<StatusTesteDiario>();
        try{
            StringBuilder sql = new StringBuilder("SELECT obj from StatusTesteDiario obj WHERE dataExecTeste >= :datainiciodia");
            Map<String, Object> params = new HashMap();
            Date data = new Date();
            data.setHours(0);
            data.setMinutes(0);
            data.setSeconds(0);
            params.put("datainiciodia", data);
            listStd = getStatusTesteDiarioDao().findByParam(sql.toString(), params);

            if(listStd != null || !listStd.isEmpty()) {
                for (StatusTesteDiario std : listStd) {
                    getStatusTesteDiarioDao().findAll();
                }
            }
        } catch (Exception ex) {
            
        }
        
        return listStd;
    }

    @Override
    public List<StatusTesteDiario> listarTodos() {
        List<StatusTesteDiario> listStd = new ArrayList<StatusTesteDiario>();
        try {
            listStd = getStatusTesteDiarioDao().findAll();
        } catch (Exception ex) {
            Logger.getLogger(StatusTesteDiarioServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
        
        return listStd;
    }
}
