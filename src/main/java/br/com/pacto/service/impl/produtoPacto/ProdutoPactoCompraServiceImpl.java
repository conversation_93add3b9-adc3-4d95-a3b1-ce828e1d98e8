package br.com.pacto.service.impl.produtoPacto;

import br.com.pacto.bean.produtoPacto.ProdutoPacto;
import br.com.pacto.bean.produtoPacto.ProdutoPactoCompra;
import br.com.pacto.bean.produtoPacto.ProdutoPactoCupomDesconto;
import br.com.pacto.dao.intf.produtoPacto.ProdutoPactoCompraDao;
import br.com.pacto.enums.TipoProdutoPacto;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.produtoPacto.ProdutoPactoCompraService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProdutoPactoCompraServiceImpl implements ProdutoPactoCompraService {

    @Autowired
    private ProdutoPactoCompraDao produtoPactoCompraDao;

    public ProdutoPactoCompra inserir(ProdutoPactoCompra object) throws ServiceException {
        try {
            return getProdutoPactoCompraDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProdutoPactoCompra alterar(ProdutoPactoCompra object) throws ServiceException {
        try {
            return getProdutoPactoCompraDao().update(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(ProdutoPactoCompra object) throws ServiceException {
        try {
            getProdutoPactoCompraDao().delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProdutoPactoCompra> obterTodos() throws ServiceException {
        try {
            return getProdutoPactoCompraDao().findAll();
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProdutoPactoCompra obterPorId(Integer id) throws ServiceException {
        try {
            return getProdutoPactoCompraDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Long qtdQtdPorCupomDescontoSucesso(ProdutoPactoCupomDesconto cupom, Boolean sucesso) throws ServiceException {
        try {
            return getProdutoPactoCompraDao().qtdQtdPorCupomDescontoSucesso(cupom, sucesso);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Long qtdQtdPorProdutoPacto(ProdutoPacto produtoPacto) throws ServiceException {
        try {
            return getProdutoPactoCompraDao().qtdQtdPorProdutoPacto(produtoPacto);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProdutoPactoCompraDao getProdutoPactoCompraDao() {
        return produtoPactoCompraDao;
    }

    public void setProdutoPactoCompraDao(ProdutoPactoCompraDao produtoPactoCompraDao) {
        this.produtoPactoCompraDao = produtoPactoCompraDao;
    }

    public List<ProdutoPactoCompra> consultar(ProdutoPacto produto, TipoProdutoPacto tipoProdutoPacto, Boolean sucesso) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();
            sql.append("SELECT obj FROM ProdutoPactoCompra obj WHERE 1 = 1 ");

            if (produto != null) {
                sql.append(" AND obj.produtoPacto = :produto ");
                params.put("produto", produto);
            }

            if (tipoProdutoPacto != null) {
                sql.append(" AND obj.tipoProdutoPacto = :tipoProdutoPacto ");
                params.put("tipoProdutoPacto", tipoProdutoPacto);
            }

            if (sucesso != null) {
                sql.append(" AND obj.sucesso = :sucesso ");
                params.put("sucesso", sucesso);
            }

            sql.append(" AND obj.excluido = false ");
            return getProdutoPactoCompraDao().findByParam(sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public boolean jaComprou(String chave, Integer codigoFinanceiro, ProdutoPacto produto, ProdutoPacto produtoDiferente,
                             TipoProdutoPacto tipoProdutoPacto, Boolean sucesso) throws ServiceException {
        try {

            StringBuilder sql = new StringBuilder();
            sql.append("select exists(\n");
            sql.append("select  \n");
            sql.append("pc.id \n");
            sql.append("from produtopactocompra pc \n");
            sql.append("left join produtopactocompra_empresafinanceiro pce on pce.produtopactocompra_id = pc.id \n");
            sql.append("left join produtopactocompra_chave pcc on pcc.produtopactocompra_id = pc.id \n");
            sql.append("where pc.excluido = false \n");

            if (!UteisValidacao.emptyString(chave)) {
                sql.append("and pcc.chave = '").append(chave).append("' \n");
            }

            if (!UteisValidacao.emptyNumber(codigoFinanceiro)) {
                sql.append("and pce.empresafinanceiro = ").append(codigoFinanceiro).append(" \n");
            }

            if (produto != null) {
                sql.append("and pc.produtopacto_id = ").append(produto.getId()).append(" \n");
            }

            if (produtoDiferente != null) {
                sql.append("and pc.produtopacto_id <> ").append(produtoDiferente.getId()).append(" \n");
            }

            if (tipoProdutoPacto != null) {
                sql.append("and pc.tipoprodutopacto = ").append(tipoProdutoPacto.ordinal()).append(" \n");
            }

            if (sucesso != null) {
                sql.append("and pc.sucesso = ").append(sucesso).append(" \n");
            }
            sql.append(") as existe \n");

            ResultSet rs = getProdutoPactoCompraDao().createStatement(sql.toString());
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
            return false;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
