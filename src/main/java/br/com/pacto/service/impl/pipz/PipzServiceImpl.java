package br.com.pacto.service.impl.pipz;

import br.com.pacto.bean.pipz.Pipz;
import br.com.pacto.dao.intf.pipz.PipzDao;
import br.com.pacto.controller.json.pipz.exception.PipzException;
import br.com.pacto.service.intf.pipz.PipzService;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.sql.ResultSet;

@Service
public class PipzServiceImpl implements PipzService {

    private int result;

    @Autowired
    private PipzDao pipzDao;

    @Override
    public Pipz alterarConfiguracoesPipz(Pipz pipz) throws Exception {

        if ((pipz.getUrlPost() == null) || (pipz.getUrlPost().trim().equals(""))){
            throw new PipzException("Informe a URL do Web Service do Pipz");
        }
        ResultSet resultado = getPipzDao().createStatement("Select count(*) as registros from Pipz");
        resultado.next();
        result = resultado.getInt("registros");
        if (result == 0) {
            pipz = getPipzDao().insert(pipz);
        } else {
            pipz = getPipzDao().update(pipz);
        }
        return pipz;
    }

    public PipzDao getPipzDao() {
        return pipzDao;
    }

    public void setPipzDao(PipzDao pipzDao) {
        this.pipzDao = pipzDao;
    }

    public Pipz retornarConfiguracaoPipz() throws Exception {
        ResultSet resultado = getPipzDao().createStatement("Select * from Pipz");
        if (resultado.next()) {
            Pipz pipz = new Pipz();
            pipz.setCodigo(resultado.getInt("codigo"));
            pipz.setUrlPost(resultado.getString("urlpost"));
            pipz.setChaveZillyon(resultado.getString("chavezillyon"));
            pipz.setChaveTreino(resultado.getString("chavetreino"));
            pipz.setChaveUcp(resultado.getString("chaveucp"));
            pipz.setChaveSite(resultado.getString("chavesite"));
            pipz.setChaveBlog(resultado.getString("chaveblog"));
            pipz.setChaveNFSE(resultado.getString("chavenfse"));
            pipz.setChaveAulaCheia(resultado.getString("chaveaulacheia"));
            pipz.setChaveCentralDeEventos(resultado.getString("chavecentraldeeventos"));
            pipz.setChaveCRM(resultado.getString("chavecrm"));
            pipz.setChaveFinanceiro(resultado.getString("chavefinanceiro"));
            pipz.setChaveGameOfResults(resultado.getString("chavegameofresults"));
            pipz.setChaveStudio(resultado.getString("chavestudio"));
            pipz.setChaveGestaoDePersonal(resultado.getString("chavegestaodepersonal"));
            pipz.setChaveSmartBox(resultado.getString("chavesmartbox"));
            pipz.setChaveZillyonAutoAtendimento(resultado.getString("chavezillyonautoatendimento"));
            pipz.setChaveNovoTreino(resultado.getString("chavenovotreino"));
            return pipz;
        }
        return null;
    }
    public String enviarEventoPipzAPI(Pipz configPipz, String evento) throws Exception{
        final CloseableHttpClient client = HttpClientBuilder.create().build();
        final HttpPost post = new HttpPost(configPipz.getUrlPost());
        HttpEntity entity = new ByteArrayEntity(evento.getBytes("UTF-8"));
        post.setEntity(entity);

        post.addHeader("Content-Type", "application/json");

        final HttpResponse respostaHttp = client.execute(post);
        final BufferedReader rd = new BufferedReader(new InputStreamReader(respostaHttp.getEntity().getContent()));
        final StringBuilder respostaString = new StringBuilder();

        String linha = "";
        while ((linha = rd.readLine()) != null) {
            respostaString.append(linha).append("\n");
        }
        return respostaString.toString();
    }
}
