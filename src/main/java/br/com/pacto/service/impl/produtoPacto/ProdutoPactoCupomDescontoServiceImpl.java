package br.com.pacto.service.impl.produtoPacto;

import br.com.pacto.bean.produtoPacto.ProdutoPacto;
import br.com.pacto.bean.produtoPacto.ProdutoPactoCupomDesconto;
import br.com.pacto.dao.intf.produtoPacto.ProdutoPactoCompraDao;
import br.com.pacto.dao.intf.produtoPacto.ProdutoPactoCupomDescontoDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.produtoPacto.ProdutoPactoCupomDescontoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Qualifier(value = "produtoPactoCupomDescontoService")
public class ProdutoPactoCupomDescontoServiceImpl implements ProdutoPactoCupomDescontoService {

    @Autowired
    private ProdutoPactoCupomDescontoDao produtoPactoCupomDescontoDao;
    @Autowired
    private ProdutoPactoCompraDao produtoPactoCompraDao;

    public ProdutoPactoCupomDesconto inserir(ProdutoPactoCupomDesconto object) throws ServiceException {
        try {
            return getProdutoPactoCupomDescontoDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProdutoPactoCupomDesconto alterar(ProdutoPactoCupomDesconto object) throws ServiceException {
        try {
            return getProdutoPactoCupomDescontoDao().update(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(ProdutoPactoCupomDesconto object) throws ServiceException {
        try {
            getProdutoPactoCupomDescontoDao().delete(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProdutoPactoCupomDesconto> obterTodos(boolean consultarUtilizado) throws ServiceException {
        try {
            List<ProdutoPactoCupomDesconto> lista = getProdutoPactoCupomDescontoDao().findAll();
            if (consultarUtilizado) {
                for (ProdutoPactoCupomDesconto obj : lista) {
                    obj.setQtdUtilizada(produtoPactoCompraDao.qtdQtdPorCupomDescontoSucesso(obj, true).intValue());
                }
            }
            return lista;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProdutoPactoCupomDesconto obterPorId(Integer id) throws ServiceException {
        try {
            return getProdutoPactoCupomDescontoDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProdutoPactoCupomDesconto obterPorCupomDesconto(String cupom) throws ServiceException {
        try {
            return getProdutoPactoCupomDescontoDao().obterPorCupomDesconto(cupom);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ProdutoPactoCupomDescontoDao getProdutoPactoCupomDescontoDao() {
        return produtoPactoCupomDescontoDao;
    }

    public void setProdutoPactoCupomDescontoDao(ProdutoPactoCupomDescontoDao produtoPactoCupomDescontoDao) {
        this.produtoPactoCupomDescontoDao = produtoPactoCupomDescontoDao;
    }
}
