package br.com.pacto.service.impl.usuarioapp;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.feed.usuarioapp.UsuarioApp;
import br.com.pacto.bean.usuario.to.UsuarioAppSincronizarTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.usuarioapp.UsuarioAppService;
import br.com.pacto.service.intf.usuarioapp.UsuarioAppSincroniaService;
import br.com.pacto.util.UtilContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class UsuarioAppSincroniaServiceImpl implements UsuarioAppSincroniaService {

    private ConcurrentHashMap<String, Empresa> empresas = new ConcurrentHashMap<>();
    private ConcurrentLinkedQueue<UsuarioAppSincronizarTO> usuariosSincronizar = new ConcurrentLinkedQueue<>();
    @Autowired
    private UsuarioAppService usuarioAppService;

    public ConcurrentHashMap<String, Empresa> getEmpresas() {
        return empresas;
    }

    public Empresa enfileirarUsuario(final String ctx, final String email, final String cpf,
                                     final String telefone, final String dataNascimento, final String senha) throws ServiceException {

        UsuarioAppSincronizarTO usuarioAppSincronizarTO = new UsuarioAppSincronizarTO(ctx, email, cpf, telefone, dataNascimento, senha);
        usuariosSincronizar.add(usuarioAppSincronizarTO);

        Empresa empresaReturn = empresas.get(ctx);
        if (empresaReturn == null) {
            EmpresaService es = UtilContext.getBean(EmpresaService.class);
            Logger.getLogger(UsuarioAppSincroniaServiceImpl.class.getName()).log(Level.INFO,
                    "Descobrindo empresa {0}", new Object[]{ctx});
            empresaReturn = es.descobrir(ctx);
            empresas.put(ctx, empresaReturn);
        }

        return empresaReturn;
    }

    public void preencherUsuario(String cpf, String telefone, String dataNascimento, String senha, UsuarioApp usuario, Empresa emp) {
        usuario.setEmpresa(emp);
        usuario.preencherUsuario(cpf, telefone, dataNascimento, senha);
    }

    public void organizarQueue(Queue<UsuarioAppSincronizarTO> filaDeTrabalho) {
        int i = 0;
        while (i < filaDeTrabalho.size()) {
            if (!filaDeTrabalho.element().isFailOver() && filaDeTrabalho.element().isProcessado())
                Logger.getLogger(UsuarioAppSincroniaServiceImpl.class.getName()).log(Level.INFO,
                        "Removendo item processado {0} tentativa {1}",
                        new Object[]{filaDeTrabalho.element().getEmail(), filaDeTrabalho.element().getTentativas()});
                filaDeTrabalho.poll();
            i++;
        }

        Logger.getLogger(UsuarioAppSincroniaServiceImpl.class.getName()).log(Level.INFO,
                "Itens Pendentes {0}",
                new Object[]{filaDeTrabalho.size()});

    }

    public ConcurrentLinkedQueue<UsuarioAppSincronizarTO> getUsuariosSincronizar() {
        return usuariosSincronizar;
    }

}
