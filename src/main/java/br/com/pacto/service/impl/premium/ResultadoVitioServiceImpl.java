package br.com.pacto.service.impl.premium;

import br.com.pacto.bean.premium.ResultadoVitio;
import br.com.pacto.dao.intf.premium.ResultadoVitioDao;
import br.com.pacto.json.ResultadoVitioJSON;
import br.com.pacto.service.intf.premium.ResultadoVitioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ResultadoVitioServiceImpl implements ResultadoVitioService {

    @Autowired
    private ResultadoVitioDao resultadoVitioDao;

    @Override
    public boolean existeAceiteEmpresa(String chave) throws Exception {
        ResultadoVitio resultadoVitio = resultadoVitioDao.findByChaveAndAceite(chave);
        return resultadoVitio != null;
    }

    @Override
    public boolean existeRespostaVitioUsuario(String chave, Integer codUsuario) throws Exception {
        ResultadoVitio resultadoVitio = resultadoVitioDao.findByChaveAndUsuario(chave, codUsuario);
        return resultadoVitio != null;
    }

    @Override
    public ResultadoVitio salvar(ResultadoVitioJSON resultadoVitioJSON) throws Exception {
        ResultadoVitio resultadoVitio = new ResultadoVitio();

        resultadoVitio.setCodUsuario(resultadoVitioJSON.getCodUsuario());
        resultadoVitio.setNome(resultadoVitioJSON.getNome());
        resultadoVitio.setTelefone(resultadoVitioJSON.getTelefone());
        resultadoVitio.setEmail(resultadoVitioJSON.getEmail());
        resultadoVitio.setIp(resultadoVitioJSON.getIp());
        resultadoVitio.setChave(resultadoVitioJSON.getChave());
        resultadoVitio.setDataResposta(resultadoVitioJSON.getDataResposta());
        resultadoVitio.setStatus(resultadoVitioJSON.getStatus());
        resultadoVitio.setMotivoRecusa(resultadoVitioJSON.getMotivoRecusa());

        return resultadoVitioDao.insert(resultadoVitio);
    }
}
