package br.com.pacto.service.impl.redeEmpresa;

import br.com.pacto.bean.empresa.redeempresa.UsuarioTelefoneEmpresa;
import br.com.pacto.dao.intf.redeempresa.UsuarioTelefoneEmpresaDao;
import br.com.pacto.service.intf.redeempresa.UsuarioTelefoneEmpresaService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON> on 10/03/2017.
 */
@Service
public class UsuarioTelefoneEmpresaServiceImpl implements UsuarioTelefoneEmpresaService {

    @Autowired
    UsuarioTelefoneEmpresaDao usuarioTelefoneEmpresaDao;

    public String persistirTelefones(String key, Integer codEmpresa, Integer codUsuario, String jsonTelefones) {

        try {
            usuarioTelefoneEmpresaDao.deleteComParam(new String[]{"chave", "codUsuario", "codEmpresa"}, new Object[]{key, codUsuario, codEmpresa});
        } catch (Exception e) {
            e.printStackTrace();
        }

        JSONArray telefones = new JSONArray(jsonTelefones);
        for (int i = 0; i < telefones.length(); i++) {
            JSONObject obj = telefones.getJSONObject(i);
            String telefone = obj.getString("telefone");
            telefone = telefone.replaceFirst(" ", "+");
            UsuarioTelefoneEmpresa usuarioTelefoneEmpresa = new UsuarioTelefoneEmpresa();
            usuarioTelefoneEmpresa.setChave(key);
            usuarioTelefoneEmpresa.setCodUsuario(codUsuario);
            usuarioTelefoneEmpresa.setCodEmpresa(codEmpresa);
            usuarioTelefoneEmpresa.setTelefone(telefone);

            try {
                usuarioTelefoneEmpresaDao.insert(usuarioTelefoneEmpresa);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return "ok";
    }

}
