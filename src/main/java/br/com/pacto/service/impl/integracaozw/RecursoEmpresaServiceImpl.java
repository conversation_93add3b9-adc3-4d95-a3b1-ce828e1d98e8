package br.com.pacto.service.impl.integracaozw;

import br.com.pacto.bean.integracaozw.RecursoEmpresa;
import br.com.pacto.dao.intf.integracaozw.RecursoEmpresaDao;
import br.com.pacto.service.intf.integracaozw.RecursoEmpresaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RecursoEmpresaServiceImpl implements RecursoEmpresaService {
    @Autowired
    RecursoEmpresaDao recursoEmpresaDao;

    @Override
    public void incluir(RecursoEmpresa recursoEmpresa) throws Exception {
        recursoEmpresaDao.insert(recursoEmpresa);
    }
}
