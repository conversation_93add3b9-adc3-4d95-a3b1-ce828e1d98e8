package br.com.pacto.service.impl.canalCliente;

import br.com.pacto.bean.canalCliente.*;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.dao.intf.canalCliente.ResponsavelDao;
import br.com.pacto.dao.intf.empresafinanceiro.EmpresaFinanceiroDao;
import br.com.pacto.service.intf.canalCliente.ResponsavelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.NoResultException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ResponsavelServiceImpl implements ResponsavelService {

    @Autowired
    private ResponsavelDao responsavelDao;
    @Autowired
    private EmpresaFinanceiroDao empresaFinanceiroDao;


    private List<Responsavel> atribuirDadosTransient(List<Responsavel> responsaveis) {
        for (Responsavel resp: responsaveis){
            atribuirDadosTransient(resp);
        }

        return responsaveis;
    }

    private Responsavel atribuirDadosTransient(Responsavel responsavel){
        responsavel.setEmpresaFinanceiroCodigo(responsavel.getEmpresaFinanceiro().getCodigo());
        return responsavel;
    }

    @Override
    public List<Responsavel> consultarPorChave(String chave) throws Exception {
        return this.atribuirDadosTransient(responsavelDao.consultarPorChave(chave));
    }

    @Override
    public Responsavel obterPorCodigo(Integer codigo) throws Exception {
        return this.atribuirDadosTransient(responsavelDao.obterPorCodigo(codigo));
    }

    @Override
    public List<Responsavel> alterarResponsaveis(List<Responsavel> responsaveis) throws Exception {

        List<Responsavel> responsaveisAlterados = new ArrayList<>();

        for (Responsavel responsavel: responsaveis){
            EmpresaFinanceiro empresaFinanceiro = empresaFinanceiroDao.findById(responsavel.getEmpresaFinanceiroCodigo());
            responsavel.setEmpresaFinanceiro(empresaFinanceiro);

            if(responsavel.getCodigo() != null && responsavel.getCodigo() > 0 ){
                Responsavel responsavelAtual = responsavelDao.obterPorCodigo(responsavel.getCodigo());
                responsavelDao.setChangedValues(responsavelAtual, responsavel);
                responsavelAtual.setAlteradoEm(new Date());
                responsaveisAlterados.add(responsavelDao.update(responsavelAtual));
            }else{
                try {
                    Responsavel responsavelAtual = responsavelDao.obterResponsavelPorTipo(responsavel.getTipo(), empresaFinanceiro.getCodigo());
                    responsavelDao.setChangedValues(responsavelAtual, responsavel);
                    responsavel.setAlteradoEm(new Date());
                    responsaveisAlterados.add(responsavelDao.update(responsavelAtual));
                }catch (NoResultException e){
                    responsaveisAlterados.add(responsavelDao.insert(responsavel));
                }
            }
        }

        return this.atribuirDadosTransient(responsaveisAlterados);
    }

    @Override
    public List<Responsavel> consultarPorEmpresaFinanceiro(Integer empresaFinanceiro) {
        return this.atribuirDadosTransient(responsavelDao.consultarPorEmpresaFinanceiro(empresaFinanceiro));
    }
}
