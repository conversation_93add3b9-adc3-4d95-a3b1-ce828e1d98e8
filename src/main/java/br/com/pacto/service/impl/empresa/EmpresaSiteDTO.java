/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.empresa;

import br.com.pacto.bean.empresa.EmpresaSite;
import br.com.pacto.objeto.Uteis;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class EmpresaSiteDTO {

    private Integer codigo;
    private String nomeResponsavel;
    private Date dataNasc;
    private String sexo;
    private String cpf;
    private String nomeFantasia;
    private String razaoSocial;
    private String cnpj;
    private String estado;
    private String cidade;
    private String email;
    private String telFixo;
    private String telCelular;
    private Date dataRegistro;
    private String dataCadastro;
    private Date dataAtivacao;
    private Boolean cadastrarNoFinanceiro;
    private boolean disponivel = true;
    private String codIndicacao;
    private Integer idIndicado;
    private String senha;
    private String cobranca;
    private String statusCobranca;
    private String matriculaCobranca;
    private String nomeapp;
    private String utmSource;
    private String utmMedium;
    private String utmCampaign;
    private String erroCadastro;

    public EmpresaSiteDTO(EmpresaSite empresaSite){
        this.codigo = empresaSite.getCodigo();
        this.nomeResponsavel = empresaSite.getNomeResponsavel();
        this.dataNasc = empresaSite.getDataNasc();
        this.sexo = empresaSite.getSexo();
        this.cpf = empresaSite.getCpf();
        this.nomeFantasia = empresaSite.getNomeFantasia();
        this.razaoSocial = empresaSite.getRazaoSocial();
        this.cnpj = empresaSite.getCnpj();
        this.estado = empresaSite.getEstado();
        this.cidade = empresaSite.getCidade();
        this.email = empresaSite.getEmail();
        this.telFixo = empresaSite.getTelFixo();
        this.telCelular = empresaSite.getTelCelular();
        this.dataRegistro = empresaSite.getDataRegistro();
        if (null != empresaSite.getDataCadastro()){
            this.dataCadastro = Uteis.getDataAplicandoFormatacao(empresaSite.getDataCadastro(), "dd 'de' MMMM 'de' yyyy");
        }
        this.dataAtivacao = empresaSite.getDataAtivacao();
        this.cadastrarNoFinanceiro = empresaSite.getCadastrarNoFinanceiro();
        this.disponivel = empresaSite.isDisponivel();
        this.codIndicacao = empresaSite.getCodIndicacao();
        this.idIndicado = empresaSite.getIdIndicado();
        this.senha = empresaSite.getSenha();
        this.statusCobranca = empresaSite.getStatusCobranca();
        this.matriculaCobranca = empresaSite.getMatriculaCobranca();
        this.nomeapp = empresaSite.getNomeapp();
        this.utmSource = empresaSite.getUtmSource();
        this.utmMedium = empresaSite.getUtmMedium();
        this.utmCampaign = empresaSite.getUtmCampaign();
        this.erroCadastro = empresaSite.getErroCadastro();
        try {
            this.cobranca = empresaSite.getConfigAssinatura().getTipo();
        }catch (Exception e){
            this.cobranca = "pacto";
        }
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getNomeResponsavel() {
        return nomeResponsavel;
    }

    public void setNomeResponsavel(String nomeResponsavel) {
        this.nomeResponsavel = nomeResponsavel;
    }

    public Date getDataNasc() {
        return dataNasc;
    }

    public void setDataNasc(Date dataNasc) {
        this.dataNasc = dataNasc;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelFixo() {
        return telFixo;
    }

    public void setTelFixo(String telFixo) {
        this.telFixo = telFixo;
    }

    public String getTelCelular() {
        return telCelular;
    }

    public void setTelCelular(String telCelular) {
        this.telCelular = telCelular;
    }

    public String getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(String dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataAtivacao() {
        return dataAtivacao;
    }

    public void setDataAtivacao(Date dataAtivacao) {
        this.dataAtivacao = dataAtivacao;
    }

    public Boolean getCadastrarNoFinanceiro() {
        if(cadastrarNoFinanceiro == null){
            cadastrarNoFinanceiro = Boolean.FALSE;
        }
        return cadastrarNoFinanceiro;
    }

    public void setCadastrarNoFinanceiro(Boolean cadastrarNoFinanceiro) {
        this.cadastrarNoFinanceiro = cadastrarNoFinanceiro;
    }

    public boolean isDisponivel() {
        return disponivel;
    }

    public void setDisponivel(boolean disponivel) {
        this.disponivel = disponivel;
    }

    public String getCodIndicacao() {
        return codIndicacao;
    }

    public void setCodIndicacao(String codIndicacao) {
        this.codIndicacao = codIndicacao;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getMatriculaCobranca() {
        return matriculaCobranca;
    }

    public void setMatriculaCobranca(String matriculaCobranca) {
        this.matriculaCobranca = matriculaCobranca;
    }

    public String getNomeapp() {
        return nomeapp;
    }

    public void setNomeapp(String nomeapp) {
        this.nomeapp = nomeapp;
    }

    public String getUtmSource() {
        return utmSource;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    public String getUtmMedium() {
        return utmMedium;
    }

    public void setUtmMedium(String utmMedium) {
        this.utmMedium = utmMedium;
    }

    public String getUtmCampaign() {
        return utmCampaign;
    }

    public void setUtmCampaign(String utmCampaign) {
        this.utmCampaign = utmCampaign;
    }

    public String getStatusCobranca() {
        return statusCobranca;
    }

    public void setStatusCobranca(String statusCobranca) {
        this.statusCobranca = statusCobranca;
    }

    public Integer getIdIndicado() {
        return idIndicado;
    }

    public void setIdIndicado(Integer idIndicado) {
        this.idIndicado = idIndicado;
    }

    public String getErroCadastro() {
        return erroCadastro;
    }

    public void setErroCadastro(String erroCadastro) {
        this.erroCadastro = erroCadastro;
    }

    public String getCobranca() {
        return cobranca;
    }

    public void setCobranca(String cobranca) {
        this.cobranca = cobranca;
    }
}
