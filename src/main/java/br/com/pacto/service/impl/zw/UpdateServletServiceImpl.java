package br.com.pacto.service.impl.zw;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.objeto.HttpRequestUtil;
import br.com.pacto.service.intf.zw.ConsultaJSON;
import br.com.pacto.service.intf.zw.UpdateServletService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Service
public class UpdateServletServiceImpl implements UpdateServletService {

    @Override
    public Object selectOne(final Empresa params, final String sql, ConsultaJSON consultaJSON, final String token) throws JSONException, IOException {
        String roboControle = params.getRoboControle();
        Map<String, String> p = new HashMap<String, String>();
        p.put("hostPG", params.getHostBD());
        p.put("portaPG", params.getPorta().toString());
        p.put("userPG", params.getUserBD());
        p.put("pwdPG", params.getPasswordBD());
        p.put("bd", params.getNomeBD());
        p.put("format", "json");
        p.put("sql", sql);
        p.put("op", "selectONE");
        p.put("lgn", token);

        JSONObject objReturn;
        try {
            String result = HttpRequestUtil.executeRequestInner(roboControle + "/UpdateServlet", p, 15000, 5000, "UTF-8");
            objReturn = new JSONObject(result);
        } catch (Exception ex) {
            objReturn = new JSONObject("{\"result\": []}");
        }
        return consultaJSON.consultar(objReturn.getString("result"));
    }
}
