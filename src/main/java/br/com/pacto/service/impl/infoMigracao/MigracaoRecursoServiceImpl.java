package br.com.pacto.service.impl.infoMigracao;

import br.com.pacto.bean.infoMigracao.MigracaoRecurso;
import br.com.pacto.dao.intf.infoMigracao.MigracaoRecursoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.infoMigracao.MigracaoRecursoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.TipoInfoMigracaoEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MigracaoRecursoServiceImpl implements MigracaoRecursoService {

    @Autowired
    private MigracaoRecursoDao dao;

    public MigracaoRecurso inserir(MigracaoRecurso object) throws ServiceException {
        try {
            return getDao().insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public MigracaoRecurso save(MigracaoRecurso object) throws ServiceException {
        try {
            return getDao().update(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public MigracaoRecurso obterPorId(Integer id) throws ServiceException {
        try {
            return getDao().findById(id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<MigracaoRecurso> consultarTodos(Boolean ativo) throws ServiceException {
        try {
            String sql = "SELECT obj FROM MigracaoRecurso obj ";
            if (ativo != null && ativo) {
                sql += "WHERE obj.ativo = :ativo";
            }
            Map<String, Object> params = new HashMap<String, Object>();
            if (ativo != null) {
                params.put("ativo", ativo);
            }
            return getDao().findByParam(sql, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Date obterDataMigracao(String chave, String recurso) throws Exception {
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada | " + chave);
            }

            TipoInfoMigracaoEnum tipoInfoMigracaoEnum = null;
            try {
                tipoInfoMigracaoEnum = TipoInfoMigracaoEnum.valueOf(recurso.toUpperCase());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (tipoInfoMigracaoEnum == null) {
                throw new Exception("TipoInfoMigracaoEnum não identificado | " + recurso);
            }
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("e.chave,\n");
            sql.append("m.datamigracao \n");
            sql.append("from empresa e \n");
            sql.append("left join migracaorecurso_zonas mz on mz.zonas = e.infoinfra \n");
            sql.append("left join migracaorecurso m on mz.migracaorecurso_id = m.id \n");
            sql.append("where m.ativo \n");
            sql.append("and UPPER(e.chave) = '").append(chave.toUpperCase()).append("' \n");
            sql.append("and m.tipoinfomigracaoid = ").append(tipoInfoMigracaoEnum.getId());
            sql.append("order by m.id desc ");
            try (ResultSet rs = getDao().createStatement(sql.toString())) {
                if (rs.next()) {
                    Date data = rs.getDate("datamigracao");
                    return Calendario.ultimaHoraDia(data);
                }
            }
            return null;
    }

    public MigracaoRecursoDao getDao() {
        return dao;
    }

    public void setDao(MigracaoRecursoDao dao) {
        this.dao = dao;
    }
}
