/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass;

import br.com.pacto.bean.gympass.GymPassBooking;
import br.com.pacto.controller.gympass.ThreadGympass;
import br.com.pacto.dao.intf.gympass.GymPassBookingDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gympass.GymPassBookingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 23/03/2020
 */
@Service
public class GymPassBookingServiceImpl implements GymPassBookingService {

    @Autowired
    private GymPassBookingDao gymPassBookingDao;
    @Autowired
    private EmpresaService empresaService;

    public GymPassBookingServiceImpl() {
    }

    @Override
    public void inserir(GymPassBooking object) throws ServiceException {
        try {
            gymPassBookingDao.insert(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void alterar(GymPassBooking object) throws ServiceException {
        try {
            gymPassBookingDao.update(object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public GymPassBooking obterPorId(Integer codigo) throws ServiceException {
        try {
            return gymPassBookingDao.findObjectByAttribute("codigo", codigo);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void enviarTreinoWeb(GymPassBooking obj) {
        try {
            ThreadGympass thread = new ThreadGympass(obj);
            thread.setDaemon(true);
            thread.start();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
