package br.com.pacto.service.impl.redeEmpresa;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.redeempresa.UsuarioEmpresaFinanceiro;
import br.com.pacto.bean.empresa.redeempresa.UsuarioEmpresaUnidade;
import br.com.pacto.bean.empresa.redeempresa.UsuarioRedeEmpresa;
import br.com.pacto.bean.empresa.redeempresa.UsuarioTelefoneEmpresa;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.jsf.base.MenuControle;
import br.com.pacto.dao.intf.redeempresa.RedeEmpresaDao;
import br.com.pacto.dao.intf.redeempresa.UsuarioRedeEmpresaDao;
import br.com.pacto.dao.intf.redeempresa.UsuarioTelefoneEmpresaDao;
import br.com.pacto.integracao.IntegracaoCadastrosWSConsumer;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.empresafinanceiro.EmpresaFinanceiroService;
import br.com.pacto.service.intf.oamd.OAMDService;
import br.com.pacto.service.intf.redeempresa.UsuarioEmpresaFinanceiroService;
import br.com.pacto.service.intf.redeempresa.UsuarioRedeEmpresaService;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * Created by Rafael on 10/03/2017.
 */
@Service
public class UsuarioRedeEmpresaServiceImpl implements UsuarioRedeEmpresaService {

    @Autowired
    UsuarioRedeEmpresaDao usuarioRedeEmpresaDao;

    @Autowired
    UsuarioEmpresaFinanceiroService usuarioEmpresaFinanceiroDao;

    @Autowired
    OAMDService oamdService;

    @Autowired
    EmpresaService empresaService;

    @Autowired
    EmpresaFinanceiroService empresaFinanceiroService;

    @Autowired
    RedeEmpresaDao redeEmpresaDao;

    @Autowired
    UsuarioTelefoneEmpresaDao usuarioTelefoneEmpresaDao;

    public UsuarioRedeEmpresa descobrirEmpresaEmail(String email) throws Exception{
        return usuarioRedeEmpresaDao.findObjectByAttribute("email",email.toLowerCase());
    }

    public JSONObject descobrirUsuarioApp(String telefone) throws Exception{
        return validarUsuarioApp(telefone);
    }

    private JSONObject validarUsuarioApp(String telefone) throws Exception {
        if (UteisValidacao.emptyString(telefone)) {
            throw new Exception("Telefone não informado");
        }
        telefone = telefone.trim().replace("+", "");
        Map<String, Object> params = new HashMap<>();

        StringBuilder query = new StringBuilder();
        query.append("WHERE obj.telefone like '%").append(telefone).append("'");

        List<UsuarioTelefoneEmpresa> usuarios = usuarioTelefoneEmpresaDao.findByParam(query, params);

        boolean usuarioEncontrado = false;
        JSONArray chaves = new JSONArray();
        for (UsuarioTelefoneEmpresa usuarioTelefoneEmpresa : usuarios) {
            String chave = usuarioTelefoneEmpresa.getChave();
            Empresa emp = empresaService.obterObjetoPorChave(chave);
            if (emp != null) {
                try {

                    EmpresaFinanceiro empresaFinanceiro = empresaFinanceiroService.obterEmpresa(usuarioTelefoneEmpresa.getCodEmpresa(), usuarioTelefoneEmpresa.getChave());
                    if (empresaFinanceiro == null || !empresaFinanceiro.getAtivaZw()) {
                        continue;
                    }

                    JSONObject empresaOAMD = validarUsuario(chave, emp.getRoboControleSemHTTPS(), usuarioTelefoneEmpresa.getCodUsuario());
                    usuarioEncontrado = true;
                    empresaOAMD.put("key", emp.getChave());
                    empresaOAMD.put("modulos", emp.getModulos());
                    empresaOAMD.put("urlTreino", emp.getUrlTreinoWeb());
                    empresaOAMD.put("urlZW", emp.getRoboControleCorrigindoProtocolo());
                    empresaOAMD.put("cod", emp.getRoboControleCorrigindoProtocolo());

                    JSONArray empresaArray = new JSONArray();

                    JSONObject empresa = new JSONObject();
                    empresa.put("key", empresaFinanceiro.getChaveZw());
                    empresa.put("nome", empresaFinanceiro.getNomeResumo());
                    JSONArray unidades = new JSONArray();
                    for (int i = 0; i < empresaOAMD.getJSONArray("empresas").length(); i++) {
                        JSONObject unidade = new JSONObject();
                        unidade.put("codigo", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("codigo"));
                        unidade.put("nome", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("nome"));
                        unidade.put("descricaoPerfil", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("descricaoPerfil"));
                        unidade.put("email", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("email"));
                        unidade.put("cnpj", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("cnpj"));
                        unidade.put("codigoFinanceiro", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("codigoFinanceiro"));
                        unidades.put(unidade);
                    }
                    empresa.put("unidades", unidades);

                    empresaArray.put(empresa);

                    empresaOAMD.put("empresas", empresaArray);

                    chaves.put(empresaOAMD);
                } catch (Exception ignored) {

                }
            }
        }

        if (!usuarioEncontrado) {
            throw new Exception("Usuário não encontrado.");
        }

        JSONObject obj = new JSONObject();
        obj.put("grupo", chaves);
        return obj;
    }

    public JSONObject descobrirUsuarioApp(String email,String senha) throws Exception{
        UsuarioRedeEmpresa usuario  = usuarioRedeEmpresaDao.findObjectByAttribute("lower(email)",email.toLowerCase());
        if(usuario == null){
            throw new Exception("Usuário não encontrado");
        }
        return validarUsuarioApp(usuario.getEmpresaFinanceiro().getChaveZw(),email,senha);
    }

    public JSONObject descobrirUsuarioApp(String key,String email,String senha) throws Exception{
        return validarUsuarioApp(key,email,senha);
    }
    private JSONObject validarUsuarioApp(String key,String email,String senha) throws Exception{
        UsuarioRedeEmpresa usuario  = usuarioRedeEmpresaDao.findObjectByAttribute("lower(email)",email.toLowerCase());
        if(usuario == null){
            throw new Exception("Usuário não encontrado.");
        }
        Empresa emp = empresaService.obterObjetoPorChave(key);
        if(emp == null){
            throw new Exception("Empresa não encontrada.");
        }
        JSONObject obj;
        String requestGame = null;
        try {
            obj = validarUsuario(key,emp.getRoboControleSemHTTPS(),email,senha);
        } catch (Exception e) {
            if (usuario.getEmpresaFinanceiro().getChaveZw().equals(key)) {
                throw e;
            } else {
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", email);
                requestGame = ExecuteRequestHttpService.executeRequest(Aplicacao.getProp(Aplicacao.urlGame)
                        + "/prest/config/obterEmpresasEmail", params);
                JSONObject jsonGame = new JSONObject(requestGame);
                if (jsonGame.has("sucesso")) {
                    Empresa empU = empresaService.obterObjetoPorChave(usuario.getEmpresaFinanceiro().getChaveZw());
                    obj = validarUsuario(usuario.getEmpresaFinanceiro().getChaveZw(), empU.getRoboControleSemHTTPS(), email, senha);
                } else {
                    throw e;
                }
            }
        }
        obj.put("key",emp.getChave());
        obj.put("modulos",emp.getModulos());
        obj.put("urlTreino",emp.getUrlTreinoWeb());
        obj.put("urlZW",emp.getRoboControleCorrigindoProtocolo());
        obj.put("cod",emp.getRoboControleCorrigindoProtocolo());
        obj.put("urlGame",UteisValidacao.emptyString(emp.getUrlGame()) ? Aplicacao.getProp(Aplicacao.urlGame) : emp.getUrlGame());
        List<UsuarioEmpresaFinanceiro> empresas = usuarioEmpresaFinanceiroDao.obterEmpresasRedeUsuario(usuario.getCodigo());
        if(usuario.getRedeEmpresa() != null) {
            montarEmpresasRedeLogin(obj, empresas);
            obj.put("redeEmpresa",true);
        } else {
            montarEmpresasLogin(obj,usuario);
            obj.put("redeEmpresa",false);
        }
        montarEmpresasGrupoGame(requestGame, obj,email);
        return obj;
    }
    
    private JSONObject validarUsuario(final String key,final String url,final String email,final String senha) throws Exception{
        String result = IntegracaoCadastrosWSConsumer.validarUsuarioEmail(key,url,email,senha);
        JSONObject retorno = new JSONObject(result);
        JSONObject obj = null;
        if(retorno.has("ERRO")){
            throw new Exception(retorno.getString("ERRO"));
        } else if(retorno.has("RETURN")){
            obj = retorno.getJSONObject("RETURN");
        } else {
            throw new Exception("Usuário não encontrado.");
        }
        return obj;
    }

    private JSONObject validarUsuario(final String key, final String url, final Integer codUsuario) throws Exception {
        String result = IntegracaoCadastrosWSConsumer.validarUsuarioTelefone(key, url, codUsuario);
        JSONObject retorno = new JSONObject(result);
        JSONObject obj = null;
        if (retorno.has("ERRO")) {
            throw new Exception(retorno.getString("ERRO"));
        } else if (retorno.has("RETURN")) {
            obj = retorno.getJSONObject("RETURN");
        } else {
            throw new Exception("Usuário não encontrado.");
        }
        return obj;
    }

    public JSONObject obterUrlRedirect(String key,String email,String senha,Integer codigoEmpresa, Boolean deslogar) throws Exception{
        Empresa emp = empresaService.obterObjetoPorChave(key);
        JSONObject obj = validarUsuario(key,emp.getRoboControleSemHTTPS(),email,senha);
        obj.put("urlRedirectAdm", formatUrl(emp.getRoboControle(),key,obj.getInt("codigo"),codigoEmpresa,Uteis.URI_REDIRECT_BI_DETALHADO, deslogar));
        obj.put("urlRedirectCRM", formatUrl(emp.getRoboControle(),key,obj.getInt("codigo"),codigoEmpresa,Uteis.URI_REDIRECT_CRM_BI, deslogar));
        obj.put("urlRedirectTreino", formatUrl(emp.getUrlTreino(),key,obj.getInt("codigo"),codigoEmpresa,Uteis.URI_REDIRECT_BI_TREINO, deslogar));
        obj.put("urlRedirectFinan", formatUrl(emp.getRoboControle(),key,obj.getInt("codigo"),codigoEmpresa,Uteis.URI_REDIRECT_FINAN, deslogar));
        obj.put("urlRedirectRankingUCP", formatUrlUCP(Aplicacao.getProp(key, Aplicacao.myUpUrlBase),key,obj.getString("userName"),obj.getString("nome")));
        return obj;
    }
    private String formatUrl(final String urlBase, final String key, final Integer usuarioZW, final Integer empresa, final String uri, boolean deslogar) {
        try {
            return  urlBase + formatUrlRedirect(key,"","",usuarioZW,empresa,uri, deslogar);
        } catch (Exception ex) {
            Logger.getLogger(MenuControle.class.getName()).log(Level.SEVERE, null, ex);
            return "";
        }
    }
    private String formatUrlUCP(final String urlBase , final String key,final String userName,String nome) {
        try {
            return  urlBase + formatUrlRedirectUCP(key,userName,nome);
        } catch (Exception ex) {
            Logger.getLogger(MenuControle.class.getName()).log(Level.SEVERE, null, ex);
            return "";
        }
    }
    private static String formatUrlRedirectUCP(final String key,final String userName,String nome) throws Exception{
        try {
            JSONObject json = new JSONObject();
            json.put("key", key);
            json.put("userName", UteisValidacao.emptyString(userName) ? "0": userName);
            json.put("nomeEmpresa","ACADEMIA");
            json.put("nomeCompleto",nome);
            json.put("telefones","");
            json.put("ranking","true");
            Calendar dataCalendar = Calendar.getInstance();
            dataCalendar.add(Calendar.MINUTE, 3);
            json.put("timevld", dataCalendar.getTimeInMillis());
            String params = "/oid?lgn=" + Uteis.encriptar(json.toString(), "chave_login_unificado");
            return params;
        } catch (Exception ex) {
            Logger.getLogger(MenuControle.class.getName()).log(Level.SEVERE, null, ex);
            return "";
        }
    }
    private static String formatUrlRedirect(final String key,final String userOAMD,final String urlLogin,final Integer usuarioZW,final Integer empresaZW,final String uri, final Boolean deslogar) {
        try {
            JSONObject json = new JSONObject();
            json.put("login", "true");
            json.put("key", key);
            json.put("idUserZW", UteisValidacao.emptyNumber(usuarioZW) ? "0": usuarioZW.toString());
            json.put("idUserTR", UteisValidacao.emptyNumber(usuarioZW) ? "0": usuarioZW.toString());
            json.put("urlRedirect", uri);
            json.put("urlLogin",urlLogin);
            json.put("idEmpresa",empresaZW);
            json.put("userOamd", UteisValidacao.emptyString(userOAMD) ?  "" : userOAMD);
            json.put("dataSistema","");
            json.put("aprensentarTreino", "true");
            Calendar dataCalendar = Calendar.getInstance();
            dataCalendar.add(Calendar.MINUTE, 3);
            json.put("timevld", dataCalendar.getTimeInMillis());
            json.put("deslogar", deslogar);
            String params = "/oid?lgn=" + Uteis.encriptar(json.toString(), "chave_login_unificado");

            return params;
        } catch (JSONException ex) {
            Logger.getLogger(MenuControle.class.getName()).log(Level.SEVERE, null, ex);
            return "";
        }
    }
    
    private void montarEmpresasGrupoGame(String request, JSONObject obj, String email) throws Exception {
        try {
            if(UteisValidacao.emptyString(request)){
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", email);
                request = ExecuteRequestHttpService.executeRequest(Aplicacao.getProp(Aplicacao.urlGame)
                        + "/prest/config/obterEmpresasEmail", params);
            }
            JSONArray arr = obj.getJSONArray("empresas");
            JSONObject json = new JSONObject(request);
            JSONArray empresasGrupo = json.getJSONArray("sucesso");
            if (UteisValidacao.emptyString(request)
                    || empresasGrupo.length() == 0) {
                return;
            }
            for (int i = 0; i < empresasGrupo.length(); i++) {
                JSONObject empresa = new JSONObject();
                empresa.put("key", empresasGrupo.getJSONObject(i).get("chave"));
                Empresa emp = empresaService.obterObjetoPorChave(empresasGrupo.getJSONObject(i).getString("chave"));
                empresa.put("urlTreino", emp == null ? "" : emp.getUrlTreinoWeb());
                empresa.put("nome", empresasGrupo.getJSONObject(i).get("nomeEmpresa"));
                JSONArray unidades = new JSONArray();
                JSONObject unidade = new JSONObject();
                unidade.put("codigo", empresasGrupo.getJSONObject(i).getInt("codigoEmpresa"));
                unidade.put("nome", empresasGrupo.getJSONObject(i).get("nomeEmpresa"));
                unidade.put("descricaoPerfil", "-");
                unidades.put(unidade);
                empresa.put("unidades", unidades);
                arr.put(empresa);
            }
            obj.put("empresas", arr);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

    }
        
    private void montarEmpresasLogin(JSONObject obj, UsuarioRedeEmpresa usuario ) throws Exception{
        JSONObject empresa = new JSONObject();
        empresa.put("key",usuario.getEmpresaFinanceiro().getChaveZw());
        empresa.put("nome",usuario.getEmpresaFinanceiro().getNomeResumo());
        JSONArray unidades = new JSONArray();
        for(int i = 0; i <  obj.getJSONArray("empresas").length(); i++){
            JSONObject unidade = new JSONObject();
            unidade.put("codigo",obj.getJSONArray("empresas").getJSONObject(i).get("codigo"));
            unidade.put("nome",obj.getJSONArray("empresas").getJSONObject(i).get("nome"));
            unidade.put("descricaoPerfil",obj.getJSONArray("empresas").getJSONObject(i).get("descricaoPerfil"));
            unidades.put(unidade);
        }
        empresa.put("unidades",unidades);
        JSONArray array = new JSONArray();
        array.put(empresa);
        obj.put("empresas",array);
    }

    private void montarEmpresasRedeLogin(JSONObject obj, List<UsuarioEmpresaFinanceiro> empresas) throws Exception{
        JSONArray e = new JSONArray();
        List<String> adds = new ArrayList<>();
        for(UsuarioEmpresaFinanceiro o : empresas){
            if(adds.contains(o.getEmpresaFinanceiro().getChaveZw() + "-" + o.getEmpresaFinanceiro().getCodigo())){
                continue;
            }
            JSONObject empresa = new JSONObject();
            adds.add(o.getEmpresaFinanceiro().getChaveZw() + "-" + o.getEmpresaFinanceiro().getCodigo());
            empresa.put("key",o.getEmpresaFinanceiro().getChaveZw());
            empresa.put("nome",o.getEmpresaFinanceiro().getNomeResumo());
            JSONArray unidades = new JSONArray();
            for(UsuarioEmpresaUnidade unidade : o.getUsuarioEmpresaUnidades()){

                JSONObject i = new JSONObject();
                i.put("codigo",unidade.getCodigoZW());
                i.put("nome",unidade.getNome());
                unidades.put(i);
            }
            empresa.put("unidades",unidades);
            e.put(empresa);
        }
        obj.put("empresas",e);
    }

    public void gerarUsuarioRedeEmpresa(String key,String email, final Usuario u) throws Exception {
        UsuarioRedeEmpresa usuario  = usuarioRedeEmpresaDao.findObjectByAttribute("email",email);
        EmpresaFinanceiro empresaFinanceiro = empresaFinanceiroService.obterPorChave(key);
        if(empresaFinanceiro == null){
            throw  new Exception("Não encontrado empresa para a chave especificada");
        }
        if(!UteisValidacao.emptyNumber( empresaFinanceiro.getRedeempresa_id())){
            gerarUsuarioRedeEmpresa(email,usuario,empresaFinanceiro, u);
        }else {
            gerarUsuarioEmpresa(email,usuario,empresaFinanceiro, "", "", "", "");
        }
    }

    private void gerarUsuarioRedeEmpresa(String email,UsuarioRedeEmpresa usuario , EmpresaFinanceiro empresaFinanceiro,
                                         final Usuario u) throws Exception{
        if(usuario == null){
            usuario = new UsuarioRedeEmpresa();
            usuario.setEmail(email.toLowerCase());
            usuario.setRedeEmpresa(redeEmpresaDao.findById(empresaFinanceiro.getRedeempresa_id()));
            usuario.setEmpresaFinanceiro(empresaFinanceiro);
            usuarioRedeEmpresaDao.insert(usuario);
        } else {
            if(!empresaFinanceiro.getRedeempresa_id().equals(usuario.getRedeEmpresa().getId())){
                throw  new Exception("Usuário já vinculado á outroa rede de empresa.");
            }
        }
        List<EmpresaFinanceiro> empresaFinanceiros = empresaFinanceiroService.consultarEmpresasRede(empresaFinanceiro.getRedeempresa_id());
        List<UsuarioEmpresaFinanceiro> empresas = usuarioEmpresaFinanceiroDao.obterEmpresasRedeUsuario(usuario.getCodigo());
        for(UsuarioEmpresaFinanceiro o : empresas){
            usuarioEmpresaFinanceiroDao.delete(o);
        }
        for(EmpresaFinanceiro obj : empresaFinanceiros){
            String chave = obj.getChaveZw();
            StringBuilder sql = new StringBuilder("Select obj from Empresa obj where obj.chave = :chave");
            HashMap<String,Object> param = new HashMap<String, Object>();
            param.put("chave",chave);
            Empresa emp = empresaService.obterObjetoPorParam(sql.toString(),param);
            sql = new StringBuilder();
            sql.append("Select emp.codigo,emp.nome from UsuarioMovel um\n");
            sql.append("INNER JOIN Usuario u ON u.codigo = um.usuarioZW\n");
            sql.append("INNER JOIN UsuarioPerfilAcesso up ON up.usuario = u.codigo\n");
            sql.append("INNER JOIN Empresa emp ON emp.codigo = up.empresa\n");
            sql.append("INNER JOIN UsuarioEmail ue ON ue.usuario = up.usuario\n");
            sql.append("WHERE ue.email = '").append(email).append("' ");
            JSONObject json = null;
            try{
                String r = oamdService.doConsulta(emp,"selectONE",sql.toString(), u);
                json  = new JSONObject(r);
            }catch (Exception ignored){
                continue;
            }
            if(json.getJSONArray("result").length() > 0){
                UsuarioEmpresaFinanceiro usuarioEmpresaFinanceiro =  usuarioEmpresaFinanceiroDao.obterPorUsuarioRede(usuario.getCodigo(),obj.getChaveZw());
                if(usuarioEmpresaFinanceiro == null){
                    usuarioEmpresaFinanceiro = new UsuarioEmpresaFinanceiro();
                    usuarioEmpresaFinanceiro.setEmpresaFinanceiro(obj);
                    usuarioEmpresaFinanceiro.setUsuarioRedeEmpresa(usuario);
                    usuarioEmpresaFinanceiroDao.incluir(usuarioEmpresaFinanceiro);
                }
                for(int i =0; i < json.getJSONArray("result").length(); i++ ){
                    JSONObject o = json.getJSONArray("result").getJSONObject(i);
                    UsuarioEmpresaUnidade usuarioEmpresaUnidade = new UsuarioEmpresaUnidade();
                    usuarioEmpresaUnidade.setCodigoZW(o.getInt("codigo"));
                    usuarioEmpresaUnidade.setNome(o.getString("nome"));
                    usuarioEmpresaUnidade.setUsuarioEmpresaFinanceiro(usuarioEmpresaFinanceiro);
                    if(usuarioEmpresaFinanceiro.getUsuarioEmpresaUnidades() == null){
                        usuarioEmpresaFinanceiro.setUsuarioEmpresaUnidades(new ArrayList<UsuarioEmpresaUnidade>());
                    }
                    usuarioEmpresaFinanceiro.getUsuarioEmpresaUnidades().add(usuarioEmpresaUnidade);
                    usuarioEmpresaFinanceiroDao.update(usuarioEmpresaFinanceiro);
                }
            }
        }
    }

    private void gerarUsuarioEmpresa(String email, UsuarioRedeEmpresa usuario, EmpresaFinanceiro empresaFinanceiro,
                                     String cpf, String telefone, String dataNascimento, String senha) throws Exception {
        if (usuario == null) {
            usuario = new UsuarioRedeEmpresa();
            usuario.setEmail(email);
            usuario.setEmpresaFinanceiro(empresaFinanceiro);
            usuario.preencherUsuario(cpf, telefone, dataNascimento, senha);
            usuarioRedeEmpresaDao.insert(usuario);
        } else {
            if (!usuario.getEmpresaFinanceiro().getCodigo().equals(empresaFinanceiro.getCodigo())) {
                throw new Exception("Email já vinculado a outra empresa.");
            }
            usuario.preencherUsuario(cpf, telefone, dataNascimento, senha);
            usuario.setEmail(email);
            usuarioRedeEmpresaDao.update(usuario);
        }
    }

    public JSONObject descobrirEmpresasUsuarioAppV2(String key, String email, String senha) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("email", email.toUpperCase());

        StringBuilder query = new StringBuilder();
        query.append("WHERE UPPER(obj.email) = :email  and obj.empresaFinanceiro.ativaZw is true ");

        List<UsuarioRedeEmpresa> usuarioRedeEmpresas = usuarioRedeEmpresaDao.findByParam(query, params);
        return descobrirEmpresasUsuarioApp(usuarioRedeEmpresas, email, senha);
    }

    public JSONObject descobrirEmpresasUsuarioAppV3(String key, String email, String senha) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("email", email.toUpperCase());

        StringBuilder query = new StringBuilder();
        query.append("WHERE UPPER(obj.email) = :email \n");

        String cpf = Uteis.tirarCaracteres(email, true);
        if (cpf != null && cpf.length() == 11) {
            params.put("cpf", cpf);
            query.append("OR replace(replace(obj.cpf, '.', ''), '-', '') = :cpf \n");
        }

        String telefone = Uteis.tirarCaracteres(email, true);
        if (telefone != null && telefone.length() >= 10) {
            params.put("tel", "%" + telefone + "%");
            query.append("OR replace(replace(replace(obj.telefone, '(', ''), ')', ''), '-','') like :tel ");
        }

        List<UsuarioRedeEmpresa> usuarioRedeEmpresas = usuarioRedeEmpresaDao.findByParam(query, params);
        return descobrirEmpresasUsuarioApp(usuarioRedeEmpresas, email, senha);
    }

    private JSONObject descobrirEmpresasUsuarioApp(List<UsuarioRedeEmpresa> usuarioRedeEmpresas, String email, String senha) throws Exception {
        if (UteisValidacao.emptyList(usuarioRedeEmpresas)) {
            throw new Exception("Usuário não encontrado.");
        }

        boolean usuarioEncontrado = false;
        JSONArray chaves = new JSONArray();
        for (UsuarioRedeEmpresa usuarioRedeEmpresa : usuarioRedeEmpresas) {
            String chave = usuarioRedeEmpresa.getEmpresaFinanceiro().getChaveZw();
            Empresa emp = empresaService.obterObjetoPorChave(chave);
            if (emp != null) {
                try {
                    JSONObject empresaOAMD = validarUsuario(chave, emp.getRoboControleSemHTTPS(), email, senha);
                    usuarioEncontrado = true;
                    empresaOAMD.put("key", emp.getChave());
                    empresaOAMD.put("modulos", emp.getModulos());
                    empresaOAMD.put("urlTreino", emp.getUrlTreinoWeb());
                    empresaOAMD.put("urlZW", emp.getRoboControleCorrigindoProtocolo());
                    empresaOAMD.put("cod", emp.getRoboControleCorrigindoProtocolo());

                    JSONArray empresaArray = new JSONArray();

                    JSONObject empresa = new JSONObject();
                    empresa.put("key", usuarioRedeEmpresa.getEmpresaFinanceiro().getChaveZw());
                    empresa.put("nome", usuarioRedeEmpresa.getEmpresaFinanceiro().getNomeResumo());
                    JSONArray unidades = new JSONArray();
                    for (int i = 0; i < empresaOAMD.getJSONArray("empresas").length(); i++) {
                        JSONObject unidade = new JSONObject();
                        unidade.put("codigo", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("codigo"));
                        unidade.put("nome", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("nome"));
                        unidade.put("descricaoPerfil", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("descricaoPerfil"));
                        unidade.put("email", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("email"));
                        unidade.put("cnpj", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("cnpj"));
                        unidade.put("codigoFinanceiro", empresaOAMD.getJSONArray("empresas").getJSONObject(i).get("codigoFinanceiro"));
                        unidades.put(unidade);
                    }
                    empresa.put("unidades", unidades);

                    empresaArray.put(empresa);

                    empresaOAMD.put("empresas", empresaArray);

                    chaves.put(empresaOAMD);
                } catch (Exception ignored) {

                }
            }
        }

        if (!usuarioEncontrado) {
            throw new Exception("Usuário não encontrado.");
        }

        JSONObject obj = new JSONObject();
        obj.put("grupo", chaves);
        return obj;
    }

    public void gerarUsuarioRedeEmpresaV2(String key,String email) throws Exception {
        EmpresaFinanceiro empresaFinanceiro = empresaFinanceiroService.obterPorChave(key);
        if (empresaFinanceiro == null) {
            throw new Exception("Não encontrado empresa para a chave especificada");
        }

        UsuarioRedeEmpresa usuario = usuarioRedeEmpresaDao.findObjectByAttributes(new String[]{"email", "empresaFinanceiro"}, new Object[]{email, empresaFinanceiro}, null);
        gerarUsuarioEmpresa(email, usuario, empresaFinanceiro, "", "", "", "");
    }

    public void gerarUsuarioRedeEmpresaV3(String key, String email, String cpf, String telefone, String dataNascimento, String senha, String empFinanceiro) throws Exception {
        EmpresaFinanceiro empresaFinanceiro;
        if (isNotBlank(empFinanceiro)) {
            empresaFinanceiro = empresaFinanceiroService.findByCodFinanceiro(Integer.valueOf(empFinanceiro));
        } else {
            empresaFinanceiro = empresaFinanceiroService.obterPorChave(key);
        }
        if (empresaFinanceiro == null) {
            throw new Exception("Não encontrado empresa para a chave especificada ou codigo financeiro");
        }
        UsuarioRedeEmpresa usuario = usuarioRedeEmpresaDao.findObjectByAttributes(new String[]{"cpf", "datanascimento", "empresaFinanceiro"}, new Object[]{cpf, Calendario.getDate("dd/MM/yyyy", dataNascimento), empresaFinanceiro}, null);
        gerarUsuarioEmpresa(email, usuario, empresaFinanceiro, cpf, telefone, dataNascimento, senha);
    }

    public void gerarUsuarioRedeEmpresaV4(String key, String email, String cpf, String telefone, String dataNascimento, String senha , String empresas) throws Exception {
        List<Integer> companyList = Arrays.stream(empresas.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        List<EmpresaFinanceiro> listaEmpresasFinanceiro = empresaFinanceiroService.consultarEmpresasChaveZwEmpresaZw(key, companyList);
        if (UteisValidacao.emptyList(listaEmpresasFinanceiro)) {
            throw new Exception("Não encontrado empresa para a chave especificada");
        }
        for (EmpresaFinanceiro empresaFinanceiro: listaEmpresasFinanceiro) {
            UsuarioRedeEmpresa usuario = usuarioRedeEmpresaDao.findObjectByAttributes(new String[]{"cpf", "datanascimento", "empresaFinanceiro"}, new Object[]{cpf, Calendario.getDate("dd/MM/yyyy", dataNascimento), empresaFinanceiro}, null);
            gerarUsuarioEmpresa(email, usuario, empresaFinanceiro, cpf, telefone, dataNascimento, senha);
        }
    }
}
