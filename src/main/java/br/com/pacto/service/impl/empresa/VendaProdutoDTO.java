package br.com.pacto.service.impl.empresa;

import org.json.JSONObject;

public class VendaProdutoDTO {

    private Integer produto;
    private String descricao;
    private Integer qtd;
    private Double valorUnitario;
    private String observacao;

    public VendaProdutoDTO() {
    }

    public VendaProdutoDTO(JSONObject json) {
        this.produto = json.optInt("produto");
        this.qtd = json.optInt("qtd");
        this.descricao = json.optString("descricao");
        this.valorUnitario = json.optDouble("valorUnitario");
        this.observacao = json.optString("observacao");
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public Integer getQtd() {
        return qtd;
    }

    public void setQtd(Integer qtd) {
        this.qtd = qtd;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }
}
