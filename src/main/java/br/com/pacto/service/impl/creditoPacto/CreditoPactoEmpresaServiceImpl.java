package br.com.pacto.service.impl.creditoPacto;

import br.com.pacto.bean.creditoPacto.CreditoPacto;
import br.com.pacto.bean.creditoPacto.CreditoPactoEmpresa;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.dao.intf.creditoPacto.CreditoPactoEmpresaDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.creditoPacto.CreditoPactoEmpresaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 25/09/2020.
 */
@Service
@Qualifier(value = "creditoPactoEmpresaService")
public class CreditoPactoEmpresaServiceImpl implements CreditoPactoEmpresaService {

    @Autowired
    private CreditoPactoEmpresaDao creditoPactoEmpresaDao;

    public CreditoPactoEmpresaDao getCreditoPactoEmpresaDao() {
        return creditoPactoEmpresaDao;
    }

    public CreditoPactoEmpresa inserir(CreditoPactoEmpresa object) throws ServiceException {
        try {
            return getCreditoPactoEmpresaDao().insert(object);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public CreditoPactoEmpresa alterar(CreditoPactoEmpresa object) throws ServiceException {
        try {
            return getCreditoPactoEmpresaDao().update(object);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(CreditoPactoEmpresa object) throws ServiceException {
        try {
            getCreditoPactoEmpresaDao().delete(object);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public void excluirPorCreditoPacto(CreditoPacto creditoPacto) throws ServiceException {
        try {
            getCreditoPactoEmpresaDao().deleteComParam(new String[]{"creditoPacto.codigo"}, new Object[]{creditoPacto.getCodigo()});
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public void excluirPorEmpresaFinanceiro(EmpresaFinanceiro empresaFinanceiro) throws ServiceException {
        try {
            getCreditoPactoEmpresaDao().deleteComParam(new String[]{"empresaFinanceiro.codigo"}, new Object[]{empresaFinanceiro.getCodigo()});
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<CreditoPactoEmpresa> obterTodos() throws ServiceException {
        try {
            return getCreditoPactoEmpresaDao().findAll();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public CreditoPactoEmpresa obterPorId(Integer id) throws ServiceException {
        try {
            return getCreditoPactoEmpresaDao().findById(id);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public List<CreditoPactoEmpresa> consultar(CreditoPacto creditoPacto) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();
            sql.append("SELECT obj FROM CreditoPactoEmpresa obj WHERE 1 = 1 ");
            if (creditoPacto != null) {
                sql.append(" AND obj.creditoPacto.codigo = :creditoPacto ");
                params.put("creditoPacto", creditoPacto.getCodigo());
            }
            sql.append(" ORDER BY obj.codigo ");
            return getCreditoPactoEmpresaDao().findByParam(sql.toString(), params);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public CreditoPactoEmpresa consultarPorEmpresaFinanceiro(EmpresaFinanceiro empresaFinanceiro) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            Map<String, Object> params = new HashMap<String, Object>();
            sql.append("SELECT obj FROM CreditoPactoEmpresa obj WHERE 1 = 1 ");
            if (empresaFinanceiro != null) {
                sql.append(" AND obj.empresaFinanceiro.codigo = :empresaFinanceiro ");
                params.put("empresaFinanceiro", empresaFinanceiro.getCodigo());
            }
            return getCreditoPactoEmpresaDao().findObjectByParam(sql.toString(), params);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }
}
