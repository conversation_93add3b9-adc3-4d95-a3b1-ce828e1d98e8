package br.com.pacto.service.impl.empresa;

import br.com.pacto.bean.empresa.ChavesEstrategicas;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.Paths;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.dao.intf.infra.ChavesEstrategicasDao;
import br.com.pacto.enums.Modulo;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.empresafinanceiro.EmpresaFinanceiroService;
import br.com.pacto.service.intf.infra.PathsService;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.HttpServico;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Service
public class DiscoveryService {

    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private PathsService pathsService;
    @Autowired
    private HttpServico httpServico;
    @Autowired
    private EmpresaFinanceiroService empresaFinanceiroService;
    @Autowired
    private ChavesEstrategicasDao chavesEstrategicasDao;

    public Paths paths() throws Exception {
        return pathsService.findDefault();
    }

    public ChavesEstrategicas chavesEstrategicas() throws Exception {
        List<ChavesEstrategicas> all = chavesEstrategicasDao.findAll();
        return all == null || all.isEmpty() ? new ChavesEstrategicas() : all.get(0);
    }

    public void reload(String chave) {
        String urlDiscovery = Aplicacao.getProp(Aplicacao.discoveryUrls);

        HttpGet httpGet = new HttpGet(urlDiscovery + "/reload?key=" + chave);
        httpGet.setHeader("Content-Type", "application/json;");

        try (CloseableHttpClient httpClient = ExecuteRequestHttpService.createConnector()) {
            String resposta = EntityUtils.toString(httpClient.execute(httpGet).getEntity());
            Uteis.logar(resposta);
        } catch (Exception e) {
            Uteis.logar(e, DiscoveryService.class);
        }
    }

    public void reloadLogin(String chave) {
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = this.urlsChave(chave);
            if (clientDiscoveryDataDTO == null) {
                Uteis.logar("DiscoveryService | ClientDiscoveryDataDTO null!");
                return;
            }

            HttpGet httpGet = new HttpGet(clientDiscoveryDataDTO.getServiceUrls().getLoginAppUrl() + "/prest/config/reload?key=" + chave);
            httpGet.setHeader("Content-Type", "application/json;");

            try (CloseableHttpClient httpClient = ExecuteRequestHttpService.createConnector()) {
                String resposta = EntityUtils.toString(httpClient.execute(httpGet).getEntity());
                Uteis.logar(resposta);
            } catch (Exception e) {
                Uteis.logar(e, DiscoveryService.class);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public ClientDiscoveryDataDTO urlsChave(String chave) {
        try {
            String urlDiscovery = Aplicacao.getProp(Aplicacao.discoveryUrls);

            HttpGet httpGet = new HttpGet(urlDiscovery + "/find/" + chave);
            httpGet.setHeader("Content-Type", "application/json;");

            try (CloseableHttpClient httpClient = ExecuteRequestHttpService.createConnector()) {
                String response = EntityUtils.toString(httpClient.execute(httpGet).getEntity());
                return JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), ClientDiscoveryDataDTO.class, true);
            } catch (Exception e) {
                Uteis.logar(e, DiscoveryService.class);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
        return null;
    }

    public ClientDiscoveryDataDTO obterUrls(String key) throws Exception {
        Paths paths = paths();
        ClientDiscoveryDataDTO client = new ClientDiscoveryDataDTO();
        client.setServiceUrls(new ServiceMapDTO(paths));
        Empresa empresa = obterEmpresa(key);
        configs(empresa, client, paths);
        if (empresa != null) {
            client.getServiceUrls().setZwUrl(empresa.getRoboControle());
            client.setEmpresas(empresasChave(key, client));
            try {
                client.setFinanceiroEmpresas(obterEmpresaFinanceiro(key));
            } catch (Exception e) {
                System.out.println(String.format("Ignorando exceção ao obter Empresa Financeiro %s", key));
            }
            client.getServiceUrls().setTreinoApiUrl(empresa.getUrlTreino() + "/prest");
            client.getServiceUrls().setTreinoUrl(empresa.getUrlTreino());
        }
        return client;
    }

    private ClientDiscoveryDataDTO configs(Empresa empresa, ClientDiscoveryDataDTO client, Paths paths) {
        if (empresa != null) {
            client.setModulosHabilitados(Modulo.modulos(empresa.getModulos()));
            client.getServiceUrls().setTreinoApiUrl(empresa.getUrlTreino() + "/prest");
            client.getServiceUrls().setZwUrl(empresa.getRoboControle());
        }
        client.getServiceUrls().setGraduacaoMsUrl(paths.getGraduacao());
        client.getServiceUrls().setLoginMsUrl(paths.getAutenticacao());
        client.getServiceUrls().setPersonagemMsUrl(paths.getPersonagem());
        client.getServiceUrls().setFrontPersonal(paths.getFrontPersonal());
        return client;
    }

    public Empresa obterEmpresa(String chave) throws Exception {
        return empresaDao.findById(chave);
    }

    public DatabaseDTO dadosBanco(String chave) throws Exception {
        Empresa empresa = empresaDao.findById(chave);
        DatabaseDTO dados = new DatabaseDTO();
        dados.setDatabase(empresa.getNomeBD());
        dados.setPassword(empresa.getPasswordBD());
        dados.setUser(empresa.getUserBD());
        dados.setPort(empresa.getPorta());
        dados.setHost(empresa.getHostBD());
        return dados;
    }

    public List<EmpresaFinanceiroDTO> obterEmpresaFinanceiro(String chave) throws Exception {
        List<EmpresaFinanceiroDTO> empsFin = new ArrayList<>();
        List<EmpresaFinanceiro> empresaFinanceiros = empresaFinanceiroService.consultarEmpresasPorChave(chave);
        for (EmpresaFinanceiro e : empresaFinanceiros) {
            EmpresaFinanceiroDTO empfin = new EmpresaFinanceiroDTO();
            empfin.setEmpresazw(e.getEmpresazw());
            empfin.setCodigoFinanceiro(e.getCodigoFinanceiro());
            empfin.setNome(e.getNomeFantasia());
            empsFin.add(empfin);
        }
        return empsFin;
    }

    private List<EmpresaDTO> empresasChave(String chave, ClientDiscoveryDataDTO client) {
        if (client.getServiceUrls().getZwUrl() == null || client.getServiceUrls().getZwUrl().isEmpty()) {
            return new ArrayList<>();
        }
        List<EmpresaDTO> empresas = new ArrayList<>();
        try {
            if (Arrays.asList(client.getModulosHabilitados()).contains(Modulo.ZILLYON_WEB.getSiglaModulo())) {
                //obter empresas da chave
                String resposta = httpServico.getString(client.getServiceUrls().getZwUrl()
                        + "/prest/empresa/todas?chave=" + chave, null);
                JSONArray array = new JSONArray(resposta);
                for (int i = 0; i < array.length(); i++) {
                    empresas.add(new EmpresaDTO(array.getJSONObject(i)));
                }
            } else {
                //obter empresas do banco do treino
                String resposta = httpServico.getString(client.getServiceUrls().getTreinoApiUrl()
                        + "/config/" + chave + "/empresas", null);
                JSONArray array = new JSONObject(resposta).getJSONArray("return");
                for (int i = 0; i < array.length(); i++) {
                    empresas.add(new EmpresaDTO(array.getJSONObject(i)));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return empresas;
    }
}
