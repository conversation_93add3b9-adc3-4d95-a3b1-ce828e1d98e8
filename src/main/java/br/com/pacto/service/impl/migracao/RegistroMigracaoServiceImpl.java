package br.com.pacto.service.impl.migracao;

import br.com.pacto.bean.migracao.dto.FiltroRegistroMigracao;
import br.com.pacto.bean.migracao.excecao.MigracaoException;
import br.com.pacto.bean.migracao.modelo.RegistroMigracao;
import br.com.pacto.dao.intf.migracao.RegistroMigracaoDao;
import br.com.pacto.service.intf.migracao.RegistroMigracaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Descrição: Serviço para manipulação de {@link RegistroMigracao}
 * Projeto: NewOAMD
 *
 * <AUTHOR> - 19/abr/2018 às 16:00
 * Pacto Soluções - Todos os direitos reservados
 */
@Service
@Transactional(readOnly = true)
public class RegistroMigracaoServiceImpl implements RegistroMigracaoService {

    @Autowired
    private RegistroMigracaoDao registroMigracaoDao;

    @Override
    public List<RegistroMigracao> listar(FiltroRegistroMigracao filtro) {
        return registroMigracaoDao.listar(filtro);
    }

    @Override
    public int contar(FiltroRegistroMigracao filtro) {
        return registroMigracaoDao.contar(filtro);
    }

    @Override
    public RegistroMigracao consultar(RegistroMigracao registroMigracao){
        try {
            return registroMigracaoDao.findById(registroMigracao.getCodigo());
        } catch (Exception e) {
            throw new MigracaoException("Erro ao consultar registro de importacao", e);
        }
    }
}
