    /*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.servlet;

    import br.com.pacto.bean.empresa.Empresa;
    import br.com.pacto.bean.empresa.EmpresaSite;
    import br.com.pacto.bean.empresa.FlagSoftwareEnum;
    import br.com.pacto.bean.empresa.InfoInfraEnum;
    import br.com.pacto.bean.usuario.Usuario;
    import br.com.pacto.controller.json.base.SuperControle;
    import br.com.pacto.objeto.Calendario;
    import br.com.pacto.objeto.Uteis;
    import br.com.pacto.service.intf.empresa.EmpresaService;
    import br.com.pacto.service.intf.empresa.EmpresaSiteService;
    import br.com.pacto.service.intf.usuario.UsuarioService;
    import br.com.pacto.util.ExecuteRequestHttpService;
    import br.com.pacto.util.PropsService;
    import br.com.pacto.util.UtilContext;
    import br.com.pacto.util.UtilProps;
    import br.com.pacto.util.enumeradores.SchemaBancoEnum;
    import br.com.pacto.util.impl.JSFUtilities;
    import org.json.JSONObject;

    import javax.faces.context.FacesContext;
    import javax.servlet.ServletException;
    import javax.servlet.http.HttpServlet;
    import javax.servlet.http.HttpServletRequest;
    import javax.servlet.http.HttpServletResponse;
    import java.io.IOException;
    import java.io.PrintWriter;
    import java.net.MalformedURLException;
    import java.net.URL;
    import java.util.Date;
    import java.util.Enumeration;
    import java.util.HashMap;
    import java.util.Map;
    import java.util.logging.Level;
    import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class OIDServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        try {
            String lgn = request.getParameter("lgn");
            //LOGIN DE OUTRO MÓDULO PARA O OAMD: UCP é um deles...
            if (lgn != null) {
                String lgnJ = Uteis.desencriptar(lgn, "chave_login_unificado");
                try {
                    JSONObject json = new JSONObject(lgnJ);
                    UsuarioService us = (UsuarioService) UtilContext.getBean(
                            UsuarioService.class);
                    Usuario usuario = us.prepararAmbiente(json);
                    if (usuario != null) {
                        request.getSession(true).setAttribute(JSFUtilities.LOGGED, usuario);
                        response.sendRedirect(request.getContextPath() + "/empresas");
                    }
                } catch (Exception ex) {
                    out.println(ex.getMessage());
                    Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
                }
                return;
            }
            request.getSession().setAttribute("erro", "Usuários ou senha incorretos.");
            String k = request.getParameter("k");
            if (request.getParameter("propagable") != null && request.getParameter("propagable").equals("s")) {
                propagar(request.getRequestURL().toString().replace("oid",
                        request.getParameter("svc")), request);
            } else if (k != null) {
                //DESCOBRIR URL DOS WEBSERVICES DO ZW
                String ws = request.getParameter("ws");
                try {
                    EmpresaService es = UtilContext.getBean(EmpresaService.class);
                    Empresa e = es.obterPorId(k);
                    if (e != null) {
                        if (ws != null) {
                            String url = e.getRoboControleCorrigindoProtocolo();
                            if (ws.contains("IntegracaoCadastrosWS") && e.getUrlIntegracaoWS() != null && !e.getUrlIntegracaoWS().isEmpty()) {
                                url = e.getUrlIntegracaoWS();
                            } else if (ws.contains("ValidacaoAcessoWSJSON") && e.getRoboControle() != null && !e.getRoboControle().isEmpty()) {
                                response.setContentType("application/json");
                                response.setCharacterEncoding("ISO-8859-1");
                                JSONObject jsonRetorno = new JSONObject();
                                jsonRetorno.put(SuperControle.STATUS_SUCESSO, url+ "/prest/validaracesso");
                                out.println(jsonRetorno);
                                return;
                            } else if (ws.contains("ValidacaoAcessoWS") && e.getUrlZaw() != null && !e.getUrlZaw().isEmpty()) {
                                url = e.getUrlZaw();
                            } else if (ws.contains("RegistrarAcessoWS") && e.getUrlZawWrite() != null && !e.getUrlZawWrite().isEmpty()) {
                                url = e.getUrlZawWrite();
                            } else if (ws.contains("RoboControle")) {
                                JSONObject jsonRetorno = new JSONObject();
                                jsonRetorno.put(SuperControle.STATUS_SUCESSO, url);
                                out.println(jsonRetorno);
                                return;
                            }
                            response.sendRedirect(url + "/" + ws + "?wsdl");
                        } else {
                            response.sendRedirect(e.getRoboControle() + "/faces/inicio.jsp?key=" + k);
                        }
                    }else{
                        out.println("Empresa não encontrada com a chave: " + k);
                    }
                } catch (Exception ex) {
                    Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
                }
            } else if (request.getParameter("activate") != null && request.getParameter("activate").length() > 10) {
                //ATIVAR EMPRESA NO ZW E TREINO PARA DEGUSTAÇÃO TRIAL DO SISTEMA
                final String codCrypto = request.getParameter("activate");
                final String plain = Uteis.desencriptarCookie(codCrypto);
                String[] dados = plain.split("\\|");
                final String chave = dados[0];
                final String dataTrial = dados[1];
                EmpresaService es = (EmpresaService) UtilContext.getBean(EmpresaService.class);
                try {
                    Empresa eOAMD = es.obterPorId(chave);
                    EmpresaSiteService ess = (EmpresaSiteService) UtilContext.getBean(EmpresaSiteService.class);
                    EmpresaSite eSite = ess.obterPorEmpresaOAMD(eOAMD);
                    if (eOAMD != null && eSite != null) {
                        if (eOAMD.getAtiva()) {
                            request.setAttribute("RETURN", "Empresa já está ativada, efetue o Login com as credenciais que foram enviadas por e-mail.");
                        } else if (!eOAMD.getAtiva()) {
                            Date d = Calendario.getDate("ddMMyyyy", dataTrial);
                            if (Calendario.maior(Calendario.hoje(), d)) {
                                es.gerarEmpresa(InfoInfraEnum.LOCAWEB_TR, FlagSoftwareEnum.TR, eOAMD, SchemaBancoEnum.PADRAO);
                                eSite.setDataAtivacao(Calendario.hoje());
                                ess.alterar(eSite, true, true);
                                request.setAttribute("RETURN", String.format("Ativação realizada com sucesso para Empresa \"%s\": ", chave));
                            } else {
                                request.setAttribute("RETURN", String.format("Ativação expirada! Realize um novo cadastro ou ligue para (62) 3251-5820.", chave));
                            }
                        }
                    } else {
                        request.setAttribute("RETURN", String.format("Empresa não encontrada \"%s\". Ligue para (62) 3251-5820 e informe essa mensagem: \"%s\"", chave, codCrypto));
                    }
                    /**
                     * PREENCHER HTML NO FINAL PEGANDO O ATRIBUTO 'RETURN' e
                     * 'URL_REDIR' (caso seja ativado com sucesso)*
                     */
                    /* TODO output your page here
                     out.println("<html>");
                     out.println("<head>");
                     out.println("<title>Bem Vindo ao Pacto Treino</title>");  
                     out.println("</head>");
                     out.println("<body>");
                     out.println("<h1>Clique no link abaixo para entrar no sistema: " + URL + "</h1>");
                     out.println("</body>");
                     out.println("</html>");
                     */
                } catch (Exception ex) {
                    Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
                }
            } else if (request.getSession().getAttribute(JSFUtilities.LOGGED) == null) {
                response.sendRedirect("");
            }
        } finally {
            out.close();
        }
    }

    private void propagar(final String urlRequest, HttpServletRequest request) {
        String[] hostsPortas = PropsService.getPropertyValue(PropsService.instanciasNotificar).split(",");
        try {
            URL u = new URL(urlRequest);
            for (String hostPorta : hostsPortas) {
                final String porta = hostPorta.substring(hostPorta.indexOf(":") + 1);
                try {
                    if (u.getPort() != Integer.valueOf(porta).intValue()) {
                        StringBuilder s = new StringBuilder(u.getProtocol());
                        s.append("://").append(hostPorta).append(u.getPath());
                        Map<String, String> p = new HashMap<String, String>();
                        Enumeration<String> keys = request.getParameterNames();
                        while (keys.hasMoreElements()) {
                            final String k = keys.nextElement();
                            p.put(k, request.getParameter(k));
                        }
                        ExecuteRequestHttpService.executeRequest(s.toString(), p);
                    }
                } catch (Exception ex) {
                    Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        } catch (MalformedURLException ex) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
        }

    }

    private String printEmpresa(Empresa e) {
        StringBuilder sb = new StringBuilder();
        sb.append("<li class=\"span2\">");
        sb.append("            <a href=\"").append(e.getUrl()).append("\" target=\"_blank\" style=\"margin-top: 6px;\" class=\"thumbnail\">");
        sb.append("   <img alt=\"").append(e.getName()).append("\" data-src=\"bootstrap/js/holder/holder.js/156x70\"");
        sb.append("style=\"width: 136px; height: 50px;\" src=\"").append(e.getUrlLogo()).append("\">");
        sb.append("<span style=\"font-size: 8px;\" class=\"label\">").append(e.getName()).append("</span>");
        sb.append("</a>");
        FacesContext ctx = FacesContext.getCurrentInstance();
        if (ctx != null) {
            if (ctx.getExternalContext().getSessionMap().get("usuario") != null) {
                String u = (String) ctx.getExternalContext().getSessionMap().get("usuario");
                if (u.equals("adm")) {

                    sb.append("<a href=\"#myModal\" data-loading-text=\"Carregando...\"");
                    sb.append("	role=\"button\" class=\"btn btn-mini\" onclick=openOptions('").
                            append(e.getChave()).append("');");
                    sb.append(" data-toggle=\"modal\">Opções</a>");

                }
            }
        }

        sb.append("</li>");
        return sb.toString();
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
