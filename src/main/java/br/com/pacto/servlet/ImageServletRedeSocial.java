/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.servlet;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UtilContext;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
public class ImageServletRedeSocial extends HttpServlet {

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        String url = request.getParameter("url");
        String reload = request.getParameter("reload");
        String chave = request.getParameter("key");
        String empresa = request.getParameter("empresa");
        EmpresaService es = (EmpresaService) UtilContext.getBean(EmpresaService.class);
        if (chave != null) {
            Integer codEmpresa = Integer.valueOf(empresa);
            try {
                Empresa empresaOamd = es.obterPorId(chave);
                url = empresaOamd.getRoboControleSemHTTPS();
            } catch (Exception e) {
            }
            Logger.getLogger(ImageServletRedeSocial.class.getName()).log(Level.SEVERE, "PREENCHER LOGO - reload:{0} url:{1} key:{2} empresa:{3}", new Object[]{reload, url, chave, empresa});
            preencherLogoRedeSocial(chave,url, codEmpresa, request, reload);
            preencherLogoBackground(chave,url, codEmpresa, request, reload, "homeBackground640x551");
            preencherLogoBackground(chave,url, codEmpresa, request, reload, "homeBackground320x276");
            preencherLogoBackground(chave,url, codEmpresa, request, reload, "foto");
//            response.sendRedirect(request.getSession().getServletContext().getContextPath()+"/imagens/" + chave + "-" + codEmpresa + ".jpg");
            Propagador.propagar(request);
        }
        
    }
    public static void preencherLogoRedeSocial(String ctx,String url, Integer codigoEmpresa, HttpServletRequest request, String reload) {
        try {
            String caminho = request.getSession().getServletContext().getRealPath("imagens");
            caminho += File.separator + ctx + "-" + codigoEmpresa + ".jpg";
            File f = new File(caminho);
            if(f.exists() &&  reload != null){
                f.delete();
            }
            if (!f.exists() ||  reload != null) {
                saveImage(url + "/imageredesocial?key=" + ctx + "&empresa=" + codigoEmpresa, caminho);
            }
        } catch (IOException ex) {
            Logger.getLogger(ImageServletRedeSocial.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    public static void preencherLogoBackground(String ctx,String url, Integer codigoEmpresa, 
            HttpServletRequest request, String reload, String campo) {
        try {
            String caminho = request.getSession().getServletContext().getRealPath("imagens");
            caminho += File.separator+(campo)+ "-" + ctx + "-" + codigoEmpresa + ".jpg";
            File f = new File(caminho);
            if(f.exists() &&  reload != null){
                f.delete();
            }
            if (!f.exists() ||  reload != null ) {
                saveImage(url + "/imageredesocial?key=" + ctx + "&empresa=" + codigoEmpresa+"&campo="+campo, caminho);
            }
        } catch (IOException ex) {
            Logger.getLogger(ImageServletRedeSocial.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void saveImage(String imageUrl, String destinationFile) throws IOException {

        URL url = new URL(imageUrl);
        URLConnection urlConn = url.openConnection();
        urlConn.setConnectTimeout(1000);
        urlConn.setReadTimeout(5000);
        urlConn.setAllowUserInteraction(false);
        urlConn.setDoOutput(true);

        InputStream inStream = urlConn.getInputStream();
        OutputStream os = new FileOutputStream(destinationFile);

        byte[] b = new byte[4096];
        int length;
        int bytesEscritos = 0;
        while ((length = inStream.read(b)) != -1) {
            os.write(b, 0, length);
            bytesEscritos += length;
        }


        inStream.close();
        os.close();
        if (bytesEscritos > 0) {
            System.out.println("OAMD -> gravei LOGOMARCA -> " + bytesEscritos + " " + destinationFile);
        } else {
            System.out.println("OAMD -> NÃO gravei LOGOMARCA -> " + destinationFile);
        }
    }
    

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /** Handles the HTTP <code>GET</code> method.
     * @param request servlet request
     * @param response servlet response
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /** Handles the HTTP <code>POST</code> method.
     * @param request servlet request
     * @param response servlet response
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /** Returns a short description of the servlet.
     */
    public String getServletInfo() {
        return "Short description";
    }
    // </editor-fold>

    

}
