/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.servlet;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.UteisValidacao;

import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class Propagador {

    public static void propagar(HttpServletRequest request){

        if(request.getParameterMap().get("propagar") != null
                && ((String[])request.getParameterMap().get("propagar")).length > 0
                && ((String[])request.getParameterMap().get("propagar"))[0].equals("false")){
            Uteis.logarDebug(String.format("NAO PROPAGAR: %s",((String[])request.getParameterMap().get("propagar"))[0]));
            return;
        }

        Uteis.logarDebug(String.format("INSTANCIAS: %s", Aplicacao.getProp(Aplicacao.instanciasPropagar)));

        Map<String, String> params = new HashMap<String, String>(request.getParameterMap());

        Enumeration parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            Object object = parameterNames.nextElement();
            String[] value = (String[]) request.getParameterMap().get(object);
            if(value != null && value.length > 0){
                params.put(object.toString(), value[0]);
            }
        }

        params.put("propagar", "false");

        String[] hostsPortas = Aplicacao.getProp(Aplicacao.instanciasPropagar) == null ? new String[]{} : 
                Aplicacao.getProp(Aplicacao.instanciasPropagar).split(",");
        try {
            URL u = new URL(request.getRequestURL().toString());
            for (String hostPorta : hostsPortas) {

                if(hostPorta.isEmpty()){
                    continue;
                }

                if (!UteisValidacao.emptyString(request.getHeader("host")) && !hostPorta.contains(request.getHeader("host"))) {
                    continue;
                }

                final String host = hostPorta.substring(0, hostPorta.indexOf(":"));
                final String porta = hostPorta.substring(hostPorta.indexOf(":") + 1);
                try {
                    String proto = u.getProtocol();
                    if ((u.getPort() != Integer.valueOf(porta)) || (!u.getHost().equals(host))) {

                        switch (porta) {
                            case "8080":
                                proto = "http";
                            default:
                                break;
                        }

                        StringBuilder s = new StringBuilder(proto).append("://").append(hostPorta).append(u.getPath());
                        Uteis.logarDebug(String.format("PROPAGANDO ====>> %s", s));
                        ExecuteRequestHttpService.executeRequest(s.toString(), params);
                    }
                } catch (Exception ex) {
                    Logger.getLogger(Propagador.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        } catch (MalformedURLException ex) {
            Logger.getLogger(Propagador.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
