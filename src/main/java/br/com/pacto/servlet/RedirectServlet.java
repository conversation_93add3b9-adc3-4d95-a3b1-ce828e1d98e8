/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.servlet;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.impl.JSFUtilities;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class RedirectServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        try {
            if (request.getParameter("up") != null) {
                Usuario user = (Usuario) request.getSession().getAttribute(JSFUtilities.LOGGED);
                if (user != null) {
                    try {
                        JSONObject o = new JSONObject();
                        o.put("userName", user.getUserName());
                        o.put("oamd", true);
                        o.put("nomeCompleto", user.getUserName());
                        o.put("timevld", Calendario.getInstance(Calendario.hoje().getTime() + ((60 * 3) * 1000)).getTimeInMillis());
                        final String lgn = Uteis.encriptar(o.toString(), "chave_login_unificado");
                        response.sendRedirect(String.format("%s/oid?lgn=%s", Aplicacao.getProp(Aplicacao.myUpUrlBase), lgn));
                    } catch (Exception ex) {
                        Logger.getLogger(RedirectServlet.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }

            if (request.getParameter("apoio") != null) {
                Usuario user = (Usuario) request.getSession().getAttribute(JSFUtilities.LOGGED);
                if (user != null) {
                    try {
                        JSONObject o = new JSONObject();
                        o.put("userName", user.getUserName());
                        o.put("oamd", true);
                        o.put("nomeCompleto", user.getUserName());
                        o.put("timevld", Calendario.getInstance(Calendario.hoje().getTime() + ((60 * 3) * 1000)).getTimeInMillis());
                        final String lgn = Uteis.encriptar(o.toString(), "chave_login_unificado");
                        response.sendRedirect(String.format("%s/oid?lgn=%s", Aplicacao.getProp(Aplicacao.telaApoio), lgn));
                    } catch (Exception ex) {
                        Logger.getLogger(RedirectServlet.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }

            if (request.getParameter("gameindicadores") != null) {
                Usuario user = (Usuario) request.getSession().getAttribute(JSFUtilities.LOGGED);
                if (user != null) {
                    try {
                        JSONObject o = new JSONObject();
                        o.put("userName", user.getUserName());
                        o.put("oamd", true);
                        o.put("nomeCompleto", user.getUserName());
                        o.put("timevld", Calendario.getInstance(Calendario.hoje().getTime() + ((60 * 3) * 1000)).getTimeInMillis());
                        final String lgn = Uteis.encriptar(o.toString(), "chave_login_unificado");
                        response.sendRedirect(String.format("%s/gerais",
                                Aplicacao.getProp(Aplicacao.urlGame),
                                lgn));
                    } catch (Exception ex) {
                        Logger.getLogger(RedirectServlet.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }
        } finally {
            out.close();
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
