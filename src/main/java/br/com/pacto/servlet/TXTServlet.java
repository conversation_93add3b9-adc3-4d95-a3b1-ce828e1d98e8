/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.servlet;

import br.com.pacto.bean.envioprotheus.EnvioProtheus;
import br.com.pacto.bean.envioprotheus.StatusEnvioEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.envioprotheus.EnvioProtheusService;
import br.com.pacto.util.UtilContext;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintStream;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
public class TXTServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        String nome = request.getParameter("nome");
        String conteudo = request.getParameter("conteudo");
        conteudo = conteudo == null ? "" : conteudo.replaceAll("\",", "\",\n");
        String falhas = request.getParameter("falhas");
        File file = new File(nome+".txt");
        if(falhas != null){
            try {
                EnvioProtheusService envioService = (EnvioProtheusService) UtilContext.getBean(EnvioProtheusService.class);
                List<EnvioProtheus> falhasT = envioService.historico(0, StatusEnvioEnum.FALHA);
                StringBuilder s = new StringBuilder();
                for(EnvioProtheus f : falhasT){
                    s.append("Envio de código: ").append(f.getCodigoRetorno()).append("\n");
                    s.append("Unidade: ").append(f.getChaveEmpresa()).append("\n");
                    s.append("Data/Hora: ").append(Uteis.getDataComHHMM(f.getHoraEnvio())).append("\n");
                    s.append("Dados: ").append(f.getDadosEnviados()).append("\n");
                    s.append("Msg erro: ").append(f.getMsErro()).append("\n");
                    
                    s.append("\n---------------------------------------------------------------\n\n");
                }
                conteudo = s.toString();
            } catch (Exception e) {
                conteudo = e.getMessage();
            }
            
            
        }
        String fileType = "";
        // Find this file id in database to get file name, and file type

        // You must tell the browser the file type you are going to send
        // for example application/pdf, text/plain, text/html, image/jpg
        response.setContentType(fileType);

        // Make sure to show the download dialog
        response.setHeader("Content-disposition", "attachment; filename=yourcustomfilename.pdf");

        // Assume file name is retrieved from database
        // For example D:\\file\\test.pdf


        
        PrintStream ps = new PrintStream(file);
        ps.println(nome+"\n"+conteudo);

        response.setContentType("application/octet-stream");
        response.setContentLength((int) file.length());
        response.setHeader("Content-Disposition",
                String.format("attachment; filename=\"%s\"", file.getName()));

        OutputStream out = response.getOutputStream();
        FileInputStream in = new FileInputStream(file);
        byte[] buffer = new byte[4096];
        int length;
        while ((length = in.read(buffer)) > 0) {
            out.write(buffer, 0, length);
        }


        out.flush();
    }
}
