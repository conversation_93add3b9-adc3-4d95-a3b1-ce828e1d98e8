package br.com.pacto.enums;

import br.com.pacto.bean.usuario.UsuarioZW;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * <h1>IMPORTANTE: <PERSON><PERSON><PERSON> você realize alguma alteração nesta classe, favor, refletir a mesma alteração na classe Modulo no módulo LoginAPP ou entre outro módulo se houver.</h1>
 * <p>
 * Repositório para os módulos disponíveis do Sistema.
 *
 * <AUTHOR>
 * @since 08/11/2018
 */
public enum Modulo {

    AULA_CHEIA("SLC", "Aula Cheia", null),
    CENTRAL_DE_EVENTOS("CE", "Central de Eventos", "ce"),
    CUSTOMER_RELATIONSHIP_MANAGEMENT("CRM", "CRM", null),
    FINANCEIRO("FIN", "Financeiro", "finan"),
    GAME_OF_RESULTS("GOR", "Game of Results", null),
    STUDIO("EST", "Studio", "estudio"),
    TREINO("TR", "Treino", null),
    ZILLYON_WEB("ZW", "ZillyonWeb", null),
    UNIVERSIDADE_COORPORATIVA_PACTO("UCP", "UCP", null),
    GESTAO_DE_PERSONAL("GP", "Gestão de Personal", null),
    SMART_BOX("SBX", "SmartBox", null),
    ZILLYON_AUTO_ATENDIMENTO("ZAA", "Autoatendimento(Totem)", null),
    NOVO_TREINO("NTR", "Novo treino", "novotreino"),
    NOVO_CROSSFIT("NCR", "Crossfit", null),
    NOVO_AVALIACAO_FISICA("NAV", "Avaliação Física", null),
    NOVO_VENDAS_ONLINE("NVO", "Vendas Online", null),
    INTELIGENCIA_ARTIFICIAL("IA", "Inteligência Artificial", null),
    OPEN_BANK("OPB", "Open Bank", null),
    GRADUACAO("GRD", "Graduação", null),
    PERSONAL_FIT("PEF", "Personal Fit", null),
    NOVO_ZW("NZW", "Novo ZW", "adm"),
    PIX("PIX", "Pix", null),
    PACTO_PAY("PAY", "PactoPay", "pactopay"),
    FACILITE_PAY("FAC", "Fypay", null),
    NICHO("NIC", "Nicho", null),
    ;

    private static final String SEPARADOR_SIGLA_MODULOS = ",";
    private final String siglaModulo;
    private final String descricao;
    private final String contexto;

    Modulo(String siglaModulo, String descricao, String contexto) {
        this.siglaModulo = siglaModulo;
        this.descricao = descricao;
        this.contexto = contexto;
    }

    /**
     * @param siglaModulo que será ignorado <b>CASE</b> e espaços no ínicio e no fim.
     * @return Dado a <code>siglaModulo</code> informada, retorna a <b>ENUM respectiva</b> ou <b>lança {@link ModuloNaoEncontradoException}</b>.
     * @throws ModuloNaoEncontradoException quando através da <code>siglaModulo</code> informada, não for possível encontrar nenhum <b>Módulo</b>.
     */
    public static Modulo fromSigla(String siglaModulo) {
        for (Modulo value : values()) {
            String siglaModuloComTrim = siglaModulo.trim();
            if (value.siglaModulo.equalsIgnoreCase(siglaModuloComTrim)) {
                return value;
            }
        }
        return null;
    }

    /**
     * @param siglasModulos que contenha as siglas dos módulos separados por vírgula ({@link #SEPARADOR_SIGLA_MODULOS}). <br>
     *                      Para cada sigla encontrada, será ignorado <b>CASE</b> e espaços no ínicio e no fim.
     * @return Dado a <code>siglasModulos</code> informada, retorna a(s) <b>ENUM(s) respectiva(s)</b> ou <b>lança {@link ModuloNaoEncontradoException}</b>.
     * @throws ModuloNaoEncontradoException quando através da <code>siglaModulo</code> informada, não for possível encontrar nenhum <b>Módulo</b>.
     */
    public static Set<Modulo> fromSiglas(String siglasModulos) {
        String[] siglas = siglasModulos.split(SEPARADOR_SIGLA_MODULOS);

        Set<Modulo> modulos = new HashSet<Modulo>();
        for (String sigla : siglas) {
            if (StringUtils.isNotBlank(sigla)) {
                modulos.add(Modulo.fromSigla(sigla));
            }
        }

        return modulos;
    }

    /**
     * @param usuario com os módulos a serem verificados.
     * @param modulo  alvo.
     * @return Dado um <code>usuario</code>, returna <b>TRUE</b> se o <code>modulo</code> informado está presente.
     */
    public static boolean isModuloHabilitadoDadoUmUsuario(UsuarioZW usuario, Modulo modulo) {
        return isModuloHabilitado(usuario.getModulosEnum(), modulo);
    }

    /**
     * @param modulosHabilitados a serem verificados.
     * @param modulo             alvo.
     * @return Dado uma coleção de <code>modulosHabilitados</code>, returna <b>TRUE</b> se o <code>modulo</code> informado está presente.
     */
    public static boolean isModuloHabilitado(Set<Modulo> modulosHabilitados, Modulo modulo) {
        return modulosHabilitados.contains(modulo);
    }

    /**
     * @param usuario com os módulos a serem verificados.
     * @param modulo  alvo.
     * @return Dado um <code>usuario</code>, returna <b>TRUE</b> se o <code>modulo</code> informado <b>NÃO</b> estiver presente.
     */
    public static boolean isModuloNaoHabilitadoDadoUmUsuario(UsuarioZW usuario, Modulo modulo) {
        return isModuloNaoHabilitado(usuario.getModulosEnum(), modulo);
    }

    /**
     * @param modulosHabilitados a serem verificados.
     * @param modulo             alvo.
     * @return Dado uma coleção de <code>modulosHabilitados</code>, returna <b>TRUE</b> se o <code>modulo</code> informado <b>NÃO</b> estiver presente.
     */
    public static boolean isModuloNaoHabilitado(Set<Modulo> modulosHabilitados, Modulo modulo) {
        return !modulosHabilitados.contains(modulo);
    }

    public static String getModulosTreinoTrial() {
        return "NTR,PEF";
    }

    public static String[] modulos(String siglasModulos) {
        return siglasModulos.split(SEPARADOR_SIGLA_MODULOS);
    }

    public String getSiglaModulo() {
        return siglaModulo;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getContexto() {
        return contexto;
    }
}
