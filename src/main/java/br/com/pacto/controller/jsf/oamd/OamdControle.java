/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.jsf.oamd;

import br.com.pacto.bean.configuracoes.ConfiguracaoEnum;
import br.com.pacto.bean.creditoPacto.CreditoPacto;
import br.com.pacto.bean.creditoPacto.CreditoPactoEmpresa;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.empresa.IdiomaBancoEnum;
import br.com.pacto.bean.empresa.InfoInfraEnum;
import br.com.pacto.bean.empresa.redeempresa.RedeEmpresa;
import br.com.pacto.bean.empresafinanceiro.EmpresaFinanceiro;
import br.com.pacto.bean.infoMigracao.InfoMigracaoLog;
import br.com.pacto.bean.perfil.permissao.PerfilPadraoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuario.UsuarioZW;
import br.com.pacto.controller.jsf.base.MenuControle;
import br.com.pacto.controller.jsf.base.SuperControle;
import br.com.pacto.controller.json.empresa.EmpresaJSON;
import br.com.pacto.controller.json.infoMigracao.DadosInfoMigracaoDTO;
import br.com.pacto.controller.json.infoMigracao.DadosInfoMigracaoItemDTO;
import br.com.pacto.enums.Modulo;
import br.com.pacto.enums.ModuloBloqueioEnum;
import br.com.pacto.memcached.impl.MemCachedManager;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Formatador;
import br.com.pacto.objeto.HttpRequestUtil;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.empresa.DiscoveryService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.creditoPacto.CreditoPactoEmpresaService;
import br.com.pacto.service.intf.creditoPacto.CreditoPactoService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.empresafinanceiro.EmpresaFinanceiroService;
import br.com.pacto.service.intf.infoMigracao.InfoMigracaoService;
import br.com.pacto.service.intf.oamd.OAMDService;
import br.com.pacto.service.intf.redeempresa.RedeEmpresaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.utilitarias.LogBloqueioService;
import br.com.pacto.service.intf.utilitarias.LogProcessoService;
import br.com.pacto.util.ExecuteRequestHttpService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.enumeradores.CarteirasEnum;
import br.com.pacto.util.enumeradores.TipoCobrancaPactoEnum;
import br.com.pacto.util.enumeradores.TipoInfoMigracaoEnum;
import br.com.pacto.util.enumeradores.TipoInfoMigracaoLogEnum;
import br.com.pacto.util.impl.JSFUtilities;
import br.com.pacto.util.impl.Ordenacao;
import edu.emory.mathcs.backport.java.util.Arrays;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.json.JSONArray;
import org.json.JSONObject;
import org.primefaces.event.SelectEvent;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.zw.client.EmpresaWS;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.SessionScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.servlet.ServletContext;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static br.com.pacto.util.enumeradores.TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS;
import static br.com.pacto.util.enumeradores.TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO;
import static ch.lambdaj.Lambda.sumFrom;

/**
 * <AUTHOR>
 */
@ManagedBean(name = "OamdControle")
@SessionScoped
public class OamdControle extends SuperControle {

    private static final String SCRIPT_BOT = "/br/com/pacto/util/resources/scripts/botscript.js";
    private static final String BOT_URL = "BOT_URL";
    private static final String URL_VALIDATE_USER = "URLVALIDATEUSER";
    private static final String BOT_APP_KEY = "BOT_APP_KEY";
    private static final String USER_NAME = "USER_NAME";
    private static final String USER_PWD = "USER_PWD";
    private static final String TOKEN1 = "TOKEN1";
    private static final String TOKEN2 = "TOKEN2";
    private static final String USER_EMAIL = "USER_EMAIL";
    private static final String USER_PHONE = "USER_PHONE";
    private static final String USER_CITY = "USER_CITY";
    private static final String USER_CITY_GOIANIA = "Goiânia";

    private static List<String> urlImages = new ArrayList<>();

    private List<Empresa> listaEmpresasFiltrada = new ArrayList<>();
    private List<Empresa> listaEmpresasFiltradaSub = new ArrayList<>();
    private List<Empresa> listaEmpresas = new ArrayList<>();
    private List<EmpresaJSON> empresasConcatenadas = new ArrayList<>();
    private int codigo = 0;
    private Empresa empresaSelecionada;
    private String imagemAtual;
    private FiltrosEnum filtroTipoServidor = FiltrosEnum.TODOS;
    private InfoInfraEnum infra = null;
    private String filtroNome = "";
    private List<SelectItem> usuariosPMGSelect;
    private List<Usuario> usuariosPMG;
    private Integer codigoCarteira;
    private Integer codigoCarteiraFiltro;
    private VisualizacaoEnum visualizacao = VisualizacaoEnum.LISTA;
    private boolean tentarBaixarLogo = true;
    private boolean logado = false;
    private boolean logando = false;
    private int index = 0;
    private boolean scroll = true;
    private EmpresaJSON empresaUnidSelecionada;
    private Integer qtdCreditoAdicionar;
    private EmpresaJSON empresaUnidSelecionadaConfigurar;
    private boolean gerarCobrancaPacto = true;
    private String justificativa;
    private List<Empresa> empresasHistorico;
    private Boolean configurandoRede = false;
    private RedeEmpresa redeDCC;
    private Integer codEmpresaFinanceiroCobranca;
    private List<SelectItem> gruposChatMoviDesk;
    private Date dataAlteracaoPosPago;
    private List<SelectItem> modEmpresas = new ArrayList<>();
    private String pwdAdmin;
    private String pwdPactoBR;
    private String justificativaBonusCredito;
    private Integer qtdBonusCredito;
    private Integer qtdBonusAtual;
    private String urlDocumentacaoPactoStore;
    private Integer creditoPactoSelecionado;
    private List<CreditoPacto> listaCreditoPacto;
    private String botAppKey;
    private String botUrl;
    private String urlValidateUser;
    private Date dataEscolhida;
    private Integer empresaRecursoPadrao;
    private List<String> tiposInfoMigracaoPadrao;
    private List<String> tiposInfoMigracaoPadraoAnterior;
    private String ferramentaAtual = "none";


    public EmpresaJSON getEmpresaUnidSelecionada() {
        if (empresaUnidSelecionada == null) {
            empresaUnidSelecionada = new EmpresaJSON();
        }
        return empresaUnidSelecionada;
    }

    public void setEmpresaUnidSelecionada(EmpresaJSON empresaUnidSelecionada) {
        this.empresaUnidSelecionada = empresaUnidSelecionada;
    }

    public Integer getQtdCreditoAdicionar() {
        if (qtdCreditoAdicionar == null) {
            qtdCreditoAdicionar = 0;
        }
        return qtdCreditoAdicionar;
    }

    public void setQtdCreditoAdicionar(Integer qtdCreditoAdicionar) {
        this.qtdCreditoAdicionar = qtdCreditoAdicionar;
    }

    public EmpresaJSON getEmpresaUnidSelecionadaConfigurar() {
        if (empresaUnidSelecionadaConfigurar == null) {
            empresaUnidSelecionadaConfigurar = new EmpresaJSON();
        }
        return empresaUnidSelecionadaConfigurar;
    }

    public void setEmpresaUnidSelecionadaConfigurar(EmpresaJSON empresaUnidSelecionadaConfigurar) {
        this.empresaUnidSelecionadaConfigurar = empresaUnidSelecionadaConfigurar;
    }

    public boolean isGerarCobrancaPacto() {
        return gerarCobrancaPacto;
    }

    public void setGerarCobrancaPacto(boolean gerarCobrancaPacto) {
        this.gerarCobrancaPacto = gerarCobrancaPacto;
    }

    public String getJustificativa() {
        if (justificativa == null) {
            justificativa = "";
        }
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public InfoInfraEnum getInfra() {
        return infra;
    }

    public void setInfra(InfoInfraEnum infra) {
        this.infra = infra;
    }

    public List<SelectItem> getGruposChatMoviDesk() {
        return gruposChatMoviDesk;
    }

    public void setGruposChatMoviDesk(List<SelectItem> gruposChatMoviDesk) {
        this.gruposChatMoviDesk = gruposChatMoviDesk;
    }

    public Boolean getApresentarMenuCadastros() {
        return getUsuario().getAdm() || isPermiteCriarEmpresa() || (getUsuario().getPmg() && getOamdPacto());
    }

    public Boolean getApresentarAbaModulos() {
        return empresaSelecionada != null
                && (getUsuario().getAdm() || getUsuario().isPermiteAlterarModulosChave());
    }

    public Boolean getPermiteAcessarConfiguracoesChave() {
        return getOamdPacto() && (getUsuario().getAdm()
                || getUsuario().isPermiteCriarEmpresa()
                || getUsuario().isPermiteAcessarConfiguracoesChave()
                || getUsuario().isPermiteAlterarModulosChave());
    }

    public String getUrlDocumentacaoPactoStore() {
        if (urlDocumentacaoPactoStore == null) {
            obterURLDocumentacaoPactoStore();
        }
        return urlDocumentacaoPactoStore;
    }

    public void setUrlDocumentacaoPactoStore(String urlDocumentacaoPactoStore) {
        this.urlDocumentacaoPactoStore = urlDocumentacaoPactoStore;
    }

    private void obterURLDocumentacaoPactoStore() {
        try {
            ConfiguracaoSistemaService cs = UtilContext.getBean(ConfiguracaoSistemaService.class);
            urlDocumentacaoPactoStore = cs.obterValorPorConfiguracao(ConfiguracaoEnum.URL_DOCUMENTACAO_PACTO_STORE);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public Integer getCreditoPactoSelecionado() {
        if (creditoPactoSelecionado == null) {
            creditoPactoSelecionado = 0;
        }
        return creditoPactoSelecionado;
    }

    public void setCreditoPactoSelecionado(Integer creditoPactoSelecionado) {
        this.creditoPactoSelecionado = creditoPactoSelecionado;
    }

    public List<CreditoPacto> getListaCreditoPacto() {
        if (listaCreditoPacto == null) {
            listaCreditoPacto = new ArrayList<>();
        }
        return listaCreditoPacto;
    }

    public void setListaCreditoPacto(List<CreditoPacto> listaCreditoPacto) {
        this.listaCreditoPacto = listaCreditoPacto;
    }

    public List<String> getTiposInfoMigracaoPadraoAnterior() {
        if (tiposInfoMigracaoPadraoAnterior == null) {
            tiposInfoMigracaoPadraoAnterior = new ArrayList<>();
        }
        return tiposInfoMigracaoPadraoAnterior;
    }

    public void setTiposInfoMigracaoPadraoAnterior(List<String> tiposInfoMigracaoPadraoAnterior) {
        this.tiposInfoMigracaoPadraoAnterior = tiposInfoMigracaoPadraoAnterior;
    }

    public enum FiltrosEnum {

        TODOS,
        LOCAWEB,
        EXTERNO,
        POSSUEM_TREINO,
        TEM_BLOQUEIO,
        USO_INTERNO,
        TOTAL_EMPRESAS;
    }

    public enum VisualizacaoEnum {

        GRADE,
        LISTA,
        HOME;
    }

    public List<String> completeEmpresas(String nome) {
        List<String> lista = new ArrayList<String>();
        for (Empresa empresa : listaEmpresas) {
            if(empresa.getIdentificadorEmpresa() != null && empresa.getIdentificadorEmpresa().toLowerCase().contains(nome.toLowerCase())){
                lista.add(empresa.getIdentificadorEmpresa() + " " + empresa.getChave());
            }else if ((empresa.getName() + empresa.getChave()).toLowerCase().contains(nome.toLowerCase())) {
                lista.add(empresa.getName() + " " + empresa.getChave());
            }
        }
        return lista;
    }

    public void selecionar() {
        filtrar();
        if (listaEmpresasFiltrada.size() == 1) {
            abrirModal(listaEmpresasFiltrada.get(0));
        }
    }

    public List<SelectItem> getSelectItemEmpresaFinanceiroCobranca() {
        List<SelectItem> lista = new ArrayList<>();
        if (this.getRedeDCC() != null && !UteisValidacao.emptyList(this.getRedeDCC().getListaEmpresa())) {
            for (EmpresaFinanceiro obj : this.getRedeDCC().getListaEmpresa()) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getNomeResumo()));
            }
            Ordenacao.ordenarLista(lista, "label");
            lista.add(0, new SelectItem(0, ""));
        }
        return lista;
    }

    private void validarDados() throws Exception {

        if (getQtdCreditoAdicionar().equals(0)) {
            throw new Exception("Informe a quantidade de crédito.");
        }

        if (isGerarCobrancaPacto()) {
            if (getEmpresaUnidSelecionada().getQtdparcelascobrancapacto().equals(0)) {
                throw new Exception("Informe a quantidade de parcelas.");
            } else if (getEmpresaUnidSelecionada().getValorcreditopacto().equals(0.0)) {
                throw new Exception("Informe o valor unitário do crédito DCC.");
            }
            setJustificativa("");
        } else {
            if (getJustificativa().isEmpty()) {
                throw new Exception("Informe a justificativa para não gerar cobrança.");
            }
            getEmpresaUnidSelecionada().setValorcreditopacto(0.0);
            getEmpresaUnidSelecionada().setQtdparcelascobrancapacto(0);
            getEmpresaUnidSelecionada().setGerarnotafiscalcobrancapacto(false);
        }
    }

    public void executarProcessoLatitudeLongitude(EmpresaJSON empresa) {
        try {
            OAMDService os = UtilContext.getBean(OAMDService.class);
            empresaSelecionada = getEmpresaService().obterPorId(empresa.getChave());
            mensInfo(os.executarProcessoLatitudeLongitude(empresaSelecionada, empresa, empresa.getCodigo(), getUsuario()));

        } catch (Exception ex) {
            mensErro(ex.getMessage());
            ex.printStackTrace();
        }
    }

    public void gravarPrePagoDCCLancarAgendamentoFinan() {
        try {
            validarDados();
            OAMDService os = UtilContext.getBean(OAMDService.class);
            String retorno = os.gravarPrePagoDCCLancarAgendamentoFinan(empresaSelecionada, getEmpresaUnidSelecionada().getCodigo(), getQtdCreditoAdicionar(),
                    getEmpresaUnidSelecionada().getQtdparcelascobrancapacto(), getValorTotal(), getEmpresaUnidSelecionada().isGerarnotafiscalcobrancapacto(),
                    getUsuario().getUserName(), getJustificativa(), isGerarCobrancaPacto());

            if (retorno.startsWith("ERRO")) {
                retorno = retorno.replaceFirst("ERRO:", "");
                throw new Exception(retorno);
            } else {
                retorno = retorno.replaceAll("Sucesso:", "");
            }

            Integer qtdCreditoAtual = Integer.parseInt(retorno);
            StringBuilder msg = new StringBuilder("");
            msg.append("Foram adicionados nesta venda ").append(getQtdCreditoAdicionar()).append(" créditos e agora, o saldo total da empresa é de: ").append(qtdCreditoAtual).append(" créditos. \n");
            if (isGerarCobrancaPacto()) {
                msg.append("Foi lançado uma conta no Financeiro com sucesso.");
            } else {
                msg.append("Essa operação não gerou agendamento no Financeiro.");
            }
            mensInfo(msg.toString());
            getEmpresaUnidSelecionada().setCreditodcc(qtdCreditoAtual);
            for (EmpresaJSON empresaJSON : empresaSelecionada.getEmpresas()) {
                if (empresaJSON.getCodigo() == getEmpresaUnidSelecionada().getCodigo()) {
                    empresaJSON.setCreditodcc(qtdCreditoAtual);
                    break;
                }
            }
            doExecuteFunction("modalDialogCobrancaPacto.hide();");
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void gravarPosPagoDCCLancarAgendamentoFinan() {
        try {
            validarDados();
            OAMDService os = (OAMDService) UtilContext.getBean(OAMDService.class);
            String retorno = os.gravarPosPagoDCCLancarAgendamentoFinan(empresaSelecionada, getEmpresaUnidSelecionada().getCodigo(), getQtdCreditoAdicionar(),
                    getEmpresaUnidSelecionada().getTipocobrancapacto(), getEmpresaUnidSelecionada().getQtdparcelascobrancapacto(), getValorTotal(),
                    getEmpresaUnidSelecionada().isGerarnotafiscalcobrancapacto(), getUsuario().getUserName(), getJustificativa(), isGerarCobrancaPacto());

            if (retorno.startsWith("ERRO")) {
                retorno = retorno.replaceFirst("ERRO:", "");
                throw new Exception(retorno);
            }

            if (isGerarCobrancaPacto()) {
                mensInfo("Conta Lançada no Financeiro com sucesso.");
            } else {
                mensInfo("Essa operação não gerou agendamento no Financeiro.");
            }
            getEmpresaUnidSelecionada().setQtdCreditoUtilizadoPosPagoPacto(0);
            for (EmpresaJSON empresaJSON : empresaSelecionada.getEmpresas()) {
                if (empresaJSON.getCodigo() == getEmpresaUnidSelecionada().getCodigo()) {
                    empresaJSON.setQtdCreditoUtilizadoPosPagoPacto(0);
                    break;
                }
            }
            doExecuteFunction("modalDialogCobrancaPacto.hide();");
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void alterarInformacoesEmpresaCobrancaPacto(EmpresaJSON empresa) {
        try {

            boolean gravandoPactoMensal = empresa.getTipocobrancapacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo());
            EmpresaFinanceiro empresaFinanceiro = null;
            if (gravandoPactoMensal) {
                //quantidade de parcelas 1
                empresa.setQtdparcelascobrancapacto(1);
                //sempre gera automatico
                empresa.setGerarcobrancaautomaticapacto(true);

                if (UteisValidacao.emptyNumber(getCreditoPactoSelecionado())) {
                    throw new Exception("Selecione a tabela recorrência");
                }
                if (empresa.getQtddiasfechamentocobrancapacto() <= 0 || empresa.getQtddiasfechamentocobrancapacto() > 25) {
                    throw new Exception("O dia do fechamento deve estar entre os dias 1 e 25");
                }
                EmpresaFinanceiroService empFinanService = UtilContext.getBean(EmpresaFinanceiroService.class);
                empresaFinanceiro = empFinanService.obterEmpresa(empresa.getCodigo(), empresaSelecionada.getChave());
                if (empresaFinanceiro == null) {
                    throw new Exception("Empresa Financeiro não encontrada.");
                }
            }

            OAMDService os = UtilContext.getBean(OAMDService.class);
            String retorno = os.alterarInformacoesEmpresaCobrancaPacto(empresaSelecionada, empresa.getCodigo(), empresa.getTipocobrancapacto(), empresa.isGerarcobrancaautomaticapacto(),
                    empresa.getQtddiasfechamentocobrancapacto(), empresa.getValorcreditopacto(), empresa.isGerarnotafiscalcobrancapacto(), empresa.getQtdparcelascobrancapacto(),
                    empresa.getQtdcreditorenovarprepagocobrancapacto(), getUsuario().getUserName(), empresa.getDiavencimentocobrancapacto(), false,
                    empresa.isCobrarcreditopactoboleto());

            if (retorno.startsWith("ERRO")) {
                retorno = retorno.replaceFirst("ERRO:", "");
                throw new Exception(retorno);
            }

            //cadastrar empresa credito pacto
            if (gravandoPactoMensal) {
                CreditoPactoEmpresaService creditoEmpresaService = UtilContext.getBean(CreditoPactoEmpresaService.class);
                creditoEmpresaService.excluirPorEmpresaFinanceiro(empresaFinanceiro);

                CreditoPactoEmpresa novo = new CreditoPactoEmpresa();
                novo.setDataRegistro(Calendario.hoje());
                novo.setUsuario(getUsuario().getUserName());
                novo.setCreditoPacto(getCreditoPactoSelecionadoObj());
                novo.setEmpresaFinanceiro(empresaFinanceiro);
                creditoEmpresaService.inserir(novo);
            }

            mensInfo("Informações de Cobrança Automática atualizadas com sucesso.");
            os.carregarEmpresas(empresaSelecionada, getUsuario(), false);
            doExecuteFunction("modalDialogCobrancaConfig.hide();");
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void adicionarCreditoDCC(int quantidadeCreditos, EmpresaJSON empresa) {
        try {
            OAMDService os = (OAMDService) UtilContext.getBean(OAMDService.class);
            int creditos = os.adicionarCreditoDCC(empresaSelecionada, quantidadeCreditos, empresa.getCodigo());
            mensInfo("Foram adicionados " + quantidadeCreditos + " créditos, agora a empresa está com saldo de: " + creditos + ".");
            empresa.setCreditodcc(creditos);
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void removerCreditoDCC(int quantidadeCreditos, EmpresaJSON empresa) {
        try {
            OAMDService os = (OAMDService) UtilContext.getBean(OAMDService.class);
            int creditos = os.removerCreditoDCC(empresaSelecionada, quantidadeCreditos, empresa.getCodigo());
            mensInfo("Foram removidos " + quantidadeCreditos + " créditos, agora a empresa está com saldo de: " + creditos + ".");
            empresa.setCreditodcc(creditos);
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void bloquearDCC(int dias, EmpresaJSON empresa) {
        try {
            OAMDService os = (OAMDService) UtilContext.getBean(OAMDService.class);
            Date expiracao = Calendario.proximoDiaUtil(Calendario.hoje(), dias);
            mensInfo(os.bloquearCreditosDCC(empresaSelecionada, expiracao, empresa.getCodigo(),
                    getUsuario()));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            empresa.setDataexpiracaocreditodcc(sdf.format(expiracao));
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void desbloquearDCC(EmpresaJSON empresa) {
        try {
            OAMDService os = (OAMDService) UtilContext.getBean(OAMDService.class);
            mensInfo(os.desbloquearCreditosDCC(empresaSelecionada, empresa.getCodigo(), getUsuario()));
            empresa.setDataexpiracaocreditodcc("");
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void bloquear(int dias, EmpresaJSON empresa) {
        bloquear(dias, empresa, false);
    }

    public void bloquear(int dias, EmpresaJSON empresa, boolean isTreino) {
        try {
            OAMDService os = UtilContext.getBean(OAMDService.class);
            Date expiracao = Calendario.proximoDiaUtil(Calendario.hoje(), dias);
            empresaSelecionada = getEmpresaService().obterPorId(empresa.getChave());
            empresaSelecionada.setNomeEmpresa(empresa.getNome());

            ModuloBloqueioEnum moduloBloqueioEnum = ModuloBloqueioEnum.ZW;
            if (isTreino) {
                moduloBloqueioEnum = ModuloBloqueioEnum.TREINO_INDEPENDENTE;
            }

            preparaMensagemSuspenderOuDesbloquearEmpresa(os.bloquearEmpresa(empresaSelecionada, expiracao, empresa.getCodigo(), getUsuario(), moduloBloqueioEnum),"bloqueada");
            selecionarEmpresa(empresaSelecionada, isTreino, true);
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void desbloquear(EmpresaJSON empresa) {
        desbloquear(empresa, false, true);
    }

    public void desbloquear(EmpresaJSON empresa, boolean isTreino, boolean removerSuspensao) {
        try {
            OAMDService os = UtilContext.getBean(OAMDService.class);
            empresaSelecionada = getEmpresaService().obterPorId(empresa.getChave());
            empresaSelecionada.setNomeEmpresa(empresa.getNome());

            ModuloBloqueioEnum moduloBloqueioEnum = ModuloBloqueioEnum.ZW;
            if (isTreino) {
                moduloBloqueioEnum = ModuloBloqueioEnum.TREINO_INDEPENDENTE;
            }

            preparaMensagemSuspenderOuDesbloquearEmpresa(
                    os.desbloquearEmpresa(empresaSelecionada,
                            empresa.getCodigo(),
                            getUsuario(),
                            moduloBloqueioEnum,
                            removerSuspensao),
                    "desbloqueada");

            empresa.setDataexpiracao(null);
            selecionarEmpresa(empresaSelecionada, isTreino, true);
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void preparaMensagemSuspenderOuDesbloquearEmpresa(String mensagem, String acao){
        if (mensagem.contains("Comando executado com sucesso")) {
            mensInfo("Empresa " + empresaSelecionada.getNomeEmpresa() + " " + acao + "!");
        } else {
            mensInfo(mensagem);
        }
    }

    public void suspender(EmpresaJSON empresa) {
        try {
            OAMDService os = UtilContext.getBean(OAMDService.class);
            empresaSelecionada = getEmpresaService().obterPorId(empresa.getChave());
            empresaSelecionada.setNomeEmpresa(empresa.getNome());
            preparaMensagemSuspenderOuDesbloquearEmpresa(os.suspenderEmpresa(empresaSelecionada, empresa.getCodigo(), getUsuario()), "suspensa");
            selecionarEmpresa(empresaSelecionada);
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public List<Empresa> getListaEmpresas() {
        return listaEmpresas;
    }

    public void setListaEmpresas(List<Empresa> listaEmpresas) {
        this.listaEmpresas = listaEmpresas;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public Empresa getEmpresaSelecionada() {
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(Empresa empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public String getImagemAtual() {
        return imagemAtual;
    }

    public void setImagemAtual(String imagemAtual) {
        this.imagemAtual = imagemAtual;
    }

    public void selecionarUnidEmpresa(EmpresaJSON empresa) {
        try {
            configurandoRede = false;
            setGerarCobrancaPacto(true);
            setJustificativa("");
            setQtdCreditoAdicionar(0);
            setEmpresaUnidSelecionada(empresa);
            if (!empresaUnidSelecionada.getTipocobrancapacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {
                setQtdCreditoAdicionar(getEmpresaUnidSelecionada().getQtdCreditoUtilizadoPosPagoPacto());
            }
        } catch (Exception e) {
            mensErro(e);
        }
    }

    public void selecionarUnidEmpresaConfigurar(EmpresaJSON empresa) {
        try {
            setListaCreditoPacto(new ArrayList<>());
            setCreditoPactoSelecionado(0);
            configurandoRede = false;
            setEmpresaUnidSelecionadaConfigurar(empresa);
            if (!empresaUnidSelecionada.getTipocobrancapacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {
                setQtdCreditoAdicionar(getEmpresaUnidSelecionada().getQtdCreditoUtilizadoPosPagoPacto());
            }

            CreditoPactoService es = UtilContext.getBean(CreditoPactoService.class);
            setListaCreditoPacto(es.obterTodos());

            if (empresa.getTipocobrancapacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {
                EmpresaFinanceiroService efs = UtilContext.getBean(EmpresaFinanceiroService.class);
                EmpresaFinanceiro empresaFinanceiro = efs.obterEmpresa(empresa.getCodigo(), empresa.getChave());
                if (empresaFinanceiro != null) {
                    CreditoPactoEmpresaService credService = UtilContext.getBean(CreditoPactoEmpresaService.class);
                    CreditoPactoEmpresa creditoPactoEmpresa = credService.consultarPorEmpresaFinanceiro(empresaFinanceiro);
                    if (creditoPactoEmpresa != null) {
                        setCreditoPactoSelecionado(creditoPactoEmpresa.getCreditoPacto().getCodigo());
                    }
                }
            }
        } catch (Exception e) {
            mensErro(e);
        }
    }

    public void selecionarEmpresa(Empresa empresa) {
        boolean isTreino = empresa != null &&
                !empresa.getModulos().contains("ZW")
                && empresa.getModulos().contains("TR");
        selecionarEmpresa(empresa, isTreino, false);
    }

    public void selecionarEmpresa(Empresa empresa, boolean isTreino, boolean openConfig) {
        try {
            setRedeDCC(null);
            this.setEmpresaRecursoPadrao(null);
            this.setTiposInfoMigracaoPadrao(new ArrayList<>());
            this.setTiposInfoMigracaoPadraoAnterior(new ArrayList<>());
            empresaSelecionada = getEmpresaService().obterPorId(empresa.getChave());
            empresaSelecionada.setUrlLogo(empresa.getUrlLogo());
            empresaSelecionada.setModulosEmpresa(new ArrayList<>(Arrays.asList(empresaSelecionada.getModulos().split("\\,"))));
            empresaSelecionada.setModulosEmpresaTreino(new ArrayList<>(Arrays.asList(empresaSelecionada.getModulos().split("\\,"))));

            if (openConfig) {
                setFerramentaAtual(empresaSelecionada.getFerramentaAtual());

                OAMDService os = UtilContext.getBean(OAMDService.class);
                os.carregarEmpresas(empresaSelecionada, getUsuario(), isTreino);

                EmpresaFinanceiroService efs = UtilContext.getBean(EmpresaFinanceiroService.class);
                EmpresaFinanceiro empresaFinanceiro = efs.obterPorChave(empresa.getChave());
                if (empresaFinanceiro != null && empresaFinanceiro.getRedeEmpresa() != null) {
                    redeDCC = empresaFinanceiro.getRedeEmpresa();
                }

                LogBloqueioService lbs = UtilContext.getBean(LogBloqueioService.class);
                lbs.obterPor(empresaSelecionada);

                LogProcessoService lps = UtilContext.getBean(LogProcessoService.class);
                lps.obterPor(empresaSelecionada);

                addEmpresaHistorico(empresa);
            }
        } catch (Exception e) {
            mensErro(e);
            e.printStackTrace();
        }
    }

    private void addEmpresaHistorico(Empresa empresa){
        List<String> historico = getChavesRecentes();
        if (historico.contains(empresa.getChave())) {
            return;
        }
        historico.add(empresa.getChave());

        empresasHistorico.add(0, empresa);
        if (historico.size() > 12) {
            historico.remove(0);
            for (Empresa emp : new ArrayList<>(empresasHistorico)) {
                if (!historico.contains(emp.getChave())) {
                    empresasHistorico.remove(emp);
                }
            }
        }
        MemCachedManager.getInstance().gravar(getIdentificadorMemcached(), historico);
    }

    private List<String> getChavesRecentes(){
        Object lido = MemCachedManager.getInstance().ler(getIdentificadorMemcached());
        return lido == null ? new ArrayList<>() : (List<String>) lido;
    }

    private String getIdentificadorMemcached(){
        return "user_"+getUsuario().getCodigo()+"_historico";
    }

    public void selecionarEmpresaJSON(EmpresaJSON json) {
        try {
            empresaSelecionada = getEmpresaService().obterPorId(json.getChave());
            OAMDService os = UtilContext.getBean(OAMDService.class);
            os.carregarEmpresas(empresaSelecionada, getUsuario(), false);
        } catch (Exception e) {
            mensErro(e);
        }
    }

    public void selecionarCarteiraPMG(Empresa empresa) {
        try {
            empresaSelecionada = empresa;
            if (empresaSelecionada.getUsuarioCarteira() == null
                    || empresaSelecionada.getUsuarioCarteira().getCodigo() == null
                    || empresaSelecionada.getUsuarioCarteira().getCodigo() == 0) {
                codigoCarteira = null;
            } else {
                codigoCarteira = empresaSelecionada.getUsuarioCarteira().getCodigo();
            }
        } catch (Exception e) {
            mensErro(e);
        }
    }

    public void reset() {
        listaEmpresasFiltrada = new ArrayList<>(listaEmpresas);
        filtroTipoServidor = FiltrosEnum.TODOS;
        infra = null;
        filtroNome = "";

    }

    public void filtrarLocais(final String filtro) {
        filtroTipoServidor = FiltrosEnum.valueOf(filtro);
        filtrar();
    }

    public void filtrarInfra(InfoInfraEnum inf) {
        infra = inf;
        filtrar();
    }

    public void todasInfras(){
        reset();
        filtrar();
    }

    public void mais() {
        index += 50;
        listaEmpresasFiltradaSub = listaEmpresasFiltrada.size() < index ?
                listaEmpresasFiltrada :
                listaEmpresasFiltrada.subList(0, index);
    }

    public void filtrar() {
        EmpresaService es = getEmpresaService();

        scroll = false;
        index = 0;
        listaEmpresasFiltrada = new ArrayList<>();
        List<Empresa> lista = new ArrayList<>();
        if (filtroTipoServidor == FiltrosEnum.TODOS && !visualizacao.equals(VisualizacaoEnum.HOME)) {
            visualizacao = VisualizacaoEnum.GRADE;
        }
        //Tipos de Filtro
        if ((filtroTipoServidor == FiltrosEnum.TODOS) || (filtroTipoServidor == FiltrosEnum.TOTAL_EMPRESAS)) {
            lista = new ArrayList<>(listaEmpresas);
        } else if (filtroTipoServidor == FiltrosEnum.POSSUEM_TREINO) {
            for (Empresa empresa : listaEmpresas) {
                if (empresa.getModulos().contains("TR")) {
                    lista.add(empresa);
                }
            }
        } else if (filtroTipoServidor == FiltrosEnum.TEM_BLOQUEIO) {
            if (getTotalEmpresas() == 0) {
                empresasConcatenadas.clear();
                contarEmpresas();
            }
            for (Empresa empresa : listaEmpresas) {
                FOR_JSON:
                for (EmpresaJSON eJson : empresa.getEmpresas()) {
                    if (eJson.getDataexpiracao() != null || !eJson.isDisponivel()) {
                        lista.add(empresa);
                        break FOR_JSON;
                    }
                }
            }

        } else if (filtroTipoServidor == FiltrosEnum.USO_INTERNO) {
            try {
                lista = es.obterTodos(true, null, true, null, infra);
            } catch (ServiceException ex) {
                mensErro(ex);
                Logger.getLogger(OamdControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else {
            for (Empresa empresa : listaEmpresas) {
                if ((empresa.getUsarBDLocal() && filtroTipoServidor.equals(FiltrosEnum.LOCAWEB))
                        || (!empresa.getUsarBDLocal() && filtroTipoServidor.equals(FiltrosEnum.EXTERNO))) {
                    lista.add(empresa);
                }
            }
        }
        if (infra != null) {
            try {
                lista = es.obterTodos(true, null, false, null, infra);
            } catch (ServiceException ex) {
                mensErro(ex);
                Logger.getLogger(OamdControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }

        lista.forEach(empresa -> {
            try {
                empresa.setTagsSelecionadas(es.setarTags(empresa));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        });

        if (filtroNome == null || filtroNome.isEmpty()) {
            listaEmpresasFiltrada.addAll(lista);
        } else {
            //TODO HUMBERTO onde vai ter que arrumar para buscar pelo codigo financeiro ... #ROLEINFINITO Precisa fazer alteração nas ENTIDADES
            for (Empresa empresa : lista) {
                if ((empresa.getName() + " " + empresa.getChave() + (empresa.getIdentificadorEmpresa() != null ? empresa.getIdentificadorEmpresa() + " " + empresa.getChave() : "")+ empresa.getEmpresas().toString()).toLowerCase().contains(filtroNome.toLowerCase())
                        || empresa.getTagsSelecionadas().contains(filtroNome.trim().toLowerCase())
                        || empresa.getChave().toLowerCase().contains(filtroNome.trim().toLowerCase())) {
                    listaEmpresasFiltrada.add(empresa);
                }
            }
        }

        preencherInformacoesEmpresa(es, listaEmpresasFiltrada);

        if (visualizacao == VisualizacaoEnum.LISTA) {
            empresasConcatenadas = concatenarEmpresas();
            setListaTela(empresasConcatenadas);
            listaEmpresasFiltradaSub = listaEmpresasFiltrada;
        } else {
            index = 0;
            setListaTela(listaEmpresasFiltrada);
            mais();
        }

    }

    public void contarEmpresas() {
        OAMDService oamds = UtilContext.getBean(OAMDService.class);
        try {
            oamds.consultarTodasEmpresas(listaEmpresas, getUsuario());
            visualizacao = VisualizacaoEnum.LISTA;
            filtroTipoServidor = FiltrosEnum.TOTAL_EMPRESAS;
            filtrar();
        } catch (ServiceException ex) {
            mensErro(ex.getMessage());
            Logger.getLogger(OamdControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List<EmpresaJSON> concatenarEmpresas() {
        List<EmpresaJSON> tmp = new ArrayList<EmpresaJSON>();
        for (Empresa e : listaEmpresasFiltrada) {
            List<EmpresaJSON> json = e.getEmpresas();
            if (json.isEmpty()) {
                EmpresaJSON ej = new EmpresaJSON();
                ej.setDisponivel(e.isDisponivel());
                ej.setNome(e.getName());
                ej.setGrupoEmpresarial(e.getName());
                ej.setChave(e.getChave());
                ej.setUrlZW(e.getUrlLoginPreparada());
                ej.setUrlTreino(e.getUrlTreino());
                ej.setModulos(e.getModulos());
                ej.setUsoInterno(e.getUsoInterno());
                json.add(ej);
            } else {
                for (EmpresaJSON ej : json) {
                    ej.setChave(e.getChave());
                    ej.setGrupoEmpresarial(e.getName());
                    ej.setUrlZW(e.getUrlLoginPreparada());
                    ej.setUrlTreino(e.getUrlTreino());
                    ej.setModulos(e.getModulos());
                    ej.setUsoInterno(e.getUsoInterno());
                }
            }
            tmp.addAll(json);
        }
        if (filtroTipoServidor == FiltrosEnum.TEM_BLOQUEIO) {
            List<EmpresaJSON> excluir = new ArrayList(tmp);
            for (EmpresaJSON ej : excluir) {
                if ((ej.isDisponivel() && (ej.getDataexpiracao() == null)) || (ej.isUsoInterno())) {
                    tmp.remove(ej);
                }
            }
        }
        Ordenacao.ordenarLista(tmp, "nome");
        return tmp;
    }

    public OamdControle() {
    }

    @PostConstruct
    public void posConstrucao() {
        try {
            obterPwd();
            consultarEmpresas();
            randomImage();
            montarUsuariosPMG();
            obterRecentes();
            popularListaGruposChatMoviDesk();
            montarModulosEmpresa();
        } catch (Exception ex) {
            mensErro(ex);
        }
    }

    private void obterPwd() {
        try {
            ConfiguracaoSistemaService cs = UtilContext.getBean(ConfiguracaoSistemaService.class);
            pwdPactoBR = cs.obterValorPorConfiguracao(ConfiguracaoEnum.SENHA_PACTOBR);
            pwdAdmin = cs.obterValorPorConfiguracao(ConfiguracaoEnum.SENHA_ADMIN);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void obterRecentes(){
        empresasHistorico = new ArrayList<>();
        List<String> historico = getChavesRecentes();
        List<String> inseridas = new ArrayList<>();
        for(String cr : historico){
            for (Empresa empresa : listaEmpresas) {
                if(cr.equals(empresa.getChave()) && !inseridas.contains(cr)){
                    inseridas.add(empresa.getChave());
                    empresasHistorico.add(0,empresa);
                    break;
                }
            }
        }
    }

    public void montarUsuariosPMG() {
        try {
            usuariosPMG = new ArrayList<>();
            usuariosPMGSelect = new ArrayList<>();
            UsuarioService usuarioService = UtilContext.getBean(UsuarioService.class);
            List<Usuario> todos = usuarioService.obterTodos();
            for (Usuario us : todos) {
                if (us.getPmg()) {
                    usuariosPMG.add(us);
                    usuariosPMGSelect.add(new SelectItem(us.getCodigo(), us.getUserName()));
                }
            }
        } catch (Exception e) {
            mensErro(e);
        }
    }

    public void montarModulosEmpresa() {
        modEmpresas = new ArrayList<>();
        modEmpresas.add(new SelectItem("ZW", "ZW - Zillyon Web"));
        modEmpresas.add((new SelectItem("TR", "TR - TreinoWeb")));
        modEmpresas.add((new SelectItem("CRM", "CRM - CRMWeb")));
        modEmpresas.add((new SelectItem("FIN", "FIN - Financeiro Web")));
        modEmpresas.add((new SelectItem("CE", "CE - Central de Eventos")));
        modEmpresas.add((new SelectItem("EST", "EST - Agenda Studio")));
        modEmpresas.add((new SelectItem("GP", "GP - Gestão Personal")));
        modEmpresas.add((new SelectItem("SLC", "SLC - Aula Cheia")));
        modEmpresas.add((new SelectItem("SBX", "SBX - SmartBox")));
        modEmpresas.add((new SelectItem("GOR", "GOR - Game Of Results")));
        modEmpresas.add((new SelectItem("ZAA", "ZAA - ZWAuto (Toten)")));
        modEmpresas.add((new SelectItem("NTR", "NTR - Novo Treino")));
        modEmpresas.add((new SelectItem("NCR", "NCR - Novo Crossfit")));
        modEmpresas.add((new SelectItem("NAV", "NAV - Novo Avaliação")));
        modEmpresas.add((new SelectItem("NVO", "NVO - Novo Vendas Online")));
        modEmpresas.add((new SelectItem("IA", "IA - Pacto I.A")));
        modEmpresas.add((new SelectItem("OPB", "OPB - Open Bank")));
        modEmpresas.add((new SelectItem("GRD", "GRD - Graduação")));
        modEmpresas.add((new SelectItem("NZW", "NZW - Novo ZW")));
        modEmpresas.add((new SelectItem(Modulo.PIX.getSiglaModulo(), "PIX - Pix")));
        modEmpresas.add((new SelectItem(Modulo.PACTO_PAY.getSiglaModulo(), "PAY - PactoPay")));
        modEmpresas.add(new SelectItem(Modulo.FACILITE_PAY.getSiglaModulo(), "FAC - Fypay"));
        modEmpresas.add(new SelectItem(Modulo.NICHO.getSiglaModulo(), "NIC - Nicho"));
    }

    private void randomImage() {
        if (!urlImages.isEmpty()) {
            Random rand = new Random();
            imagemAtual = urlImages.get(rand.nextInt(urlImages.size()));
        }
    }

    public void gravarCarteira() {
        try {
            if (codigoCarteira != null && codigoCarteira > 0) {
                empresaSelecionada.setUsuarioCarteira(new Usuario());
                empresaSelecionada.getUsuarioCarteira().setCodigo(codigoCarteira);
            } else {
                empresaSelecionada.setUsuarioCarteira(null);
            }
            empresaSelecionada = getEmpresaService().alterar(empresaSelecionada);
            consultarEmpresasFiltro();
            mensInfo("Carteira atualizada com sucesso!");
        } catch (Exception e) {
            mensErro(e);
        }
    }

    public void consultarEmpresas() throws Exception {
        visualizacao = VisualizacaoEnum.HOME;
        filtroTipoServidor = FiltrosEnum.TODOS;
        filtroNome = "";
        scroll = true;
        consultarEmpresas(null);
        mais();

        preencherInformacoesEmpresa(getEmpresaService(), listaEmpresasFiltradaSub);
    }

    public void consultarEmpresasFiltro() throws Exception {
        consultarEmpresas(codigoCarteiraFiltro);

    }

    public void consultarEmpresas(Integer codigoUsuario) throws Exception {
        if (getViewUtils().getViewId().contains("empresas")) {
            listaEmpresas = new ArrayList<>();
            List<Empresa> tmpEmpresas = getEmpresaService().obterTodos(false, codigoUsuario, false, null, null);
            List<Empresa> listaTodasEmpresas = new ArrayList<>();
            if (getUsuario().getPerfil().getNome().equals(PerfilPadraoEnum.COMERCIAL.getNomePerfil())) {
                listaTodasEmpresas.addAll(tmpEmpresas.stream()
                        .filter(it -> it.getCarteiras().contains(CarteirasEnum.ADM))
                        .collect(Collectors.toList()));
            } else if (getUsuario().getPerfil().getNome().equals(PerfilPadraoEnum.PERSONAL_FIT.getNomePerfil())) {
                listaTodasEmpresas.addAll(tmpEmpresas.stream()
                        .filter(it -> it.getModulos().contains(Modulo.PERSONAL_FIT.getSiglaModulo()))
                        .collect(Collectors.toList()));
            } else if (getUsuario().getCarteiras() != null && getUsuario().getCarteiras().size() > 0) {
                for (CarteirasEnum carteira : getUsuario().getCarteiras()) {
                    listaTodasEmpresas.addAll(tmpEmpresas.stream()
                            .filter(it -> it.getCarteiras().contains(carteira))
                            .collect(Collectors.toList()));
                }
            } else {
                listaTodasEmpresas.addAll(tmpEmpresas);
            }

//            preencherInformacoesEmpresa(es, listaTodasEmpresas);
            listaEmpresas.addAll(listaTodasEmpresas);

            tentarBaixarLogo = false;
            listaEmpresas = Ordenacao.ordenarLista(listaEmpresas, "visualName");
            listaEmpresasFiltrada = new ArrayList<Empresa>(listaEmpresas);

        }
        codigoCarteiraFiltro = codigoUsuario;
    }

    private EmpresaService getEmpresaService() {
        return UtilContext.getBean(EmpresaService.class);
    }

    private DiscoveryService getDiscoveryService() {
        return UtilContext.getBean(DiscoveryService.class);
    }

    private void preencherInformacoesEmpresa(EmpresaService es, List<Empresa> listaTodasEmpresas) {
        for (Empresa emp : listaTodasEmpresas) {
            if (emp.getDadosPreenchidos()) {
                continue;
            }
            try {
                JSONObject json = new JSONObject();
                json.put("key", emp.getChave());
                json.put("nomeUsuario", getUsuario().getUserName());
                json.put("codigoUsuario", getUsuario().getCodigo());
                json.put("urlOamd", Uteis.getURL());
                json.put("dataLink", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd-MM-yyyy HH:mm"));
                String lgn = Uteis.encriptar(json.toString(), "chave_game_r35u17s");
                emp.setUrlLoginGame(emp.getUrlGame()+"/oid?lgn="+lgn);

                emp.setTagsSelecionadas(es.setarTags(emp));
                emp.preencherLogo(tentarBaixarLogo,
                        ((ServletContext) FacesContext.getCurrentInstance().
                                getExternalContext().getContext()).getRealPath("imagens"));
                emp.setDadosPreenchidos(true);
            } catch (Exception ignored) {
            }
        }
    }

    public String getDescricaoCarteira() {
        if (codigoCarteiraFiltro != null && codigoCarteiraFiltro == 0) {
            return "Grupos empresariais fora das carteiras do PMG";
        }
        if (codigoCarteiraFiltro != null) {
            for (Usuario s : usuariosPMG) {
                if (s.getCodigo() == codigoCarteiraFiltro) {
                    return "Grupos empresariais na carteira de " + s.getUserName();
                }
            }
        }
        return "";
    }

    public void selecionarEmpresa() {
        Empresa item = (Empresa) FacesContext.getCurrentInstance().getExternalContext().getRequestMap().get("item");
        setEmpresaSelecionada(item);
    }

    public void selecionarEmpresaZW() {
        try {
            for (EmpresaWS empresaWS : getUsuarioZW().getEmpresasWS()) {
                if (empresaWS.getCodigo().equals(getUsuarioZW().getEmpresa().getCodigo())) {
                    EmpresaWS newEmpresaWS = new EmpresaWS();
                    newEmpresaWS.setCodigo(empresaWS.getCodigo());
                    newEmpresaWS.setUsarNFCe(empresaWS.isUsarNFCe());
                    newEmpresaWS.setUsarNFSe(empresaWS.isUsarNFSe());
                    newEmpresaWS.setNome(empresaWS.getNome());
                    newEmpresaWS.setChaveNFSe(empresaWS.getChaveNFSe());
                    newEmpresaWS.setTotalDiasExtras(empresaWS.getTotalDiasExtras());

                    getUsuarioZW().setEmpresa(newEmpresaWS);

                    try {
                        UsuarioService us = (UsuarioService) UtilContext.getBean(UsuarioService.class);
                        getUsuarioZW().setPactoPay(us.permitePactoPay(getEmpresaSelecionada(), getUsuarioZW()));
                    } catch (Exception ignored) {
                    }
                    return;
                }
            }
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public List<Empresa> getListaEmpresasFiltrada() {
        return listaEmpresasFiltrada;
    }

    public void setListaEmpresasFiltrada(List<Empresa> listaEmpresasFiltrada) {
        this.listaEmpresasFiltrada = listaEmpresasFiltrada;
    }

    public String getFiltroNome() {
        return filtroNome;
    }

    public void setFiltroNome(String filtroNome) {
        this.filtroNome = filtroNome;
    }

    public void limparCampo(){
        filtroNome = "";
        filtrar();
    }


    public Integer getTotalEmpresas() {
        if (listaEmpresas != null && !listaEmpresas.isEmpty()) {
            return sumFrom(listaEmpresas).getnEmpresasBanco();
        }
        return 0;
    }

    public Integer getCodigoCarteira() {
        return codigoCarteira;
    }

    public void setCodigoCarteira(Integer codigoCarteira) {
        this.codigoCarteira = codigoCarteira;
    }

    public List<SelectItem> getUsuariosPMGSelect() {
        return usuariosPMGSelect;
    }

    public void setUsuariosPMGSelect(List<SelectItem> usuariosPMGSelect) {
        this.usuariosPMGSelect = usuariosPMGSelect;
    }

    public List<Usuario> getUsuariosPMG() {
        return usuariosPMG;
    }

    public void setUsuariosPMG(List<Usuario> usuariosPMG) {
        this.usuariosPMG = usuariosPMG;
    }

    public Integer getCodigoCarteiraFiltro() {
        return codigoCarteiraFiltro;
    }

    public void setCodigoCarteiraFiltro(Integer codigoCarteiraFiltro) {
        this.codigoCarteiraFiltro = codigoCarteiraFiltro;
    }

    public FiltrosEnum getFiltroTipoServidor() {
        return filtroTipoServidor;
    }

    public void setFiltroTipoServidor(FiltrosEnum filtroTipoServidor) {
        this.filtroTipoServidor = filtroTipoServidor;
    }

    public VisualizacaoEnum getVisualizacao() {
        return visualizacao;
    }

    public void mudarVisualizacao(String v) {
        visualizacao = VisualizacaoEnum.valueOf(v);
    }

    public void setVisualizacao(VisualizacaoEnum visualizacao) {
        this.visualizacao = visualizacao;
    }

    public void toggleVisualizacao(final String modo) {
        this.visualizacao = VisualizacaoEnum.valueOf(modo);
        filtrar();
    }

    public List<EmpresaJSON> getEmpresasConcatenadas() {
        return empresasConcatenadas;
    }

    public void setEmpresasConcatenadas(List<EmpresaJSON> empresasConcatenadas) {
        this.empresasConcatenadas = empresasConcatenadas;
    }

    public void postProcessXLS(Object document) {
        HSSFWorkbook wb = (HSSFWorkbook) document;
        HSSFSheet sheet = wb.getSheetAt(0);
        sheet.setDefaultColumnWidth(32);
        HSSFRow header = sheet.getRow(0);

        HSSFCellStyle cellStyle = wb.createCellStyle();
        HSSFFont font = wb.createFont();
        font.setColor(HSSFColor.WHITE.index);
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        cellStyle.setFont(font);
        cellStyle.setFillForegroundColor(HSSFColor.BLACK.index);
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);

        for (int i = 0; i < header.getPhysicalNumberOfCells(); i++) {
            HSSFCell cell = header.getCell(i);
            cell.setCellStyle(cellStyle);
        }
    }

    public void abrirModal(Empresa empresa) {
        logado = false;
        selecionarEmpresa(empresa);
        MenuControle mc = (MenuControle) JSFUtilities.getFromSession(MenuControle.class);
        if (mc != null) {
            mc.setDataSistema(null);
        }
        JSFUtilities.removeFromSession("logadozw");
        doExecuteFunction("modalLogin.show();");
    }

    public void logar() {
        logar(Aplicacao.getProp(Aplicacao.userHolding),
                    Aplicacao.getProp(Aplicacao.senhaHolding));
    }

    public void logar(String nameUser) {
        String userSenha;
        if (nameUser.equals("PACTOBR")) {
            userSenha = pwdPactoBR;
        } else {
            userSenha = pwdAdmin;
        }
        logar(nameUser, userSenha);
    }

    public void logar(String nameUser,String userSenha) {
        UsuarioService us = UtilContext.getBean(UsuarioService.class);
        try {
            if (logado) {
                return;
            }
            logando = true;

            UsuarioZW u;
            if (getEmpresaSelecionada().getModulos().contains("ZW")) {
                u = us.validarUsuarioZW(getEmpresaSelecionada(), getEmpresaSelecionada().getChave(), nameUser, userSenha, getEmpresaSelecionada().getRoboControleSemHTTPS(), getUsuario().getUserName());
            } else {
                u = us.validarUsuarioTR(getEmpresaSelecionada().getChave(),
                        nameUser, userSenha, getEmpresaSelecionada().getUrlTreino(),
                        getUsuario().getUserName());
            }

            if (u == null) {
                logado = false;
                JSFUtilities.removeFromSession("logadozw");
                throw new Exception("login.usuariosenhaInvalida");
            } else {
                u.adicionarModulosPorStringSiglas(getEmpresaSelecionada().getModulos());
                u.setEmpresaOAMD(getEmpresaSelecionada());
                u.setUrlTreino(getEmpresaSelecionada().getUrlTreino());
                u.setIdioma(getEmpresaSelecionada().getIdiomaBanco() == null ? IdiomaBancoEnum.PT.name().toLowerCase() : getEmpresaSelecionada().getIdiomaBanco().name().toLowerCase());
                if (u.getForaDoHorario() || u.getSenhaExpirada()) {
                    throw new Exception(u.getMensagem());
                }
                u.setSenha(userSenha);
                if (nameUser.equals("PACTOBR") || nameUser.equals(Aplicacao.getProp(Aplicacao.userHolding))) {
                    validarUsuarioNovoTreino(u);
                    validarUsuarioZWFront(u);
                }
                JSFUtilities.storeOnSession("logadozw", u);
                logado = true;
                doExecuteFunction("logadoModal.show();modalLogin.hide();logarWiki();");
            }
        } catch (Exception e) {
            mensErro("Exeção logar " + e.getMessage());
            Uteis.logar(e, OamdControle.class);
        } finally {
            logando = false;
        }
    }

    private void validarUsuarioNovoTreino(UsuarioZW usuarioZW) {
        try {
            Uteis.logar("Validando UsuarioZWFront");
            if (!UteisValidacao.emptyList(usuarioZW.getModulosEnum())
                    && (usuarioZW.getModulosEnum().contains(Modulo.NOVO_TREINO)
                    || usuarioZW.getModulosEnum().contains(Modulo.NOVO_CROSSFIT)
                    || usuarioZW.getModulosEnum().contains(Modulo.NOVO_AVALIACAO_FISICA))) {
                JSONObject jsonBody = new JSONObject();
                jsonBody.put("chave", getEmpresaSelecionada().getChave());
                jsonBody.put("username", usuarioZW.getUserName());
                jsonBody.put("senha", "");
                Map<String, String> maps = new HashMap<String, String>();
                maps.put("Content-Type", "application/json");
                String urlTreino = getEmpresaSelecionada().getUrlTreinoSemHTTPS() + "/prest/login/validar-usuario-movel/";

                String request = ExecuteRequestHttpService.executeHttpRequest(urlTreino, jsonBody.toString(), maps, "POST", "UTF-8");
                Uteis.logar(request);
                JSONObject json = new JSONObject(request);
                usuarioZW.setToken(json.optString("content"));
                usuarioZW.setUrlNT(getEmpresaSelecionada().getTreinofront());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void validarUsuarioZWFront(UsuarioZW usuarioZW) {
        try {
            if (!UteisValidacao.emptyList(usuarioZW.getModulosEnum()) &&
                    (usuarioZW.getModulosEnum().contains(Modulo.NOVO_ZW) || usuarioZW.getModulosEnum().contains(Modulo.PACTO_PAY))) {
                String urlDiscovery = Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + getEmpresaSelecionada().getChave();
                String responseDiscovery = HttpRequestUtil.executeRequestGET(urlDiscovery, 5000, 5000);
                JSONObject objResponseDiscovery = new JSONObject(responseDiscovery);
                JSONObject content = objResponseDiscovery.optJSONObject("content");
                if (content != null) {
                    JSONObject serviceUrls = content.optJSONObject("serviceUrls");
                    if (serviceUrls != null) {
                        String autenticacaoUrl = serviceUrls.optString("autenticacaoUrl");
                        JSONObject jsonBody = new JSONObject();
                        jsonBody.put("chave", getEmpresaSelecionada().getChave());
                        jsonBody.put("username", usuarioZW.getUserName());
                        jsonBody.put("senha", usuarioZW.getSenha());

                        Map<String, String> maps = new HashMap<>();
                        maps.put("Content-Type", "application/json");

                        String msAutenticacao = autenticacaoUrl + "/aut/login";
                        String responseAutenticacao = ExecuteRequestHttpService.executeHttpRequest(msAutenticacao, jsonBody.toString(), maps, "POST", "UTF-8");
                        JSONObject objResponseAutenticacao = new JSONObject(responseAutenticacao);
                        JSONObject contentAut = objResponseAutenticacao.optJSONObject("content");
                        if (contentAut != null) {
                            String token = contentAut.optString("token");
                            usuarioZW.setToken(token);
                            usuarioZW.setUrlNZW(getEmpresaSelecionada().getZwfront());
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public UsuarioZW getUsuarioZW() {
        return (UsuarioZW) JSFUtilities.getFromSession("logadozw");
    }

    public boolean isLogado() {
        return logado;
    }

    public void setLogado(boolean logado) {
        this.logado = logado;
    }

    public boolean isLogando() {
        return logando;
    }

    public void setLogando(boolean logando) {
        this.logando = logando;
    }

    public void logout() {
        logado = false;
        doExecuteFunction("logadoModal.hide();");
        JSFUtilities.removeFromSession("logadozw");
    }

    public static List<String> getUrlImages() {
        return urlImages;
    }

    public static void setUrlImages(List<String> urlImages) {
        OamdControle.urlImages = urlImages;
    }

    public boolean isTentarBaixarLogo() {
        return tentarBaixarLogo;
    }

    public void setTentarBaixarLogo(boolean tentarBaixarLogo) {
        this.tentarBaixarLogo = tentarBaixarLogo;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public List<Empresa> getListaEmpresasFiltradaSub() {
        return listaEmpresasFiltradaSub;
    }

    public void setListaEmpresasFiltradaSub(List<Empresa> listaEmpresasFiltradaSub) {
        this.listaEmpresasFiltradaSub = listaEmpresasFiltradaSub;
    }

    public boolean isScroll() {
        return scroll;
    }

    public void setScroll(boolean scroll) {
        this.scroll = scroll;
    }

    public List<SelectItem> getSelectItemTipoCobrancaPacto() {
        List<SelectItem> retorno = new ArrayList<SelectItem>();
        for (TipoCobrancaPactoEnum tipo : TipoCobrancaPactoEnum.values()) {
            if (configurandoRede && (tipo.equals(REDE_EMPRESAS_PRE_PAGO) || tipo.equals(POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS))) {
                retorno.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
            } else if (!configurandoRede) { // tela de adição de creditos por empresa
                retorno.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(retorno, "label");
        return retorno;
    }

    public Date getDataPrimeiraParcela() {
        return Uteis.somarDias(Calendario.hoje(), 16);
    }

    public Double getValorTotal() {
        return getEmpresaUnidSelecionada().getValorcreditopacto() * getQtdCreditoAdicionar();
    }

    public String getValorTotalApresentar() {
        return Formatador.formatarValorMonetario(getValorTotal());
    }

    public String getValorParcelaApresentar() {
        return Formatador.formatarValorMonetario(getValorTotal() / getEmpresaUnidSelecionada().getQtdparcelascobrancapacto());
    }

    public String getLinkGame() {
        return Aplicacao.getProp(Aplicacao.urlGame);
    }

    public List<Empresa> getEmpresasHistorico() {
        return empresasHistorico;
    }

    public void setEmpresasHistorico(List<Empresa> empresasHistorico) {
        this.empresasHistorico = empresasHistorico;
    }

    public boolean getApresentarCS(){
        return getOamdPacto() &&
                (getUsuario().getAdm() != null && getUsuario().getAdm() ||
                        PerfilPadraoEnum.COORDENADOR.getNomePerfil().equals(getUsuario().getPerfil().getNome()) ||
                        PerfilPadraoEnum.USUARIO_CS.getNomePerfil().equals(getUsuario().getPerfil().getNome())) ;
    }

    public boolean isPermiteCriarEmpresa(){
        return getOamdPacto() && ((getUsuario().getAdm() != null && getUsuario().getAdm()) || getUsuario().isPermiteCriarEmpresa());
    }

    public boolean isApresentarMenuProdutosPacto(){
        return isApresentarProdutosPacto() || isApresentarProdutosPactoCupomDesconto();
    }

    public boolean isApresentarProdutosPacto(){
        return getOamdPacto() && (getUsuario().getAdm() || getUsuario().isPermiteProdutoPacto());
    }

    public boolean isApresentarProdutosPactoCupomDesconto(){
        return getOamdPacto() && (getUsuario().getAdm() || getUsuario().isPermiteProdutoPactoCupom());
    }


    public void addCreditoRede(RedeEmpresa rede){
        try {
            configurandoRede = true;
            redeDCC = rede;
            setGerarCobrancaPacto(true);
            setJustificativa("");
            setQtdCreditoAdicionar(0);

            EmpresaJSON e = new EmpresaJSON();
            e.setNome(rede.getNome());
            e.setTipocobrancapacto(REDE_EMPRESAS_PRE_PAGO.getCodigo());
            e.setDtultimacobrancapacto(rede.getDtultimacobrancapacto());
            e.setValorcreditopacto(rede.getValorcreditopacto());
            e.setQtdCreditoUtilizadoPosPagoPacto(rede.getQtdCreditoUtilizadoPosPagoPacto());
            e.setGerarnotafiscalcobrancapacto(rede.getGerarNotaFiscal());
            e.setGerarcobrancaautomaticapacto(rede.getGerarCobrancaAutomaticaPacto());
            e.setQtdcreditorenovarprepagocobrancapacto(rede.getQtdCreditoRenovar());
            e.setQtdparcelascobrancapacto(rede.getQtdParcelas());
            e.setDiavencimentocobrancapacto(rede.getDiaVencimentoCobrancaPacto());
            setEmpresaUnidSelecionada(e);

            if (!empresaUnidSelecionada.getTipocobrancapacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {
                setQtdCreditoAdicionar(getEmpresaUnidSelecionada().getQtdCreditoUtilizadoPosPagoPacto());
            }
        } catch (Exception e) {
            mensErro(e);
        }
    }

    public void configurarCreditoRede(RedeEmpresa rede){
        try {
            configurandoRede = true;
            redeDCC = rede;
            setGerarCobrancaPacto(true);
            setJustificativa("");
            setQtdCreditoAdicionar(0);

            setCodEmpresaFinanceiroCobranca(0);
            if (rede.getEmpresaFinanceiroCobranca() != null) {
                setCodEmpresaFinanceiroCobranca(rede.getEmpresaFinanceiroCobranca().getCodigo());
            }

            EmpresaJSON e = new EmpresaJSON();
            e.setNome(rede.getNome());
            e.setTipocobrancapacto(REDE_EMPRESAS_PRE_PAGO.getCodigo());
            e.setDtultimacobrancapacto(rede.getDtultimacobrancapacto());
            e.setValorcreditopacto(rede.getValorcreditopacto());
            e.setQtdCreditoUtilizadoPosPagoPacto(rede.getQtdCreditoUtilizadoPosPagoPacto());
            e.setGerarnotafiscalcobrancapacto(rede.getGerarNotaFiscal());
            e.setGerarcobrancaautomaticapacto(rede.getGerarCobrancaAutomaticaPacto());
            e.setQtdcreditorenovarprepagocobrancapacto(rede.getQtdCreditoRenovar());
            e.setQtdparcelascobrancapacto(rede.getQtdParcelas());
            e.setDiavencimentocobrancapacto(rede.getDiaVencimentoCobrancaPacto());
            e.setCobrarcreditopactoboleto(rede.getCobrarCreditoPactoBoleto());
            setEmpresaUnidSelecionadaConfigurar(e);

        } catch (Exception e) {
            mensErro(e);
        }
    }

    public Boolean getConfigurandoRede() {
        return configurandoRede;
    }

    public void setConfigurandoRede(Boolean configurandoRede) {
        this.configurandoRede = configurandoRede;
    }

    public void gravarDCCRede(boolean gravarCreditos) {
        try {
            if (gravarCreditos) {
                validarDados();
            }

            RedeEmpresaService rs = UtilContext.getBean(RedeEmpresaService.class);

            redeDCC = rs.gravarCreditoRedeDCC(redeDCC,
                    gravarCreditos ? getEmpresaUnidSelecionada() : getEmpresaUnidSelecionadaConfigurar(),
                    getQtdCreditoAdicionar(),
                    getUsuario().getUserName(),
                    justificativa,
                    gerarCobrancaPacto,
                    getValorTotal(),
                    getDataPrimeiraParcela(),
                    gravarCreditos,
                    codEmpresaFinanceiroCobranca);

            if (gravarCreditos) {
                StringBuilder msg = new StringBuilder();
                msg.append("Foram adicionados nesta venda ");
                msg.append(getQtdCreditoAdicionar());
                msg.append(" créditos e agora, o saldo total da rede é de: ");
                msg.append(redeDCC.getCreditos()).append(" créditos. \n");
                if (isGerarCobrancaPacto()) {
                    msg.append("Foi lançado uma conta no Financeiro com sucesso.");
                } else {
                    msg.append("Essa operação não gerou agendamento no Financeiro.");
                }
                mensInfo(msg.toString());

                doExecuteFunction("modalDialogCobrancaPacto.hide();");
            } else {
                mensInfo("Dados salvos com sucesso.");
                doExecuteFunction("modalDialogCobrancaConfig.hide();");
            }

        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public RedeEmpresa getRedeDCC() {
        return redeDCC;
    }

    public void setRedeDCC(RedeEmpresa redeDCC) {
        this.redeDCC = redeDCC;
    }


    public void toggleFerramentaAtendimento() {
        empresaSelecionada.setFerramentaAtual(getFerramentaAtual());
    }

    public void gravarFerramentaAtendimento() {
        try {
            empresaSelecionada.setFerramentaAtual(getFerramentaAtual());

            if (empresaSelecionada.getUtilizarMoviDesk()) {
                final String retorno = AdmWSConsumer.utilizarMoviDesk(empresaSelecionada, empresaSelecionada.getUtilizarMoviDesk());
                if (houveErroIntegracaoZwSOAP(retorno)) {
                    empresaSelecionada.setUtilizarMoviDesk(!empresaSelecionada.getUtilizarMoviDesk());
                    mensErro("Não foi possível atualizar a instância ZW: " + retorno);
                    return;
                }
            }

            empresaSelecionada = getEmpresaService().alterar(empresaSelecionada);
            getEmpresaService().atualizarFerramentaAtendimentoEmpresaOAMDInfra(empresaSelecionada, getUsuario());
            mensInfo("Ferramenta de atendimento atualizada com sucesso.");
        } catch (Exception e) {
            mensErro(e);
        } finally {
            getEmpresaService().refreshConfigs(empresaSelecionada);
        }
    }

    public void toggleHabilitaModulos(boolean treino) {
        try {
            StringBuilder lstModulos = new StringBuilder();
            if (treino) {
                for (String moduloEmpresa : empresaSelecionada.getModulosEmpresaTreino()) {
                    lstModulos.append(moduloEmpresa.concat(","));
                }
            } else {
                for (String moduloEmpresa : empresaSelecionada.getModulosEmpresa()) {
                    lstModulos.append(moduloEmpresa.concat(","));
                }
            }
            boolean igual = empresaSelecionada.getModulos().equals(lstModulos.substring(0, lstModulos.length() - 1));
            empresaSelecionada.setModulos(lstModulos.substring(0, lstModulos.length() - 1));

            if (UteisValidacao.emptyString(empresaSelecionada.getMsAutenticacao()) &&
                    empresaSelecionada.getModulos().contains(Modulo.PACTO_PAY.getSiglaModulo())) {
                empresaSelecionada.setMsAutenticacao("https://auth.ms.pactosolucoes.com.br");
            }

            empresaSelecionada = getEmpresaService().alterar(empresaSelecionada);
            getEmpresaService().atualizarModulosOAMDInfra(empresaSelecionada, getUsuario());
            if (treino) {
                empresaSelecionada.setModulosEmpresaTreino(new ArrayList<>(Arrays.asList(empresaSelecionada.getModulos().split("\\,"))));
            } else {
                empresaSelecionada.setModulosEmpresa(new ArrayList<>(Arrays.asList(empresaSelecionada.getModulos().split("\\,"))));
            }
            if (!igual) {
                getDiscoveryService().reload(empresaSelecionada.getChave());
                getDiscoveryService().reloadLogin(empresaSelecionada.getChave());
            }
            mensInfo("Módulos atualizados com sucesso.");
        } catch (Exception e) {
            e.printStackTrace();
            mensErro("Erro ao realizar a operação: " + e.getMessage());
        } finally {
            getEmpresaService().refreshConfigs(empresaSelecionada);
        }
    }

    public void toggleHabilitarChatMoviDesk() {
        try {
            if (empresaSelecionada.getUtilizarChatMoviDesk() && !empresaSelecionada.getUtilizarMoviDesk()) {
                mensErro("Para ativar o CHAT do MoviDesk você precisa habilitar o próprio MoviDesk antes.");
                return;
            }

            final String retorno = AdmWSConsumer.utilizarChatMoviDesk(empresaSelecionada, empresaSelecionada.getUtilizarChatMoviDesk());
            if (houveErroIntegracaoZwSOAP(retorno)) {
                empresaSelecionada.setUtilizarChatMoviDesk(!empresaSelecionada.getUtilizarChatMoviDesk());
                mensErro("Não foi possível atualizar a instância ZW: " + retorno);
                return;
            }

            empresaSelecionada = getEmpresaService().alterar(empresaSelecionada);
            mensInfo("Operação realizada com sucesso.");
        } catch (Exception e) {
            Uteis.logar(e, OamdControle.class);
            mensErro("Erro ao realizar a operação: " + e.getMessage());
        } finally {
            getEmpresaService().refreshConfigs(empresaSelecionada);
        }
    }

    public void alterarGrupoChatMoviDesk() {
        try {
            final String retorno = AdmWSConsumer.alterarGrupoChatMovidesk(empresaSelecionada, empresaSelecionada.getGrupoChatMoviDesk());
            if (houveErroIntegracaoZwSOAP(retorno)) {
                mensErro("Não foi possível mudar o grupo de chat da empresa: " + retorno);
                return;
            }

            empresaSelecionada = getEmpresaService().alterar(empresaSelecionada);
            mensInfo("Operação realizada com sucesso.");
        } catch (Exception e) {
            Uteis.logar(e, OamdControle.class);
            mensErro("Erro ao realizar a operação: " + e.getMessage());
        } finally {
            getEmpresaService().refreshConfigs(empresaSelecionada);
        }
    }

    private boolean houveErroIntegracaoZwSOAP(final String retorno) {
        return StringUtils.isBlank(retorno)
                || retorno.equals("{}")
                || retorno.equals("[]")
                || retorno.equals("{[]}")
                || retorno.equals("[{}]")
                || StringUtils.containsIgnoreCase(retorno, "erro")
                || StringUtils.containsIgnoreCase(retorno, "exception")
                || StringUtils.containsIgnoreCase(retorno, "falha")
                || StringUtils.containsIgnoreCase(retorno, "falhou")
                || !StringUtils.containsIgnoreCase(retorno, "sucesso")
                || !StringUtils.containsIgnoreCase(retorno, "operacao realizada com sucesso");
    }

    private void popularListaGruposChatMoviDesk() {
        this.gruposChatMoviDesk = new ArrayList<>();
        this.gruposChatMoviDesk.add(new SelectItem("52429C99928D447E8E6D43F0B1F08F02", "Carteira A"));
        this.gruposChatMoviDesk.add(new SelectItem("9D53461473A64BCFA6A0967105928C35", "Chat - Espanhol"));
        this.gruposChatMoviDesk.add(new SelectItem("DBD6947063E54273BD1FE733134C332F", "Chat - Grandes Contas"));
        this.gruposChatMoviDesk.add(new SelectItem("608F4D143C5A4E3B84A3F63407EA474D", "Chat - Redes"));
        this.gruposChatMoviDesk.add(new SelectItem("2BEA81B1B7E34362B5B54A852F7D7A81", "Time Z"));
    }


    public Date getDataAlteracaoPosPago() {
        return dataAlteracaoPosPago;
    }

    public void setDataAlteracaoPosPago(Date dataAlteracaoPosPago) {
        this.dataAlteracaoPosPago = dataAlteracaoPosPago;
    }

    public void selecionarUnidEmpresaConfigurarInicioPosPago(EmpresaJSON empresa) {
        try {
            setEmpresaUnidSelecionada(empresa);
            setDataAlteracaoPosPago(null);
        } catch (Exception e) {
            mensErro(e);
        }
    }

    public void ajustarEmpresaParaIniciarPosPago() {
        try {

            if (getDataAlteracaoPosPago() == null) {
                throw new Exception("Informe a data que foi realizado a alteração para Pós-Pago.");
            }

            String dataAlteracao= Uteis.getDataAplicandoFormatacao(getDataAlteracaoPosPago(), "dd/MM/yyyy HH:mm:ss");

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("empresa", getEmpresaUnidSelecionada().getCodigo());
            jsonObject.put("nomeUsuarioOAMD", getUsuario().getUserName());
            jsonObject.put("dataAlteracao", dataAlteracao);

            final String retorno = AdmWSConsumer.ajustarEmpresaParaIniciarPosPago(empresaSelecionada, jsonObject.toString());
            if (!retorno.toUpperCase().startsWith("SUCESSO")) {
                throw new Exception(retorno);
            }
            mensInfo("Operação realizada com sucesso.");
            doExecuteFunction("modalDataPosPago.hide();");
        } catch (Exception e) {
            mensErro("Erro ao realizar a operação: " + e.getMessage());
        }
    }

    public List<SelectItem> getModEmpresas() {
        return modEmpresas;
    }

    public void setModEmpresas(List<SelectItem> modEmpresas) {
        this.modEmpresas = modEmpresas;
    }

    public String getJustificativaBonusCredito() {
        if (justificativaBonusCredito == null) {
            justificativaBonusCredito = "";
        }
        return justificativaBonusCredito;
    }

    public void setJustificativaBonusCredito(String justificativaBonusCredito) {
        this.justificativaBonusCredito = justificativaBonusCredito;
    }

    public Integer getQtdBonusCredito() {
        if (qtdBonusCredito == null) {
            qtdBonusCredito = 0;
        }
        return qtdBonusCredito;
    }

    public void setQtdBonusCredito(Integer qtdBonusCredito) {
        this.qtdBonusCredito = qtdBonusCredito;
    }

    public Integer getQtdBonusAtual() {
        if (qtdBonusAtual == null) {
            qtdBonusAtual = 0;
        }
        return qtdBonusAtual;
    }

    public void setQtdBonusAtual(Integer qtdBonusAtual) {
        this.qtdBonusAtual = qtdBonusAtual;
    }

    public void abrirAdicionarBonusCreditoPacto(EmpresaJSON empresa) {
        try {
            setQtdBonusCredito(0);
            setQtdBonusAtual(0);
            setJustificativaBonusCredito("");

            selecionarUnidEmpresa(empresa);
            OAMDService os = UtilContext.getBean(OAMDService.class);
            setQtdBonusAtual(os.qtdAtualBonusCreditoPacto(empresaSelecionada, empresa.getCodigo()));
            doExecuteFunction("modalDialogCreditoPactoBonus.show();");
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public void gravarAdicionarBonusCreditoPacto() {
        try {
            if (getQtdBonusCredito() < 0) {
                throw new Exception("A quantidade de bônus não pode ser negativo.");
            }

            if (getJustificativaBonusCredito().trim().isEmpty()) {
                throw new Exception("Informe a justificativa.");
            }

            OAMDService os = UtilContext.getBean(OAMDService.class);
            String retorno = os.adicionarBonusCreditoPacto(empresaSelecionada, getEmpresaUnidSelecionada().getCodigo(),
                    getQtdBonusCredito(), getJustificativaBonusCredito(), getUsuario().getUserName());
            mensInfo(retorno + " Para a empresa " + getEmpresaUnidSelecionada().getNome());
            doExecuteFunction("modalDialogCreditoPactoBonus.hide();");
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public List<SelectItem> getSelectItemTabelaCreditoPacto() {
        List<SelectItem> lista = new ArrayList<>();
        for (CreditoPacto obj : getListaCreditoPacto()) {
            lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public CreditoPacto getCreditoPactoSelecionadoObj() {
        if (UteisValidacao.emptyNumber(getCreditoPactoSelecionado())) {
            return new CreditoPacto();
        }

        for (CreditoPacto creditoPacto : getListaCreditoPacto()) {
            if (creditoPacto.getCodigo().equals(getCreditoPactoSelecionado())) {
                return creditoPacto;
            }
        }
        return new CreditoPacto();
    }

    public void selecionouTipoCobrancaPacto() {
        if (empresaUnidSelecionadaConfigurar != null &&
                empresaUnidSelecionadaConfigurar.getTipocobrancapacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {
            empresaUnidSelecionadaConfigurar.setQtdparcelascobrancapacto(1);
            empresaUnidSelecionadaConfigurar.setQtddiasfechamentocobrancapacto(1);
            empresaUnidSelecionadaConfigurar.setDiavencimentocobrancapacto(0);
            empresaUnidSelecionadaConfigurar.setGerarnotafiscalcobrancapacto(true);
            empresaUnidSelecionadaConfigurar.setGerarcobrancaautomaticapacto(true);
        }
    }

    public Boolean getApresentarMenuInfras() {
        return !getUsuario().getPerfil().getNome().equals(PerfilPadraoEnum.COMERCIAL.getNomePerfil()) &&
                !getUsuario().getPerfil().getNome().equals(PerfilPadraoEnum.PERSONAL_FIT.getNomePerfil());
    }

    private String getConfig(ConfiguracaoEnum config) {
        try {
            return UtilContext.getBean(ConfiguracaoSistemaService.class).obterValorPorConfiguracao(config);
        } catch (ServiceException e) {
            e.printStackTrace();
        }
        return "";
    }

    public String getBotAppKey() {
        if (botAppKey == null)
            botAppKey = getConfig(ConfiguracaoEnum.BOT_APP_KEY);
        return botAppKey;
    }

    public void setBotAppKey(String botAppKey) {
        this.botAppKey = botAppKey;
    }

    public String getBotUrl() {
        if (botUrl == null)
            botUrl = getConfig(ConfiguracaoEnum.BOT_URL);
        return botUrl;
    }

    public void setBotUrl(String botUrl) {
        this.botUrl = botUrl;
    }

    public String getUrlValidateUser() {
        if (urlValidateUser == null)
            urlValidateUser = getConfig(ConfiguracaoEnum.URL_VALIDATE_USER);
        return urlValidateUser;
    }

    public void setUrlValidateUser(String urlValidateUser) {
        this.urlValidateUser = urlValidateUser;
    }

    public String getScriptBot() {
        try {
            return Uteis.convertStreamToString(
                    this.getClass().getResourceAsStream(SCRIPT_BOT))
                    .replace(BOT_URL, getBotUrl())
                    .replace(URL_VALIDATE_USER, getUrlValidateUser())
                    .replace(BOT_APP_KEY, getBotAppKey())
                    .replace(USER_NAME, getUsuario().getUserName())
                    .replace(USER_PWD, Uteis.obterTokenUsuario(getUsuario().getUserName(), getUsuario().getPassTrans()))
                    .replace(TOKEN1, Uteis.obterTokenUsuario(getUsuario().getUserName(), getUsuario().getPassTrans()))
                    .replace(TOKEN2, getUsuario().getTokenBot1())
                    .replace(USER_CITY, USER_CITY_GOIANIA)
                    .replace(USER_EMAIL, getUsuario().getEmail())
                    .replace(USER_PHONE, getUsuario().getTelefone())
                    ;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public void setDataEscolhida(Date dataEscolhida) {
        this.dataEscolhida = dataEscolhida;
    }

    public Date getDataEscolhida() {
        return dataEscolhida;
    }

    public void onDateSelect(SelectEvent event) {
        SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
        format.format(event.getObject());
    }


    public void diferencaDataPersonalizada(EmpresaJSON empresa){
        Date hoje = new Date();

        int diff = UteisValidacao.diferencaDeDias(getDataEscolhida(), hoje);

        if(diff < 0 ){
            FacesContext facesContext = FacesContext.getCurrentInstance();
            facesContext.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Data selecionada é anterior a data de hoje","Data selecionada é anterior a data de hoje"));
            setDataEscolhida(null);
            return;
        }
        bloquear(diff, empresa);
        setDataEscolhida(null);
    }

    public Integer getCodEmpresaFinanceiroCobranca() {
        if (codEmpresaFinanceiroCobranca == null) {
            codEmpresaFinanceiroCobranca = 0;
        }
        return codEmpresaFinanceiroCobranca;
    }

    public void setCodEmpresaFinanceiroCobranca(Integer codEmpresaFinanceiroCobranca) {
        this.codEmpresaFinanceiroCobranca = codEmpresaFinanceiroCobranca;
    }

    public Integer getEmpresaRecursoPadrao() {
        return empresaRecursoPadrao;
    }

    public void setEmpresaRecursoPadrao(Integer empresaRecursoPadrao) {
        this.empresaRecursoPadrao = empresaRecursoPadrao;
    }

    public List<String> getTiposInfoMigracaoPadrao() {
        if (tiposInfoMigracaoPadrao == null) {
            tiposInfoMigracaoPadrao = new ArrayList<>();
        }
        return tiposInfoMigracaoPadrao;
    }

    public void setTiposInfoMigracaoPadrao(List<String> tiposInfoMigracaoPadrao) {
        this.tiposInfoMigracaoPadrao = tiposInfoMigracaoPadrao;
    }

    public List<SelectItem> getSelectItemEmpresaRecursoPadrao() {
        List<SelectItem> lista = new ArrayList<>();
        if (this.empresaSelecionada != null) {
            for (EmpresaJSON empresaJSON : this.empresaSelecionada.getEmpresas()) {
                lista.add(new SelectItem(empresaJSON.getCodigo(), empresaJSON.getNome()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(null, ""));
        lista.add(1, new SelectItem(0, "TODAS"));
        return lista;
    }

    public List<SelectItem> getSelectItemTipoInfoMigracao() {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoInfoMigracaoEnum tipo : TipoInfoMigracaoEnum.values()) {
            if (tipo.isRecursoPadrao() &&
                    !tipo.equals(TipoInfoMigracaoEnum.LISTA_PESSOAS)) {
                lista.add(new SelectItem(tipo.name(), tipo.name()));
            }
        }
        return lista;
    }

    public void alterarEmpresaRecursoPadrao() {
        this.setTiposInfoMigracaoPadrao(new ArrayList<>());
        this.setTiposInfoMigracaoPadraoAnterior(new ArrayList<>());
        if (!UteisValidacao.emptyNumber(this.getEmpresaRecursoPadrao())) {
            for (EmpresaJSON empresaJSON : this.empresaSelecionada.getEmpresas()) {
                if (empresaJSON.getCodigo() == this.getEmpresaRecursoPadrao()) {
                    for (String tip : empresaJSON.getTiposinfomigracaopadrao().split(",")) {
                        if (!UteisValidacao.emptyString(tip)) {
                            this.getTiposInfoMigracaoPadrao().add(tip);
                            this.getTiposInfoMigracaoPadraoAnterior().add(tip);
                        }
                    }
                    break;
                }
            }
        } else if (this.getEmpresaRecursoPadrao() != null  &&
                this.getEmpresaRecursoPadrao() == 0) {
            //obter a config do sistema
            String tiposInfoMigracaoConfigSistema = obterRecursoPadraoTodos();
            for (String tip : tiposInfoMigracaoConfigSistema.split(",")) {
                if (!UteisValidacao.emptyString(tip)) {
                    this.getTiposInfoMigracaoPadrao().add(tip);
                    this.getTiposInfoMigracaoPadraoAnterior().add(tip);
                }
            }
        }
    }

    private String obterRecursoPadraoTodos() {
        try {
            OAMDService os = (OAMDService) UtilContext.getBean(OAMDService.class);
            JSONObject jsonResp = os.obterRecursoPadrao(empresaSelecionada, this.getEmpresaRecursoPadrao());
            return jsonResp.optString("content");
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public void alterarRecursoPadrao() {
        try {
            if (this.getEmpresaRecursoPadrao() == null) {
                throw new Exception("Selecione a empresa");
            }

            StringBuilder tiposAtivos = new StringBuilder();
            for (String tip : this.getTiposInfoMigracaoPadrao()) {
                tiposAtivos.append(",").append(tip);
            }

            OAMDService os = UtilContext.getBean(OAMDService.class);
            os.alterarRecursoPadrao(empresaSelecionada, this.getEmpresaRecursoPadrao(), tiposAtivos.toString().replaceFirst(",", ""), this.getUsuario(), null);

            DadosInfoMigracaoDTO dtoLog  = new DadosInfoMigracaoDTO();
            dtoLog.setDataRegistro(Calendario.hoje().getTime());
            dtoLog.setUsuarioOAMD(this.getUsuario().getCodigo());
            dtoLog.setUsuarioOAMDUsername(this.getUsuario().getUserName());
            dtoLog.setTiposInfo(tiposAtivos.toString().replaceFirst(",", ""));

            StringBuilder tiposAtivosAnterior = new StringBuilder();
            for (String tip : this.getTiposInfoMigracaoPadraoAnterior()) {
                tiposAtivosAnterior.append(",").append(tip);
            }
            dtoLog.setTiposInfoAnterior(tiposAtivosAnterior.toString().replaceFirst(",", ""));

            dtoLog.setFalha(new ArrayList<>());
            dtoLog.setSucesso(new ArrayList<>());
            dtoLog.setTipoLog("EMPRESA_INDIVIDUAL");

            DadosInfoMigracaoItemDTO itemDTO = new DadosInfoMigracaoItemDTO();
            itemDTO.setChave(empresaSelecionada.getChave());
            itemDTO.setEmpresa(UteisValidacao.emptyNumber(this.getEmpresaRecursoPadrao()) ? 0 : this.getEmpresaRecursoPadrao());
            dtoLog.getSucesso().add(itemDTO);
            dtoLog.setQtdFalha(dtoLog.getFalha().size());
            dtoLog.setQtdSucesso(dtoLog.getSucesso().size());

            InfoMigracaoLog log = new InfoMigracaoLog();
            log.setTipo(TipoInfoMigracaoLogEnum.RECURSO_EMPRESA_INDIVIDUAL.getId());
            log.setDados(new JSONObject(dtoLog).toString());
            getInfoMigracaoService().inserir(log);

            mensInfo("Recurso padrão alterado.");
            doExecuteFunction("modalDialogRecursoPadrao.hide();");
        } catch (Exception e) {
            mensErro(e.getMessage());
        }
    }

    public InfoMigracaoService getInfoMigracaoService() {
        return (InfoMigracaoService) UtilContext.getBean(InfoMigracaoService.class);
    }

    public String getFerramentaAtual() {
        return ferramentaAtual;
    }

    public void setFerramentaAtual(String ferramentaAtual) {
        this.ferramentaAtual = ferramentaAtual;
    }

}
