/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.ws;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.feed.FeedGestao;
import br.com.pacto.bean.feed.FeedGestaoHistorico;
import br.com.pacto.bean.feed.PaginaInicialFeed;
import br.com.pacto.bean.game.DadosGame;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.empresa.EmpresaOAMDJSON;
import br.com.pacto.integracao.IntegracaoCadastrosWSConsumer;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.process.oamd.ValidarEnviarCodAcesso;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.feed.FeedGestaoService;
import br.com.pacto.service.intf.game.DadosGameService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.server.Constants;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
@WebService(serviceName = "OamdWS")
public class OamdWS {

    /**
     * This is a sample web service operation
     */
    @WebMethod(operationName = "obterFeeds")
    public String obterFeeds() {
        try {
            FeedGestaoService feedService = (FeedGestaoService) UtilContext.getBean(FeedGestaoService.class);
            List<FeedGestao> obterTodos = feedService.obterVigentes();
            List<JSONObject> jsonL = new ArrayList<JSONObject>();
            for (FeedGestao fg : obterTodos) {
                jsonL.add(new JSONObject(fg.toJSON().toJSON()));
            }
            JSONArray jsonA = new JSONArray(jsonL);
            return jsonA.toString();
        } catch (Exception e) {
            return e.getMessage();
        }

    }

    @WebMethod(operationName = "obterPaginaInicial")
    public String obterPaginaInicial() {
        try {
            FeedGestaoService feedService = (FeedGestaoService) UtilContext.getBean(FeedGestaoService.class);
            PaginaInicialFeed paginaInicial = feedService.consultarPaginalInicial();
            List<JSONObject> jsonL = new ArrayList<JSONObject>();
            jsonL.add(new JSONObject(paginaInicial.toJSON().toJSON()));
            JSONArray jsonA = new JSONArray(jsonL);
            return jsonA.toString();
        } catch (Exception e) {
            return e.getMessage();
        }

    }

    @WebMethod(operationName = "gravarFeedBack")
    public void gravarFeedBack(
            @WebParam(name = "dados") String dados) {
        try {
            FeedGestaoService feedService = (FeedGestaoService) UtilContext.getBean(FeedGestaoService.class);
            String[] split = dados.split("\\|");
            for (String str : split) {
                if (str == null || str.isEmpty()) {
                    continue;
                }
                String[] historico = str.split(";");
                FeedGestaoHistorico feed = new FeedGestaoHistorico();
                feed.setNomeEmpresa(historico[0]);
                feed.setDataVisualizacao(Calendario.getDate("dd/MM/yyyy - HH:mm", historico[1]));
                feed.setLiked(historico[2].equals("t"));
                feed.setDisliked(historico[3].equals("t"));
                feed.setCodigoEmpresa(Integer.valueOf(historico[4]));
                feed.setFeed(feedService.obterPorId(Integer.valueOf(historico[5])));
                feed.setUsuario(Integer.valueOf(historico[6]));
                feedService.gravarHistorico(feed);
            }
        } catch (Exception e) {

        }


    }

    @WebMethod(operationName = "validarUserOamd")
    public String validarUserOamd(
            @WebParam(name = "username") String username, @WebParam(name = "senha") String senha) {
        try {
            UsuarioService us = (UsuarioService) UtilContext.getBean(UsuarioService.class);
            us.validarUsuario(username, senha, false);
            return "sucesso";
        } catch (Exception e) {
            return e.getMessage();
        }

    }

    @WebMethod(operationName = "gravarDadosGame")
    public String gravarDadosGame(
            @WebParam(name = "agrupador") String agrupador, @WebParam(name = "dados") String dados) {
        try {
            DadosGameService dgs = (DadosGameService) UtilContext.getBean(DadosGameService.class);
            DadosGame dadosGame = new DadosGame();
            dadosGame.setAgrupador(agrupador);
            dadosGame.setDados(dados);
            return dgs.gravarDadosGame(dadosGame);
        } catch (Exception e) {
            return e.getMessage();
        }

    }

    @WebMethod(operationName = "obterDadosGame")
    public String obterDadosGame() {
        try {
            DadosGameService dgs = (DadosGameService) UtilContext.getBean(DadosGameService.class);
            return dgs.obterDadosGame().toString();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    @WebMethod(operationName = "logar")
    public String logar(@WebParam(name = "username") final String username,
                        @WebParam(name = "senha") final String senha,
                        @WebParam(name = "somenteUsername") Boolean somenteUsername) {
        try {
            UsuarioService us = (UsuarioService) UtilContext.getBean(UsuarioService.class);
            Usuario usuario = us.validarUsuario(username, senha, somenteUsername);
            return usuario.toJSON();
        } catch (Exception e) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("erro", e.getMessage());
            } catch (JSONException ex) {
                Logger.getLogger(OamdWS.class.getName()).log(Level.SEVERE, null, ex);
            }
            return jsonObject.toString();
        }
    }

    @WebMethod(operationName = "empresas")
    public String empresas() {
        try {
            List<Empresa> empresas = getEmpresaService().obterTodos(false);
            List<JSONObject> jsonL = new ArrayList<JSONObject>();
            for (Empresa empresa : empresas) {
                jsonL.add(new JSONObject(new EmpresaOAMDJSON(empresa).toJSON()));
            }
            return new JSONArray(jsonL).toString();
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }


    @WebMethod(operationName = "sincronizarComentario")
    public String sincronizarComentario(@WebParam(name = "key") final String key,
                                        @WebParam(name = "codigoUsuario") final Integer codigoUsuario,
                                        @WebParam(name = "mensagem") final String mensagem,
                                        @WebParam(name = "titulo") final String titulo,
                                        @WebParam(name = "codigoExterno") final Integer codigoExterno,
                                        @WebParam(name = "solicitacao_id") final Integer solicitacao_id) {
        JSONObject objectReturn = new JSONObject();
        String retorno = "";
        try {
            Empresa empresa = getEmpresaService().obterPorId(key);
            if (empresa != null) {
                retorno = IntegracaoCadastrosWSConsumer.enviarMensagemAoUsuario(empresa.getRoboControleSemHTTPS(), key, codigoUsuario, mensagem, titulo, codigoExterno, solicitacao_id);
                Integer codigoRetorno = Integer.parseInt(retorno);

                objectReturn.put(SuperControle.STATUS_SUCESSO, codigoRetorno);
            } else {
                objectReturn.put(SuperControle.STATUS_ERRO, "Chave " + key + " não encontrada");
            }
        } catch (Exception e) {
            try {
                objectReturn.put(SuperControle.STATUS_ERRO, retorno);
            } catch (Exception ex) {
                return "ERRO: " + ex.getMessage();
            }
        }
        return objectReturn.toString();
    }

    @WebMethod(operationName = "sincronizarComentarioUsername")
    public String sincronizarComentarioUsername(@WebParam(name = "key") final String key,
                                        @WebParam(name = "username") final String username,
                                        @WebParam(name = "mensagem") final String mensagem,
                                        @WebParam(name = "titulo") final String titulo,
                                        @WebParam(name = "codigoExterno") final Integer codigoExterno) {
        JSONObject objectReturn = new JSONObject();
        String retorno = "";
        try {
            Empresa empresa = getEmpresaService().obterPorId(key);
            if (empresa != null) {


                retorno = IntegracaoCadastrosWSConsumer.enviarMensagemAoUsuarioUsername(empresa.getRoboControleSemHTTPS(), key, username, mensagem, titulo, codigoExterno);
                Integer codigoRetorno = Integer.parseInt(retorno);

                objectReturn.put(SuperControle.STATUS_SUCESSO, codigoRetorno);
            } else {
                objectReturn.put(SuperControle.STATUS_ERRO, "Chave " + key + " não encontrada");
            }
        } catch (Exception e) {
            try {
                objectReturn.put(SuperControle.STATUS_ERRO, retorno);
            } catch (Exception ex) {
                return "ERRO: " + ex.getMessage();
            }
        }
        return objectReturn.toString();
    }

    @WebMethod(operationName = "enviarNotificacaoUsuario")
    public String enviarNotificacaoUsuario(@WebParam(name = "key") final String key,
                                           @WebParam(name = "codUsuario") Integer codUsuario,
                                           @WebParam(name = "username") String username,
                                           @WebParam(name = "mensagem") String mensagem,
                                           @WebParam(name = "link") String link,
                                           @WebParam(name = "tipoNotificacao") Integer tipoNotificacao) {

        JSONObject objectReturn = new JSONObject();
        String retorno = "";
        try {
            Empresa empresa = getEmpresaService().obterPorId(key);
            if (empresa != null) {
                retorno = IntegracaoCadastrosWSConsumer.enviarNotificacaoUsuario(
                        empresa.getRoboControleSemHTTPS(), key, codUsuario,
                        username, mensagem, link, tipoNotificacao);
                objectReturn.put(SuperControle.STATUS_SUCESSO, retorno);
            } else {
                objectReturn.put(SuperControle.STATUS_ERRO, "Chave " + key + " não encontrada");
            }

        } catch (Exception e) {
            try {
                objectReturn.put(SuperControle.STATUS_ERRO, retorno);
            } catch (Exception ex) {
                return "ERRO: " + ex.getMessage();
            }
        }
        return objectReturn.toString();
    }

    @WebMethod(operationName = "todasEmpresas")
    public String todasEmpresas() {
        try {
            if(Aplicacao.empresas == null){
                Aplicacao.empresas = getEmpresaService().obterTodosComInativasJSON(false);
            }
            List<JSONObject> jsonL = new ArrayList<JSONObject>();
            for (EmpresaOAMDJSON empresa : Aplicacao.empresas) {
                jsonL.add(new JSONObject(empresa.toJSON()));
            }
            return new JSONArray(jsonL).toString();
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    private EmpresaService getEmpresaService() {
        return (EmpresaService) UtilContext.getBean(EmpresaService.class);
    }

    @WebMethod(operationName = "validarEnviarCodAcesso")
    public String validarEnviarCodAcesso(@WebParam(name = "key") String key,
                                       @WebParam(name = "codigoAcesso") String codigoAcesso,
                                       @WebParam(name = "chaves") String chaves) {

        try {
            final Usuario u = UtilContext.getBean(UsuarioService.class).
                    validarUsuario(Constants.LOGIN_API_OAMD, Constants.SENHA_API_OAMD, false);
            final String token = Uteis.obterTokenUsuario(u.getUserName(), u.getPassTrans());
            Boolean enviarCodAcesso = ValidarEnviarCodAcesso.validarEnviarCodAcesso(
                    getEmpresaService(), key, chaves, codigoAcesso, token);
            return enviarCodAcesso.toString();
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "gravarIdentificadorAcesso")
    public String gravarIdentificadorAcesso(@WebParam(name = "chave") String chave,
                                       @WebParam(name = "identificador") String identificador) {
        try {
            getEmpresaService().gravarIdentificadorAcesso(chave, identificador);
            return "sucesso";
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    @WebMethod(operationName = "buscarIdentificadorAcesso")
    public String buscarIdentificadorAcesso(@WebParam(name = "chave") String chave) {
        try {
            return getEmpresaService().buscarIdentificadorAcesso(chave);
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

}
