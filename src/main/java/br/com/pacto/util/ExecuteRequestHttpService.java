/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.server.Constants;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import javax.net.ssl.*;
import javax.security.cert.X509Certificate;

/**
 *
 * <AUTHOR>
 */
public class ExecuteRequestHttpService {

    public int timeout = 0;
    public int readTimeout = 0;
    public int connectTimeout = 0;

    public static final String METODO_POST = "POST";
    public static final String METODO_GET = "GET";
    public static final String METODO_PUT = "PUT";
    public static final String METODO_DELETE = "DELETE";

    static {
        TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

            }

            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

            }

            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }

            public void checkClientTrusted(X509Certificate[] certs, String authType) {
            }

            public void checkServerTrusted(X509Certificate[] certs, String authType) {
            }
        }};

        SSLContext sc = null;
        try {
            sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String executeRequest(String urlRequest, Map<String, String> params) throws IOException {
        return executeRequest(urlRequest, params, "");
    }

    public static String executeRequest(String urlRequest, Map<String, String> params, String charsertResposta) throws IOException {
        return executeRequest(urlRequest, params, charsertResposta, "iso-8859-1");
    }

    public synchronized static String executeRequest(String urlRequest, Map<String, String> params, String charsertResposta, String encode) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                            + paramName + "="
                            + URLEncoder.encode(valor, encode);
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd;
        if(charsertResposta != null && charsertResposta != "") {
            rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), Charset.forName(charsertResposta)));
        } else {
            rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        }
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha            
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        return resposta;
    }

    public static String executeRequestGET(String urlRequest) throws IOException {

        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        rd.close();
        return resposta;
    }

    public static void main(String[] args) {
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {

                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(
                            java.security.cert.X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(
                            java.security.cert.X509Certificate[] certs, String authType) {
                    }
                }};

            try {

                SSLContext sc = SSLContext.getInstance("SSL");
                sc.init(null, trustAllCerts, new java.security.SecureRandom());
                HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.setProperty("https.proxySet", "true");
            System.setProperty("https.proxyHost", "xxx.xxx.xxx.xxx");
            System.setProperty("https.proxyPort", "80");
            URL url = new URL("https://www.google.com");
            @SuppressWarnings("deprecation")
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoInput(true);
            connection.setDoOutput(true);

            connection.setRequestMethod("GET");
            HttpsURLConnection.setFollowRedirects(true);

            String query = "serviceId="
                    + URLEncoder.encode("7");

            connection.setRequestProperty("Content-length",
                    String.valueOf(query.length()));
            connection.setRequestProperty("Content-Type",
                    "application/x-www- form-urlencoded");
            connection.setRequestProperty("User-Agent",
                    "Mozilla/4.0 (compatible; MSIE 5.0; Windows 98; DigExt)");

            DataOutputStream output = new DataOutputStream(
                    connection.getOutputStream());

            output.writeBytes(query);

            System.out.println("Resp Code:" + connection.getResponseCode());
            System.out.println("Resp Message:"
                    + connection.getResponseMessage());

            DataInputStream input = new DataInputStream(
                    connection.getInputStream());

            for (int c = input.read(); c != -1; c = input.read()) {
                System.out.print((char) c);
            }
            input.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public String executeRequestInner(String urlRequest, Map<String, String> params) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
//            Uteis.logar(null, params.toString());
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                            + paramName + "="
                            + URLEncoder.encode(valor, "iso-8859-1");
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        if (timeout != 0) {
            conn.setReadTimeout(timeout);
            conn.setConnectTimeout(timeout);
        }
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
//        Uteis.logar(null, resposta);
        return resposta;
    }

    public String executeRequestInner(String urlRequest, Map<String, String> params, String encode) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
//            Uteis.logar(null, params.toString());
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + URLEncoder.encode(valor, encode);
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        if (connectTimeout != 0) {
            conn.setConnectTimeout(connectTimeout);
        }
        if (readTimeout != 0){
            conn.setReadTimeout(readTimeout);
        }
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), encode));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
//        Uteis.logar(null, resposta);
        return resposta;
    }

    public static String executeHttpRequest(String urlRequest, String corpo, Map<String, String> headers, String metodo, String encode) throws IOException{
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : headers.keySet()){
            conn.setRequestProperty(keyHeader, headers.get(keyHeader));
        }
        conn.setRequestMethod(metodo);
        if(corpo != null){
            conn.setDoOutput(true);
            OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
            wr.write(corpo);
            wr.flush();
            wr.close();
        }
        // Pega a Resposta
        InputStream in = null;
        try{
            in = conn.getInputStream();
        }catch (IOException  e){
            if(conn.getResponseCode() != 200){
                in = conn.getErrorStream();
            }
        }
        BufferedReader rd = new BufferedReader(new InputStreamReader(in, Charset.forName(encode)));
        String line;
        StringBuilder resposta = new StringBuilder();
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta.append(line);
        }
        rd.close();
        return resposta.toString();
    }

    public String executeRequestGET(final String urlRequest, final Map<String, String> paramsHeader) throws IOException {
        System.out.println(urlRequest);
        System.out.println(paramsHeader);
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        if (paramsHeader != null){
            Set<String> setHeader = paramsHeader.keySet();
            for (String k : setHeader){
                conn.setRequestProperty(k, paramsHeader.get(k));
            }
        }
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        rd.close();
        return resposta;
    }


    public static String executeHttpRequestGenerico(String urlRequest, Map<String, String> paramsHeader, Map<String, String> paramsCorpo, String metodo, String encode, String encodeResponse) throws IOException {
        if (encodeResponse == null) {
            encodeResponse = encode;
        }

        String parametrosCodificados = "";
        if (paramsCorpo != null) {
            Set<String> s = paramsCorpo.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = paramsCorpo.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + URLEncoder.encode(valor, encode);
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest +"?" + parametrosCodificados);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : paramsHeader.keySet()){
            conn.setRequestProperty(keyHeader, paramsHeader.get(keyHeader));
        }

        conn.setRequestMethod(metodo);
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
//        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), encodeResponse));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        return resposta;
    }

    public static String executeHttpPost(String urlBase, Map<String, String> headers, Map<String, String> params, String body, String charset) throws IOException {

        HttpPost httpPost = new HttpPost(obterUrlComParams(urlBase, params, charset));
        addHeaders(httpPost, headers);
        addBody(httpPost, body, charset);

        HttpClient client = createConnector();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        return EntityUtils.toString(response.getEntity());
    }

    private static void addBody(HttpPost httpPost, String body, String charset) {
        if (!UteisValidacao.emptyString(body)) {
            StringEntity entity = new StringEntity(body, charset);
            httpPost.setEntity(entity);
        }
    }

    private static void addHeaders(HttpPost httpPost, Map<String, String> headers) {
        for(Map.Entry<String, String> entry : headers.entrySet()){
            httpPost.setHeader(entry.getKey(), entry.getValue());
        }
    }

    private static String obterUrlComParams(String urlBase, Map<String, String> params, String charset) throws UnsupportedEncodingException {
        if (params == null || params.isEmpty()) {
            return urlBase;
        }

        StringBuilder result = new StringBuilder();
        boolean first = true;
        for(Map.Entry<String, String> entry : params.entrySet()){
            if (first) {
                first = false;
            } else {
                result.append("&");
            }
            result.append(URLEncoder.encode(entry.getKey(), charset));
            result.append("=");
            result.append(URLEncoder.encode(entry.getValue(), charset));
        }
        return urlBase + "?" + result.toString();
    }

    public static CloseableHttpClient createConnector() {
        return createConnector(null);
    }

    public static CloseableHttpClient createConnector(RequestConfig requestConfig) {

        // Construir o contexto SSL que ignora a validação de certificado
        SSLContext sslContext = null;
        try {
            sslContext = SSLContextBuilder
                    .create()
                    .loadTrustMaterial((chain, authType) -> true) // Aceita qualquer certificado
                    .build();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        } catch (KeyStoreException e) {
            throw new RuntimeException(e);
        }

        return HttpClients.custom()
                .setSSLContext(sslContext)
                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE) // Ignora a verificação do nome do host
                .setDefaultRequestConfig(requestConfig != null ? requestConfig : RequestConfig.DEFAULT)
                .build();

    }


}

