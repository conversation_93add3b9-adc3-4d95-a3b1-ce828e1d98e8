/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.Collator;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 *
 * <AUTHOR> 3
 */
public class Ordenacao implements Comparator  {

    private String campo;

    private Boolean ordenarStringComoNumero = false;

    private static ThreadLocal<Ordenacao> instance;

    private synchronized static Ordenacao getInstance() {
        if (instance == null) {
            instance = new ThreadLocal<Ordenacao>();
            instance.set(new Ordenacao());
        }
        if (instance.get() == null) {
            instance.set(new Ordenacao());
        }
        return instance.get();
    }

    /**
     * Construtor privado somente para prevenir tentativas de inst�ncia direta da classe
     * 
     */
    private Ordenacao() {
        super();
    }

    public int compare(Object primeiro, Object segundo) {
        Object o1 = null, o2 = null;
        Method getMethod = null;
        final Collator collator = Collator.getInstance(Locale.getDefault());
        try {
            if (!(primeiro == null || segundo == null)) {
                if (primeiro instanceof String) {
                    String primeiroString = (String) primeiro;
                    String segundoString = (String) segundo;
                    if(getInstance().ordenarStringComoNumero){
                        primeiroString = Uteis.adicionarValorEsquerda("0", primeiroString, 10);
                        segundoString = Uteis.adicionarValorEsquerda("0", segundoString, 10);
                    }
                    return collator.compare(primeiroString, segundoString);
                }
                getMethod = primeiro.getClass().getDeclaredMethod("get".concat(campo.substring(0, 1).toUpperCase()).concat(campo.substring(1)), null);
                o1 = getMethod.invoke(primeiro, null);
                o2 = getMethod.invoke(segundo, null);

                if (o1 != null && o2 != null && o1 instanceof Integer || o1 instanceof Float || o1 instanceof Double || o1 instanceof Byte || o1 instanceof Long) {

                    Double numero = new Double(String.valueOf(o1));
                    return numero.compareTo(new Double(String.valueOf(o2)));
                }

                if (o1 != null && o2 != null && o1 instanceof Date) {
                    Calendar cal1 = Calendario.getInstance();
                    cal1.setTime((Date) o1);
                    Calendar cal2 = Calendario.getInstance();
                    cal2.setTime((Date) o2);

                    return cal1.compareTo(cal2);
                }

                if (o1 != null && o2 != null) {
                    String o1String = String.valueOf(o1);
                    String o2String = String.valueOf(o2);
                    if(getInstance().ordenarStringComoNumero){
                        o1String = Uteis.adicionarValorEsquerda("0", o1String, 10);
                        o2String = Uteis.adicionarValorEsquerda("0", o2String, 10);
                    }
                    return collator.compare(o1String , o2String);
                }
            }
        } catch (NoSuchMethodException metodo) {
            throw new RuntimeException(metodo);
        } catch (InvocationTargetException invoke) {
            throw new RuntimeException(invoke);
        } catch (IllegalAccessException access) {
            throw new RuntimeException(access);
        }
	return -1;
    }

    /**
     * M�todo respons�vel por retorna uma lista de Object ordenada pelo nome do campo informado
     * 
     * @param lista
     * @param campo
     * @return
     */
    public static List ordenarLista(final List lista, final String campo) {
        if ((lista != null) &&
            (!lista.isEmpty()) &&
            (campo != null) &&
            (!(campo.trim().length() == 0))) {
            getInstance().campo = (campo);
            Collections.sort(lista, getInstance());
        }
        return lista;
    }

    /**
     * M�todo respons�vel por retorna uma lista de Object ordenada pelo nome do campo informado
     * Caso <code>stringComoNumero</code> seja true, ele contatenar� zeros a esquerda antes de realizar a ordenacao.
     * @param lista
     * @param campo
     * @return
     */
    public static List ordenarLista(final List lista, final String campo, final Boolean stringComoNumero){
        getInstance().ordenarStringComoNumero = stringComoNumero;
        ordenarLista(lista, campo);
        getInstance().ordenarStringComoNumero = false;
        return lista;
    }

    public static List ordenarListaReverse (final List lista, final String campo) {
        if ((lista != null) &&
                (!lista.isEmpty()) &&
                (campo != null) &&
                (!(campo.trim().length() == 0))) {
            getInstance().campo = (campo);
            Collections.sort(lista, getInstance());
            Collections.reverse(lista);
        }
        return lista;
    }


    
}