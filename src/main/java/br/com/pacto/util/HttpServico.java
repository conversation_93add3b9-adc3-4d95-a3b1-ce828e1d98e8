package br.com.pacto.util;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;


@Component
public class HttpServico {

    private static final Logger LOG = LoggerFactory.getLogger(HttpServico.class);

    private static final String ACCEPT_HEADER = "Accept";

    private final RestTemplate restTemplate;

    public HttpServico() {
        this.restTemplate = new RestTemplate();
    }

    private String autorizacao(String token){
        return token.contains("Bearer") ? token : ("Bearer " + token);
    }

    public JSONObject postJson(String url, String corpo, String token) throws Exception{
        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url);
        StringEntity entity = new StringEntity(corpo);
        httpPost.setEntity(entity);
        if(token != null){
            httpPost.setHeader("Authorization", autorizacao(token));
        }
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Content-type", "application/json");
        CloseableHttpResponse response = client.execute(httpPost);
        String responseJSON = EntityUtils.toString(response.getEntity());
        return new JSONObject(responseJSON);
    }

    public JSONObject getJson(String url, String token) throws Exception{
        return new JSONObject(getString(url, token));
    }

    public String getString(String url, String token) throws Exception{
        try (CloseableHttpClient client = HttpClientBuilder.create().build()) {
            HttpGet httpGet = new HttpGet(url);
            if (token != null) {
                httpGet.setHeader("Authorization", autorizacao(token));
            }
            httpGet.setHeader("Accept", "application/json");
            httpGet.setHeader("Content-type", "application/json");
            try (CloseableHttpResponse response = client.execute(httpGet)) {
                return EntityUtils.toString(response.getEntity());
            }
        }
    }

    public ResponseEntity<String> doJson(String url, String corpo, HttpMethod httpMethod, String token) {
        HttpHeaders headers = new HttpHeaders();
        if(token != null){
            headers.set("Authorization", autorizacao(token));
        }
        headers.set("Content-Type", "application/json");
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        HttpEntity<?> entidade = new HttpEntity<>(corpo, headers);
        String uri = builder.toString();
        return restTemplate.exchange(
                uri,
                httpMethod,
                entidade,
                String.class);
    }

}
