/**
 * 
 */
package br.com.pacto.util;

import br.com.pacto.objeto.Uteis;
import org.apache.commons.io.FileUtils;

import java.io.*;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe de manipula��o de arquivos
 * 
 * <AUTHOR>
 */
public final class FileUtilities {

    private FileUtilities() {
    }

    public static byte[] obterBytesArquivo(final File arquivo) throws IOException {
        byte[] bytesArquivo;
        // Obter o array de bytes que define o arquivo
        final ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        // tamanho do buffer de 4 KB
        final byte buffer[] = new byte[4096];
        int bytesRead;
        final FileInputStream fiSream = new FileInputStream(arquivo.getAbsolutePath());
        // l� o tamanho do buffer enquanto tiver mais bytes
        bytesRead = fiSream.read(buffer);
        while (bytesRead != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
            bytesRead = fiSream.read(buffer);
        }
        bytesArquivo = arrayOutputStream.toByteArray();
        fiSream.close();

        return bytesArquivo;
    }

    public static byte[] obterBytesInputStream(final InputStream input) throws IOException {
        byte[] bytesArquivo;
        // Obter o array de bytes que define o arquivo
        final ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        // tamanho do buffer de 4 KB
        final byte buffer[] = new byte[4096];
        int bytesRead;
        // l� o tamanho do buffer enquanto tiver mais bytes
        bytesRead = input.read(buffer);
        while (bytesRead != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
            bytesRead = input.read(buffer);
        }
        bytesArquivo = arrayOutputStream.toByteArray();

        return bytesArquivo;
    }

    public static List<Map<String, File>> readListFilesDirectory(final String folder)
            throws FileNotFoundException, IOException {
        File f = new File(folder);
        List<Map<String, File>> conteudoArquivos = new ArrayList();
        for (final File fileEntry : f.listFiles()) {

            Map<String, File> mapa = new HashMap();
            mapa.put(fileEntry.getAbsolutePath(), fileEntry);
            conteudoArquivos.add(mapa);
        }
        return conteudoArquivos;
    }

    public static StringBuilder readContentFile(final String fileName)
            throws FileNotFoundException, IOException {
        FileInputStream fis = new FileInputStream(fileName);
        return new StringBuilder(Uteis.convertStreamToStringBuffer(fis));
    }

    public static void forceDirectory(final String pathDestino) {
        File fDestino = new File(pathDestino);
        if (!fDestino.exists()) {
            try {
                FileUtils.forceMkdir(fDestino);
            } catch (Exception e) {
                Logger.getLogger(FileUtilities.class.getName()).log(Level.SEVERE, e.getMessage(), e);
            }
        }
    }

    public static Properties getPropsFromFile(final String path) {
        Properties props = new Properties();
        InputStream in = FileUtilities.class.getResourceAsStream(path);
        try {
            props.load(in);
            return props;
        } catch (IOException ex) {
            Logger.getLogger(FileUtilities.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            try {
                in.close();
            } catch (IOException ex) {
                Logger.getLogger(FileUtilities.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return null;
    }

    public static File saveToFile(byte[] bytes, String fileName) throws IOException {
        File f = new File(fileName);
        FileUtils.writeByteArrayToFile(f, bytes);
        return f;
    }
}
