package br.com.pacto.util;

import javax.faces.context.FacesContext;

@SuppressWarnings({"deprecation", "unchecked"})
public final class JSFUtilities {

    private static final String RSOURCE_NOT_FOUND = "Missing resource: ";
    public static final String KEY = "key";
    public static final String LOGGED = "logado";
    public static final String CON = "con";
    public static final String TZ = "timeZoneID";
    public static final String MODULOS = "modulosHabilitados";
    public static final String DICAS_ESCONDER = "dicasEsconder";
    public static final String COOKIE_CREDENTIALS_FAILOVER = "faILover";

    private JSFUtilities() {
    }

    private abstract static class InnerFacesContext extends FacesContext {

        protected static void setFacesContextAsCurrentInstance(FacesContext facesContext) {
            FacesContext.setCurrentInstance(facesContext);
        }
    }

    public static Object getManagedBean(final String instanceName) {
        return FacesContext.getCurrentInstance().getApplication().createValueBinding("#{" + instanceName + "}").getValue(
                FacesContext.getCurrentInstance());
    }

   
}
