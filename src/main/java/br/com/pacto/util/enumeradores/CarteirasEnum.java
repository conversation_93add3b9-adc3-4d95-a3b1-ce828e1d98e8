/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum CarteirasEnum {
    
 // Enumeradores
    CENTRAL(0, "Central","CCEN"),
    IMPLANTACAO(1, "Implantação","CIMP"),
    ADM(2, "Comercial","CCOM"),
    PMG(3, "PMG","CPMG"),
    SP(4, "SP","CSP"),
    RJ(5, "RJ","CRJ"),
    RS(6, "RS","CRS"),
    MG(7, "MG","CMG"),
    BLUEFIT_REDE(8, "BLUEFIT_REDE","BFR"),
    BLUEFIT_GO(9, "BLUEFIT_GO","BFG"),
    BLUEFIT_SP(10, "BLUEFIT_SP","BFS");
    // Atributos
    
    private Integer id;
    private String descricao;
    private String codigo;

    // Métodos da Classe
    /**
     * Método que seta o código e descrição
     *
     * @param codigo
     * @param descricao
     */
    private CarteirasEnum(final Integer id, final String descricao,final String codigo) {
        this.setId(id);
        this.setDescricao(descricao);
        this.setCodigo(codigo);
    }

    public static CarteirasEnum getFromID(int id){
        for(CarteirasEnum carteira : values()){
            if(carteira.getId() == id){
                return carteira;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    

}
