package br.com.pacto.util.enumeradores;

/**
 * Created with IntelliJ IDEA.
 * User: joa<PERSON><PERSON>
 * Date: 07/03/14
 * Time: 17:23
 */

public enum MidiasNiveisEnum {

    PEQUENA_QUADRADA(0,"/Small/Square/",40,40),
    PEQUENA_RETANGULO(1,"/Small/Square/",114,40),
    MOBILE_QUADRADA(2,"/Mobile/Square/",56,56),
    MOBILE_RETANGULO(3,"/Mobile/Square/",320,112),
    MEDIA_PEQUENA_QUADRADA(4,"/MediumSmall/Square/",128,128),
    MEDIA_PEQUENA_RETANGULO(5,"/MediumSmall/Square/",256,90),
    MEDIA_QUADRADA(6,"/Medium/Square/",512,512),
    MEDIA_RETANGULO(7,"/Medium/Square/",640,224),
    GRANDE_QUADRADA(8,"/Large/Square/",1024,1024),
    <PERSON><PERSON><PERSON><PERSON>_RETANGULO(9,"/Large/Square/",960,336),
    ORIGINAL(10,"/Original/",-1,-1);

    private Integer id, width, height;
    private String local;

    /**
     * Construtor
     * @param id Id.
     * @param local Endereço completo até o arquivo, incluindo / ...url... /.
     * @param width Largura da Imagem.
     * @param height Altura da Imagem.
     */

    private MidiasNiveisEnum(int id, String local,int width,int height) {
        this.id = id;
        this.local = local;
        this.width = width;
        this.height = height;
    }

    public Integer getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    /**
     * Largura
     * @return width Retorna a largura da Imagem. Retorna -1 em caso de largura variavel.
     */
    public Integer getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    /**
     * Altura
     * @return height Retorna a altura da Imagem. Retorna -1 em caso de altura variavel.
     */
    public Integer getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    /**
     * Local
     * @return local Retorna o local padrão COM as barras iniciais e finais "/...valor.../".
     */
    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public static MidiasNiveisEnum getFromId(Integer id){
        for(MidiasNiveisEnum tipo : values()){
            if(tipo.getId().equals(id)){
                return tipo;
            }
        }
        return null;
    }
}
