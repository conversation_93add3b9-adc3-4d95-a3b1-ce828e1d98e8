package br.com.pacto.util.enumeradores;

public enum TipoInfoMigracaoLogEnum {

    RECURSO_EMPRESA(0, "Empresa recurso"),
    RECURSO_EMPRESA_PADRAO(1, "Empresa recurso alterar recursos"),
    RECURSO_EMPRESA_ADICIONAR(2, "Empresa recurso adicionar recurso"),
    RECURSO_EMPRESA_REMOVER(3, "Empresa recurso remover recurso"),
    RECURSO_EMPRESA_USUARIO_AUTOMATICO(4, "Empresa recurso usuario automatico"),
    RECURSO_EMPRESA_INDIVIDUAL(5, "Empresa recurso individual");

    Integer id;
    String descricao;

    TipoInfoMigracaoLogEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoInfoMigracaoLogEnum obter(String name) {
        for (TipoInfoMigracaoLogEnum tipo : values()) {
            if (tipo.name().equalsIgnoreCase(name)) {
                return tipo;
            }
        }
        return null;
    }

    public static TipoInfoMigracaoLogEnum obterPorId(Integer id) {
        for (TipoInfoMigracaoLogEnum tipo : values()) {
            if (tipo.getId().equals(id)) {
                return tipo;
            }
        }
        return null;
    }
}
