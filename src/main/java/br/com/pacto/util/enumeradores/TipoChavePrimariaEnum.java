/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum TipoChavePrimariaEnum {

    CLIENTE(0, "Cliente", "CLI"),
    COLABORADOR(1, "Colaborador", "COL"),
    USUARIO(2, "Usuário", "USU");

    private Integer id;
    private String descricao;
    private String sigla;

    private TipoChavePrimariaEnum(final Integer id, final String descricao, final String sigla) {
        this.setId(id);
        this.setDescricao(descricao);
        this.setSigla(sigla);
    }

    public static TipoChavePrimariaEnum getFromID(int id){
        for(TipoChavePrimariaEnum carteira : values()){
            if(carteira.getId() == id){
                return carteira;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
}