package br.com.pacto.util.enumeradores;

public enum TipoInfoMigracaoEnum {

//    IMPORTANTE
//    Esse mesmo ENUM existe nos seguintes projetos | ZW - Legado | ADM-CORE-MS | OAMD | ao alterar aqui deve ser alterado nos demais projetos

    NEGOCIACAO(1, true),
    CONTRATO_LANCADO(2, false),
    LISTA_PESSOAS(3, true),
    TELA_ALUNO(4, true),
    VENDA_AVULSA(5, true),
    CONFIGURACOES(6, true),
    CAIXA_ABERTO(7, true),
    INCLUIR_CLIENTE(8, true);

    Integer id;
    boolean recursoPadrao;

    TipoInfoMigracaoEnum(Integer id, boolean recursoPadrao) {
        this.id = id;
        this.recursoPadrao = recursoPadrao;
    }

    public Integer getId() {
        return id;
    }

    public boolean isRecursoPadrao() {
        return recursoPadrao;
    }

    public static String obterCodigos(Boolean recursoPadrao) {
        StringBuilder ret = new StringBuilder();
        for (TipoInfoMigracaoEnum tipo : values()) {
            if (recursoPadrao == null ||
                    tipo.isRecursoPadrao() == recursoPadrao) {
                ret.append(",").append(tipo.getId());
            }
        }
        return ret.toString().replaceFirst(",", "");
    }

    public static TipoInfoMigracaoEnum obter(String name) {
        for (TipoInfoMigracaoEnum tipo : values()) {
            if (tipo.name().equalsIgnoreCase(name)) {
                return tipo;
            }
        }
        return null;
    }

    public static TipoInfoMigracaoEnum obterPorId(Integer id) {
        for (TipoInfoMigracaoEnum tipo : values()) {
            if (tipo.getId().equals(id)) {
                return tipo;
            }
        }
        return null;
    }
}
