/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.enumeradores;

/**
 * <AUTHOR>
 */
public enum CarteiraRelacionamentoEnum {

    CARTEIRA_A(36, "Carteira A"),
    CARTEIRA_B(37, "Carteira B"),
    CARTEIRA_C(38, "Carteira C"),
    CARTEIRA_D(26, "Carteira D"),
    CARTEIRA_Z(42, "Carteira Z"),
    CARTEIRA_Q40(30, "Consolidação"),
    TODAS(0, "Todas as carteiras");

    private Integer id;
    private String descricao;

    CarteiraRelacionamentoEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static CarteiraRelacionamentoEnum obterPor(String descricao) {
        for (CarteiraRelacionamentoEnum carteiraRelacionamentoEnum : CarteiraRelacionamentoEnum.values()) {
            if (descricao.equals(carteiraRelacionamentoEnum.getDescricao())) {
                return carteiraRelacionamentoEnum;
            }
        }
        return TODAS;
    }

    public static CarteiraRelacionamentoEnum getFromID(int id) {
        for (CarteiraRelacionamentoEnum carteira : values()) {
            if (carteira.getId() == id) {
                return carteira;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }


}