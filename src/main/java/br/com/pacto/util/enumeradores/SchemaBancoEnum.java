/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.enumeradores;

/**
 * <AUTHOR>
 */
public enum SchemaBancoEnum {

    /**
     * Quando for necessario atualizar este arquivo gera-lo com o seguinte comando "pg_dump --column-inserts 'nome do banco' > 'nome do arquivo de saida'.sql'
     * by <PERSON><PERSON> - 17/12/2019
     * pg_dump -U zillyonweb --column-inserts povoador > C:\PactoJ\script\ZW_SCHEMA.sql
     */
    PADRAO(0, "BANCO PADRÃO", "ZW_SCHEMA_PADRAO.sql", ""),
    CROSS_EXPERIENCE(1, "BANCO CROSS EXPERIENCE", "ZW_SCHEMA_CROSS_EXPERIENCE.sql", "TR_SCHEMA_CROSS_EXPERIENCE.sql"),
    PRATIQUE(2, "BANCO PRATIQUE", "ZW_SCHEMA_PRATIQUE.sql", "TR_SCHEMA_PRATIQUE.sql"),
    MYBOX(3, "BANCO MY BOX", "ZW_SCHEMA_MYBOX.sql", "TR_SCHEMA_MYBOX.sql"),
    IMPLANTACAO(4, "BANCO IMPLANTACAO", "ZW_SCHEMA_PADRAO.sql", "TR_SCHEMA_PADRAO.sql"),
    ENGENHARIA_DO_CORPO(5, "BANCO ENGENHARIA DO CORPO", "ZW_SCHEMA_EDC.sql", "TR_SCHEMA_EDC.sql"),
    GREEN_LIFE(6, "BANCO GREEN LIFE", "ZW_SCHEMA_GREEN_LIFE.sql", "TR_SCHEMA_PADRAO.sql"),
    CORPO_E_SAUDE(7, "BANCO CORPO E SAUDE", "ZW_SCHEMA_CORPO_E_SAUDE.sql", "TR_SCHEMA_PADRAO.sql"),
    LIVE(8, "BANCO LIVE", "ZW_SCHEMA_LIVE.sql", "TR_SCHEMA_PADRAO.sql"),
    ;

    private Integer id;
    private String descricao;
    private String arquivoZW;
    private String arquivoTR;

    SchemaBancoEnum(final Integer id, final String descricao, final String arquivoZW, final String arquivoTR) {
        this.id = id;
        this.descricao = descricao;
        this.arquivoZW = arquivoZW;
        this.arquivoTR = arquivoTR;
    }

    public static SchemaBancoEnum getFromID(int id) {
        for (SchemaBancoEnum carteira : values()) {
            if (carteira.getId() == id) {
                return carteira;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getArquivoZW() {
        return arquivoZW;
    }

    public String getArquivoTR() {
        return arquivoTR;
    }
}
