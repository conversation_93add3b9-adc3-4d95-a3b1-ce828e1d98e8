package br.com.pacto.util.enumeradores;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.util.impl.Ordenacao;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public enum DiasSemana {

    // Enumeradores
    DOMINGO("DM", 1, "domingo"),
    SEGUNDA_FEIRA("SG", 2, "segunda"),
    TERCA_FEIRA("TR", 3, "terca"),
    QUARTA_FEIRA("QA", 4, "quarta"),
    QUINTA_FEIRA("QI", 5, "quinta"),
    SEXTA_FEIRA("SX", 6, "sexta"),
    SABADO("SB", 7, "sabado");
    // Atributos
    private String codigo;
    private String chave;
    private Integer numeral;

    // Métodos da Classe
    /**
     * Método que seta o código e descrição
     *
     * @param codigo
     * @param descricao
     */
    private DiasSemana(final String codigo, final Integer numeral, final String chave) {
        this.setCodigo(codigo);
        this.setNumeral(numeral);
        this.setChave(chave);
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    /**
     * Busca o código do enumerador e retorna o enumerador
     *
     * @param codigo
     * @return diaSemana
     */
    public static DiasSemana getDiaSemana(final String codigo) {
        DiasSemana diaSemana = null;
        for (DiasSemana dia : DiasSemana.values()) {
            if (dia.getCodigo().equals(codigo)) {
                diaSemana = dia;
            }
        }
        return diaSemana;
    }

    public static DiasSemana getDiaSemanaNumeral(final Integer numeral) {
        DiasSemana diaSemana = null;
        for (DiasSemana dia : DiasSemana.values()) {
            if (dia.getNumeral().equals(numeral)) {
                diaSemana = dia;
            }
        }
        return diaSemana;
    }

    // Getters and Setters
    /**
     * @return O campo codigo.
     */
    public String getCodigo() {
        return this.codigo;
    }

    /**
     * @param codigo O novo valor de codigo.
     */
    private void setCodigo(final String codigo) {
        this.codigo = codigo;
    }

    /**
     * @param numeral the numeral to set
     */
    public void setNumeral(Integer numeral) {
        this.numeral = numeral;
    }

    /**
     * @return the numeral
     */
    public Integer getNumeral() {
        return numeral;
    }

    public boolean equals_ISO8601(Date data) {
        int dayOfWeek = Calendario.getInstance(data).get(Calendar.DAY_OF_WEEK);
        if (this == DOMINGO && dayOfWeek == Calendar.SUNDAY) {
            return true;
        } else if (this == SEGUNDA_FEIRA && dayOfWeek == Calendar.MONDAY) {
            return true;
        } else if (this == TERCA_FEIRA && dayOfWeek == Calendar.TUESDAY) {
            return true;
        } else if (this == QUARTA_FEIRA && dayOfWeek == Calendar.WEDNESDAY) {
            return true;
        } else if (this == QUINTA_FEIRA && dayOfWeek == Calendar.THURSDAY) {
            return true;
        } else if (this == SEXTA_FEIRA && dayOfWeek == Calendar.FRIDAY) {
            return true;
        } else if (this == SABADO && dayOfWeek == Calendar.SATURDAY) {
            return true;
        }
        return false;
    }
    
    public static List<DiasSemana> getList(final List<String> lista){
        List<DiasSemana> result = new ArrayList<DiasSemana>();
        for (String s: lista){
            DiasSemana ds = DiasSemana.getDiaSemana(s);
            result.add(ds);
        }
        Ordenacao.ordenarLista(result, "numeral");
        return result;
    }
}
