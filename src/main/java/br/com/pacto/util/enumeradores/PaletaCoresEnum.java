/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum PaletaCoresEnum {
    // Azul
    AZUL_A(0,"Azul A", "#60C2EC"),
    AZUL_B(1,"Azul B", "#A5E7E7"),
    AZUL_C(2,"Azul C", "#35B5EC"),
    AZUL_D(3,"Azul D", "#5CE7E7"),
    AZUL_E(4,"Azul E", "#009BDC"),//
    AZUL_F(5,"Azul F", "#008888"),//
    AZUL_G(6,"Azul G", "#2C7C9F"),
    AZUL_H(7,"Azul H", "#1E5A5A"),
    AZUL_I(8,"Azul I", "#005E86"),
    AZUL_J(9,"Azul J", "#004848"),
    //Azul Escuro
    AZUL_ESCURO_A(10,"Azul Escuro A", "#60C2EC"),
    AZUL_ESCURO_B(11,"Azul Escuro B", "#A5E7E7"),
    AZUL_ESCURO_C(12,"Azul Escuro C", "#35B5EC"),//
    AZUL_ESCURO_D(13,"Azul Escuro D", "#5CE7E7"),
    AZUL_ESCURO_E(14,"Azul Escuro E", "#009BDC"),
    // Roxo
    ROXO_A(15,"Roxo A", "#D4A8EC"),
    ROXO_B(16,"Roxo B", "#BB63EC"),
    ROXO_C(17,"Roxo C", "#7109AA"),//
    ROXO_D(18,"Roxo D", "#9C299C"),
    ROXO_E(19,"Roxo E", "#9C179C"),
    ROXO_F(20,"Roxo F", "#740074"),//
    ROXO_G(21,"Roxo G", "#5A2974"),
    ROXO_H(22,"Roxo H", "#5C135C"),
    ROXO_I(23,"Roxo I", "#520052"),
    ROXO_J(24,"Roxo J", "#3E025F"),
    // Vermelho
    VERMELHO_A(25,"Vermelho A", "#FD6775"),
    VERMELHO_B(26,"Vermelho B", "#FD4C5D"),
    VERMELHO_C(27,"Vermelho C", "#D0404E"),
    VERMELHO_D(28,"Vermelho D", "#DD8E99"),
    VERMELHO_E(29,"Vermelho E", "#DD6677"),
    VERMELHO_F(30,"Vermelho F", "#843741"),
    VERMELHO_G(31,"Vermelho G", "#FC293B"),//
    VERMELHO_H(32,"Vermelho H", "#AC293B"),//
    VERMELHO_I(33,"Vermelho I", "#BE1424"),
    VERMELHO_J(34,"Vermelho J", "#740F1D"),
    // Laranja
    LARANJA_A(35,"Laranja A", "#FED788"),
    LARANJA_B(36,"Laranja B", "#FEC34C"),
    LARANJA_C(37,"Laranja C", "#FDA902"),//
    LARANJA_D(38,"Laranja D", "#C2912E"),
    LARANJA_E(39,"Laranja E", "#AB7201"),
    LARANJA_F(40,"Laranja F", "#EF9D5A"),
    LARANJA_G(41,"Laranja G", "#EF8732"),
    LARANJA_H(42,"Laranja H", "#E36700"),//
    LARANJA_I(43,"Laranja I", "#BA6721"),
    LARANJA_J(44,"Laranja J", "#AA4D00"),
    // Marrom
    MARROM_A(45,"Marrom A", "#DDB77C"),
    MARROM_B(46,"Marrom B", "#DDA245"),
    MARROM_C(47,"Marrom C", "#A36400"),//
    MARROM_D(48,"Marrom D", "#8B5D15"),
    MARROM_E(49,"Marrom E", "#814F00"),
    MARROM_F(50,"Marrom F", "#A87845"),
    MARROM_G(51,"Marrom G", "#A86926"),
    MARROM_H(52,"Marrom H", "#603100"),//
    MARROM_I(53,"Marrom I", "#452D13"),
    MARROM_J(54,"Marrom J", "#3B1E00"),
    // Verde
    VERDE_A(55,"Verde A", "#4CFF8E"),
    VERDE_B(56,"Verde B", "#58E28C"),
    VERDE_C(57,"Verde C", "#21C05A"),//
    VERDE_D(58,"Verde D", "#339B5A"),
    VERDE_E(59,"Verde E", "#0F8C3D"),
    VERDE_F(60,"Verde F", "#82C79C"),
    VERDE_G(61,"Verde G", "#50A56C"),
    VERDE_H(62,"Verde H", "#1F7740"),//
    VERDE_I(63,"Verde I", "#2E6843"),
    VERDE_J(64,"Verde J", "#0B5025"),
    // Verde Limao
    VERDE_LIMAO_A(65,"Verde Limao A", "#D5F29B"),
    VERDE_LIMAO_B(66,"Verde Limao B", "#BEF256"),
    VERDE_LIMAO_C(67,"Verde Limao C", "#8DD400"),//
    VERDE_LIMAO_D(68,"Verde Limao D", "#7BA723"),
    VERDE_LIMAO_E(69,"Verde Limao E", "#639500"),
    // Amarelo
    AMARELO_A(70,"Amarelo A", "#FEE790"),
    AMARELO_B(71,"Amarelo B", "#FEDB52"),
    AMARELO_C(72,"Amarelo C", "#FBC904"),//
    AMARELO_D(73,"Amarelo D", "#D5B123"),
    AMARELO_E(74,"Amarelo E", "#C69E02");

    private Integer id;
    private String descricao;
    private String cor;

    private PaletaCoresEnum(Integer id, String descricao, String cor) {
        this.id = id;
        this.descricao = descricao;
        this.cor = cor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getTextoCor() {
        String corConverter = cor;
        String corDoTexto = "";

        corConverter = corConverter.replace("#", "");

        Integer c_r = Integer.parseInt((corConverter.substring(0, 2)),16);
        Integer c_g = Integer.parseInt((corConverter.substring(2, 4)),16);
        Integer c_b = Integer.parseInt((corConverter.substring(4, 6)),16);

        Integer brilho = ((c_r * 299) + (c_g * 587) + (c_b * 114)) / 1000;

        if (brilho > 130) {
            corDoTexto = "#000000";
        } else {
            corDoTexto = "#FFFFFF";
        }

        return corDoTexto;
    }

    public String getClasseCor() {
        String corConverter = cor;
        String corDoTexto = "";

        corConverter = corConverter.replace("#", "");

        Integer c_r = Integer.parseInt((corConverter.substring(0, 2)),16);
        Integer c_g = Integer.parseInt((corConverter.substring(2, 4)),16);
        Integer c_b = Integer.parseInt((corConverter.substring(4, 6)),16);

        Integer brilho = ((c_r * 299) + (c_g * 587) + (c_b * 114)) / 1000;

        if (brilho > 130) {
            corDoTexto = "textoPreto";
        } else {
            corDoTexto = "textoBranco";
        }

        return corDoTexto;
    }

    public static PaletaCoresEnum getFromId(Integer id){
        for(PaletaCoresEnum tipo : values()){
            if(tipo.getId().equals(id)){
                return tipo;
            }
        }
        return null;
    }
}
