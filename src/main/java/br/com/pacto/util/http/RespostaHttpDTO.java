package br.com.pacto.util.http;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 29/04/2020
 */
public class RespostaHttpDTO {

    private Integer httpStatus;
    private String response;

    public Integer getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(Integer httpStatus) {
        this.httpStatus = httpStatus;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }
}
