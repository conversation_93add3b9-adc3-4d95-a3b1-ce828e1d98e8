/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.http;

import br.com.pacto.util.UteisValidacao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.security.cert.X509Certificate;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 29/04/2020
 */
public class RequestHttpService {

    private String charsetSend = "UTF-8";
    private String charsetResponse = "UTF-8";

    public RespostaHttpDTO executeRequest(String url, Map<String, String> headers, Map<String, String> params,
                                                        String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        return executeRequest(url, headers, params, null, body, null, metodoHttpEnum);
    }

    public RespostaHttpDTO executeRequest(String url, Map<String, String> headers,
                                                        Map<String, String> params, String encodingParams,
                                                        String body, String encodingBody, MetodoHttpEnum metodoHttpEnum) throws Exception {

        //adiconar os params
        url += getParamsString(params, encodingParams);

        //adiconar o body
        StringEntity entity = null;
        if (!UteisValidacao.emptyString(body)) {
            if (UteisValidacao.emptyString(encodingBody)) {
                encodingBody = charsetSend;
            }
            entity = new StringEntity(body, encodingBody);
        }

        if (metodoHttpEnum == null) {
            throw new Exception("Método HTTP não informado");
        }

        HttpResponse httpResponse = null;
        HttpClient httpClient = null;

        if (url.toLowerCase().contains("https:")) {
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

                        }

                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }

                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        }

                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        }
                    }
            };

            SSLContext sc = null;
            try {
                sc = SSLContext.getInstance("SSL");
                sc.init(null, trustAllCerts, new SecureRandom());
                httpClient = HttpClients.custom().setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).
                        setSslcontext(sc).build();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            httpClient = HttpClients.custom().build();
        }

        switch (metodoHttpEnum) {
            case GET:

                HttpGet httpGet = new HttpGet(url);
                for (String keyHeader : headers.keySet()) {
                    httpGet.addHeader(keyHeader, headers.get(keyHeader));
                }
                httpResponse = httpClient.execute(httpGet);
                break;

            case POST:

                HttpPost httpPost = new HttpPost(url);
                if (entity != null) {
                    httpPost.setEntity(entity);
                }
                for (String keyHeader : headers.keySet()) {
                    httpPost.addHeader(keyHeader, headers.get(keyHeader));
                }
                httpResponse = httpClient.execute(httpPost);
                break;

            case PUT:

                HttpPut httpPut = new HttpPut(url);
                if (entity != null) {
                    httpPut.setEntity(entity);
                }
                for (String keyHeader : headers.keySet()) {
                    httpPut.addHeader(keyHeader, headers.get(keyHeader));
                }
                httpResponse = httpClient.execute(httpPut);
                break;

            case DELETE:
                HttpDelete httpDelete = new HttpDelete(url);
                for (String keyHeader : headers.keySet()) {
                    httpDelete.addHeader(keyHeader, headers.get(keyHeader));
                }
                httpResponse = httpClient.execute(httpDelete);
                break;
        }

        RespostaHttpDTO respostaHttpDTO = new RespostaHttpDTO();
        if (httpResponse != null) {
            int httpStatus = httpResponse.getStatusLine().getStatusCode();
            respostaHttpDTO.setHttpStatus(httpStatus);
            try {
                respostaHttpDTO.setResponse(EntityUtils.toString(httpResponse.getEntity(), charsetResponse));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return respostaHttpDTO;
    }

    private String getParamsString(Map<String, String> params, String encodingParams) throws UnsupportedEncodingException {
        if (params == null || params.isEmpty()) {
            return "";
        }

        if (UteisValidacao.emptyString(encodingParams)) {
            encodingParams = charsetSend;
        }

        StringBuilder result = new StringBuilder();
        boolean first = true;
        for (String keyParams : params.keySet()) {
            if (first) {
                first = false;
                result.append("?");
            } else {
                result.append("&");
            }
            result.append(URLEncoder.encode(keyParams, encodingParams));
            result.append("=");
            result.append(URLEncoder.encode(params.get(keyParams), encodingParams));
        }
        return result.toString();
    }
}

