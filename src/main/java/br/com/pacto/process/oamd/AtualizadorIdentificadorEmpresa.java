package br.com.pacto.process.oamd;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by johny<PERSON> on 10/10/2016.
 */
public class AtualizadorIdentificadorEmpresa {

    private static final String HOST = "localhost";

    private static final String BANCO = "OAMD";

    private static final String PORTA = "5432";

    private static String USUARIO = "postgres";

    private static String SENHA = "pactodb";

    private Connection getConnectionOAMD() throws  Exception{
        return getConnection(HOST, BANCO, PORTA, USUARIO, SENHA);
    }

    private Connection getConnection(String host, String banco, String porta, String usuario, String senha) throws  Exception{
        Class.forName("org.postgresql.Driver");
        return DriverManager.getConnection("jdbc:postgresql://" + HOST +":" +porta+ "/" + banco, usuario, senha);
    }

    private ResultSet getEmpresasProcessar() throws  Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT chave, \"nomeBD\" as nomeBD, \"hostBD\" as hostBD, \"userBD\" as userBD, \"passwordBD\" as passwordDB, porta FROM empresa WHERE ativa");
        return getConnectionOAMD().prepareStatement(sql.toString()).executeQuery();
    }

    private List<String> getNomeEmpresa(String host, String banco, String porta, String usuario, String senha) throws  Exception{
        Connection con = getConnection(host, banco, porta, usuario, senha);
        ResultSet rs = con.prepareStatement("SELECT nome FROM empresa WHERE ativa").executeQuery();
        List<String> nomes = new ArrayList<String>();
        while(rs.next()){
            nomes.add(rs.getString("nome"));
        }
        return nomes;
    }

    private void atualizarIdentificador(String chave, String identificador) throws  Exception{
        PreparedStatement ps = getConnectionOAMD().prepareStatement("UPDATE empresa SET identificadorempresa = ? WHERE chave = ?");
        ps.setString(1, identificador);
        ps.setString(2, chave);
        ps.executeUpdate();
    }

    private void processarAtualizacaoIdentificador(){
        try{
            ResultSet rs = getEmpresasProcessar();
            while(rs.next()){
                String chave = rs.getString("chave");
                String nomeBD = rs.getString("nomeBD");
                String hostBD = rs.getString("hostBD");
                String userBD = rs.getString("userBD");
                String passwordDB = rs.getString("passwordDB");
                String porta = rs.getString("porta");
                try{
                    System.out.println("Tentando conectar no banco " + nomeBD + " chave " + chave);
                    List<String> nomes = getNomeEmpresa(hostBD, nomeBD, porta, userBD, passwordDB);
                    if(nomes.size() == 1){
                        System.out.println("Atualizando empresa " + chave + " banco " + nomeBD + "com identificadorempresa " + nomes.get(0));
                        atualizarIdentificador(chave, nomes.get(0));
                        System.out.println("Empresa " + chave + " banco " + nomeBD + " atualizada com sucesso!");
                    }else{
                        System.out.println("Empresa " + chave + " banco " + nomeBD + " não foi atualizada pois existem " + nomes.size() + " empresas cadastradas em sua base");
                    }
                }catch (Exception e){
                    System.out.println("Falha ao conectar no banco " + nomeBD + " chave " + chave);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            System.out.println("Falha ao processar atualização");
        }
    }

    public static void main(String[] args) {
        new AtualizadorIdentificadorEmpresa().processarAtualizacaoIdentificador();
    }

}
