package servicos.integracao.movidesk.controller;

import br.com.pacto.controller.jsf.base.SuperControle;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UtilContext;
import servicos.integracao.movidesk.body.RetornoMoviDesk;
import servicos.integracao.movidesk.service.MovideskImpl;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.SessionScoped;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ManagedBean(name = "MoviDeskControle")
@SessionScoped
public class MoviDeskControle extends SuperControle {
    private List<RetornoMoviDesk> lista = null;
    private MovideskImpl movideskImpl;
    private String mensagem;
    private String mensagemPronta;
    private String mensagemProblema = "Olá, informamos que estamos passando por uma instabilidade momentânea no sistema. Dentro de alguns minutos estará normalizado!\nSistema Pacto.";
    private String mensagemOk = "Olá, o acesso ao sistema está normalizado!\nSistema Pacto.";
    private String mensagemProblemaSms = "Ola, informamos que no momento estamos passando por uma instabilidade momentanea no sistema. Dentro de alguns minutos estara normalizado!\nSistema Pacto.";
    private String mensagemOkSms = "Ola, o acesso ao sistema esta normalizado!\nSistema Pacto.";
    private boolean marcarTodos = false;
    private Date inicio = new Date();
    private Date fim = new Date();
    private boolean fecharTickets = false;
    private boolean marcarAlertaZW;
    EmpresaService empresaService = (EmpresaService) UtilContext.getBean(EmpresaService.class);
    private boolean enviarChat = true;
    private boolean enviarSms = true;
    private String urlQrCode;
    private String corStatus;
    private String celularConectado;

    public MoviDeskControle() {
        // verificaAlertaZW();
    }

    @PostConstruct
    public void init() {
        //lista = getMovideskImpl().buscaTicketsAbertosMovideskJsonObject();
        // verificaAlertaZW();
    }

    public void verificaAlertaZW() {
        marcarAlertaZW = empresaService.alertaZWAtivo();
    }

    public void enviarMensagens() {
        if (getMensagem().trim().length() > 0) {
            if (isEnviarChat() || isEnviarSms()) {
                StringBuilder sb = new StringBuilder("Mensagens enviadas para:\n");
                boolean mensagensEnviadas = false;
                for (RetornoMoviDesk retornoMoviDesk : lista) {
                    if (retornoMoviDesk.isEnviarMensagem()) {
                        retornoMoviDesk.getMensagemEnviar().setMensagem(getMensagem());
                        if (isEnviarChat()) {
                            getMovideskImpl().enviarMensagemWhatsApp(retornoMoviDesk, isFecharTickets());
                        }
                        if (isEnviarSms()) {
                            getMovideskImpl().enviarSMS(retornoMoviDesk);
                        }
                        retornoMoviDesk.setEnviarMensagem(false);
                        sb.append("+" + retornoMoviDesk.getCreatedBy().getPhone() + "\n");
                        mensagensEnviadas = true;
                        empresaService.salvaMensagemEnviada(retornoMoviDesk.getMensagemEnviar());
                        retornoMoviDesk.setMensagensEnviadas(empresaService.buscaMensagensEnviadas(retornoMoviDesk.getId()));
                    }
                }
                if (mensagensEnviadas) {
                    showMessageGrowView(sb.toString());
                } else {
                    showMessageGrowView("Selecione ao menos um ticket para enviar mensagem!");
                }
            } else {
                showMessageGrowView("Selecione como deseja enviar a mensagem (Whatsapp ou SMS)");
            }
        } else {
            showMessageGrowView("Selecione ou digite uma mensagem antes de enviar!");
        }
    }

    public void logoutChat() {
        getMovideskImpl().logoutChat();
    }

    public void atualizarLista() {
        lista = getMovideskImpl().buscaTicketsAbertosMovideskJsonObject(getInicio(), getFim());
        setMarcarTodos(false);
    }

    public void marcarTodostickets() {
        for (RetornoMoviDesk r : lista) {
            if (r.isPermiteEnviar()) {
                r.setEnviarMensagem(isMarcarTodos());
            }
        }
    }

    public void setaMensagem() {
        if (getMensagemPronta().equals("1"))
            setMensagem(getMensagemProblema());
        else if (getMensagemPronta().equals("2"))
            setMensagem(getMensagemOk());
        else
            setMensagem("");
    }

    public void ativarAlertaZW() {
        empresaService.ativaAlertaZW(isMarcarAlertaZW());
    }

    public List<RetornoMoviDesk> getLista() {
        if (null == lista)
            lista = new ArrayList<RetornoMoviDesk>();
        return lista;
    }

    public void setLista(List<RetornoMoviDesk> lista) {
        this.lista = lista;
    }

    private MovideskImpl getMovideskImpl() {
        if (null == movideskImpl)
            movideskImpl = new MovideskImpl();
        return movideskImpl;
    }

    public String getMensagem() {
        if (null == this.mensagem)
            this.mensagem = "";
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getMensagemProblemaSms() {
        if (null == this.mensagemProblemaSms)
            this.mensagemProblemaSms = "";
        return mensagemProblemaSms;
    }

    public void setMensagemProblemaSms(String mensagemProblemaSms) {
        this.mensagemProblemaSms = mensagemProblemaSms;
    }

    public String getMensagemOkSms() {
        return mensagemOkSms;
    }

    public void setMensagemOkSms(String mensagemOkSms) {
        this.mensagemOkSms = mensagemOkSms;
    }

    public boolean isMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }

    public String getMensagemPronta() {
        return mensagemPronta;
    }

    public void setMensagemPronta(String mensagemPronta) {
        this.mensagemPronta = mensagemPronta;
    }

    public String getMensagemProblema() {
        return mensagemProblema;
    }

    public void setMensagemProblema(String mensagemProblema) {
        this.mensagemProblema = mensagemProblema;
    }

    public String getMensagemOk() {
        return mensagemOk;
    }

    public void setMensagemOk(String mensagemOk) {
        this.mensagemOk = mensagemOk;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public boolean isFecharTickets() {
        return fecharTickets;
    }

    public void setFecharTickets(boolean fecharTickets) {
        this.fecharTickets = fecharTickets;
    }

    public boolean isMarcarAlertaZW() {
        return marcarAlertaZW;
    }

    public void setMarcarAlertaZW(boolean marcarAlertaZW) {
        this.marcarAlertaZW = marcarAlertaZW;
    }

    public boolean isEnviarChat() {
        return enviarChat;
    }

    public void setEnviarChat(boolean enviarChat) {
        this.enviarChat = enviarChat;
    }

    public boolean isEnviarSms() {
        return enviarSms;
    }

    public void setEnviarSms(boolean enviarSms) {
        this.enviarSms = enviarSms;
    }

    public String getUrlQrCode() {
        urlQrCode = getMovideskImpl().getUrlQrCode();
        return urlQrCode + "&t=" + System.currentTimeMillis();
    }

    public void setUrlQrCode(String urlQrCode) {
        this.urlQrCode = urlQrCode;
    }

    public String getCorStatus() {
        getMovideskImpl().getStatusChat();
        if (getMovideskImpl().isCelularConectado()) {
            corStatus = "green";
            celularConectado = "Celular conectado na API do WhatsApp";
        } else {
            corStatus = "red";
            celularConectado = "Celular desconectado da API do WhatsApp";
        }
        return corStatus;
    }

    public void setCorStatus(String corStatus) {
        this.corStatus = corStatus;
    }

    public String getCelularConectado() {
        return celularConectado;
    }

    public void setCelularConectado(String celularConectado) {
        this.celularConectado = celularConectado;
    }

    public boolean isCelularConectadoAPI() {
        return getMovideskImpl().isCelularConectado();
    }

    public String getStatusLoading() {
        return getMovideskImpl().getStatusLoading();
    }

    public boolean isStatusLoadingDesconectar(){
        return getMovideskImpl().isStatusLoadingDesconectar();
    }
}
