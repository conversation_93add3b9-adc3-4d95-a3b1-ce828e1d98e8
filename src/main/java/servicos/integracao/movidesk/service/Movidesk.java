package servicos.integracao.movidesk.service;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.http.*;
import servicos.integracao.movidesk.body.RetornoChat;
import servicos.integracao.movidesk.body.RetornoMoviDesk;
import servicos.integracao.movidesk.body.RetornoOwnerMoviDesk;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public interface Movidesk {

    void enviarMensagemWhatsApp(RetornoMoviDesk retornoMoviDesk, boolean fecharTicket);

    void getQrCodeChat();

    void logoutChat();

    void getStatusChat();

    void fecharTicket(RetornoMoviDesk retornoMoviDesk);

    HttpHeaders getHeader(boolean chat);

    String formataDataParaMovidesk(Date data);

    List<RetornoMoviDesk> buscaTicketsAbertosMovideskJsonObject(Date inicio, Date fim);

    void montaMensagemEnviar(RetornoMoviDesk obj);

    void montaOwnerECreateBy(RetornoOwnerMoviDesk obj, JSONObject jObj);

    void montaTelefone(RetornoOwnerMoviDesk retorno);

    void enviarSMS(RetornoMoviDesk retornoMoviDesk);

}
