package servicos.integracao.movidesk.service;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UtilContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import servicos.integracao.movidesk.body.*;
import servicos.integracao.movidesk.dao.MensagemDAO;
import servicos.integracao.movidesk.error.RestResponseExceptionHandler;

import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class MovideskImpl implements Movidesk {
    private static RestTemplate restTemplate = new RestTemplateBuilder().errorHandler(new RestResponseExceptionHandler()).build();
    EmpresaService empresaService = (EmpresaService) UtilContext.getBean(EmpresaService.class);

    private static final String TOKEN_CHAT_API = "?token=xcyp1uuuxddtabbc";
    private static final String CHAT_API = "https://api.chat-api.com/instance100572/";
    private static final String URL_CHAT_API = CHAT_API + "sendMessage" + TOKEN_CHAT_API;
    private static final String CHAT_API_STATUS = CHAT_API + "status" + TOKEN_CHAT_API;
    private static final String CHAT_API_QR_CODE = CHAT_API + "qr_code" + TOKEN_CHAT_API;
    private static final String CHAT_API_LOGOUT = CHAT_API + "logout" + TOKEN_CHAT_API;

    private static final String URL_SMS_API = "https://s.smsup.com.br/smsservice/api/sender/%s";
    private static final String TOKEN_SMS_API = "klWlG3r2VOhNeJS6zSEbzg==";
    private static final String KEY_SMS_API = "aae06a9469a47e5b58da769ec6041af0";

    private static final String TOKEN_MOVI_DESK_API = "ede24129-1880-4b07-bd54-ea0633da9f32";
    private static final String SELECT_MOVI_DESK_API = "&$select=id,subject,status,createdDate,resolvedIn,actions,baseStatus,category,serviceFirstLevel,serviceSecondLevel,serviceThirdLevel&$expand=owner,createdBy&$filter=baseStatus%20ne%20%27closed%27%20and%20baseStatus%20ne%20%20%27Canceled%27%20and%20baseStatus%20ne%20%20%27Stopped%27%20and%20serviceSecondLevel%20eq%20%27INFRAESTRUTURA%27%20and%20createdDate%20ge%20INICIO%20and%20createdDate%20le%20FIM%20&$orderby=baseStatus";
    private static final String URL_MOVIDESK_API = "https://api.movidesk.com/public/v1/tickets?token=" + TOKEN_MOVI_DESK_API;
    private static final String ALTERAR_MOVIDESK_API = URL_MOVIDESK_API + "&id=";

    private boolean celularConectado = false;
    private String urlQrCode;
    private String statusLoading;
    private boolean statusLoadingDesconectar = false;

    @Autowired
    private MensagemDAO mensagemDAO;

    public MovideskImpl() {
        restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(Charset.forName("UTF-8")));
    }

    @Override
    public void enviarMensagemWhatsApp(RetornoMoviDesk retornoMoviDesk, boolean fecharTicket) {
        HttpHeaders headers = getHeader(true);
        JSONObject body = new JSONObject();
        //body.put("phone", "*************");
        body.put("phone", retornoMoviDesk.getCreatedBy().getPhone());
        body.put("body", retornoMoviDesk.getMensagemEnviar().getMensagem());
        try {
            HttpEntity<String> request = new HttpEntity<>(body.toString(), headers);
            ResponseEntity<RetornoChat> response = restTemplate.postForEntity(URL_CHAT_API, request, RetornoChat.class);
            montaMensagemEnviar(retornoMoviDesk);
            if (fecharTicket) {
                fecharTicket(retornoMoviDesk);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void getQrCodeChat() {

    }

    @Override
    public void logoutChat() {
        try {
            HttpClient client = HttpClientBuilder.create().build();
            HttpResponse response = client.execute(new HttpGet(CHAT_API_LOGOUT));

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
            if (statusCode == 200) {
                System.out.println(responseBody);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void getStatusChat() {
        try {
            HttpClient client = HttpClientBuilder.create().build();
            HttpResponse response = client.execute(new HttpGet(CHAT_API_STATUS));

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
            setStatusLoading("");
            if (statusCode == 200) {
                System.out.println(responseBody);
                JSONObject js = new JSONObject(responseBody);
                if (js.getString("accountStatus").equalsIgnoreCase("authenticated")) {
                    setCelularConectado(true);
                    setStatusLoadingDesconectar(false);
                } else {
                    setCelularConectado(false);
                    setStatusLoadingDesconectar(true);
                    /*if (js.getString("accountStatus").equalsIgnoreCase("got qr code")) {
                        setStatusLoadingDesconectar(true);
                    }*/
                    if (js.getString("accountStatus").equalsIgnoreCase("loading")) {
                        setStatusLoading("O celular utilizado pode estar conectado no WhatsApp Web e não pode ser usado aqui. \nPara corrigir, encerre todas as conexões ativas do WhatsApp Web no celular conectado ou acesse o Chat API (https://app.chat-api.com), faça login com a conta da Pacto Soluções e clique na opção \"Usar Aqui\"! \nApós isso, aguarde 60 segundos e atualize esta página para gerar o QR CODE.");
                        setStatusLoadingDesconectar(false);
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void enviarSMS(RetornoMoviDesk retornoMoviDesk) {

        Message message = new Message();
        message.setDataRegistro(new Date());
        message.setMsg(Uteis.removeCaracteresEspeciais(retornoMoviDesk.getMensagemEnviar().getMensagem()));
        //message.setNumero("*************");
        message.setNumero(retornoMoviDesk.getMensagemEnviar().getTelefone());
        ArrayList<Message> messages = new ArrayList<Message>();
        messages.add(message);
        ObjectMapper mapper = new ObjectMapper();

        try {
            org.apache.http.client.methods.HttpPost httpPost = new org.apache.http.client.methods.HttpPost(String.format(URL_SMS_API, "doAsObject"));

            List<NameValuePair> params = new ArrayList<NameValuePair>();
            params.add(new BasicNameValuePair("key", KEY_SMS_API));
            params.add(new BasicNameValuePair("token", TOKEN_SMS_API));
            params.add(new BasicNameValuePair("msgs", mapper.writeValueAsString(messages)));

            httpPost.setEntity(new UrlEncodedFormEntity(params));
            UnsafeSSLHelper unsafeSSLHelper = new UnsafeSSLHelper();

            org.apache.http.impl.client.CloseableHttpClient httpClient = HttpClientBuilder
                    .create()
                    .setSslcontext(unsafeSSLHelper.createUnsecureSSLContext())
                    .setHostnameVerifier(unsafeSSLHelper.getPassiveX509HostnameVerifier())
                    .build();

            org.apache.http.client.methods.CloseableHttpResponse response = httpClient.execute(httpPost);
            org.apache.http.HttpEntity entity = response.getEntity();

            System.out.println(EntityUtils.toString(entity));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void fecharTicket(RetornoMoviDesk retornoMoviDesk) {
        try {
            JSONObject body = new JSONObject();
            body.put("status", "Resolvido");
            body.put("baseStatus", "Resolved");
            body.put("actions", "[{\"id\":1,\"type\":2,\"origin\":9,\"description\":\"Fechado pelo OAMD\",\"status\":\"Resolvido\",\"justification\":\""
                    + (retornoMoviDesk.getMensagemEnviar().getMensagem().length() > 128 ? retornoMoviDesk.getMensagemEnviar().getMensagem().substring(0, 127) : retornoMoviDesk.getMensagemEnviar().getMensagem())
                    + "\",\"createdDate\":\"" + formataDataParaMovidesk(new Date()) + "\",\"createdBy\":{\"id\":\"302050a3-bef7-4391-\",\"personType\":1,\"profileType\":3,\"businessName\":\"Sistema OAMD\",\"email\":\"<EMAIL>\",\"phone\":\"(62) 99999-9999\"}}]");
            StringEntity entity = new StringEntity(body.toString(), "UTF-8");
            HttpPatch httpPatch = new HttpPatch(ALTERAR_MOVIDESK_API + retornoMoviDesk.getId());
            httpPatch.setEntity(entity);
            httpPatch.setHeader("Accept", "*/*");
            httpPatch.setHeader("Cache-Control", "no-cache");
            httpPatch.setHeader("Connection", "keep-alive");
            httpPatch.setHeader("Content-Type", "application/json");
            httpPatch.setHeader("Accept-Encoding", "gzip, deflate, br");

            HttpClient client = HttpClientBuilder.create().build();
            HttpResponse response = client.execute(httpPatch);


            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            System.out.println("RESPOSTA: " + responseBody);
            if (statusCode == 200) {

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public HttpHeaders getHeader(boolean chat) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", "*/*");
        headers.add("Cache-Control", "no-cache");
        headers.add("Connection", "keep-alive");
        headers.add("Content-Type", "application/json");
        headers.add("Accept-Encoding", "gzip, deflate, br");
        if (chat) {
            headers.add("Host", "api.chat-api.com");
        } else {
            headers.add("Host", "api.movidesk.com");
        }
        return headers;
    }

    @Override
    public String formataDataParaMovidesk(Date data) {
        StringBuilder dataFormatada = new StringBuilder(new SimpleDateFormat("yyyy-MM-dd").format(data));
        dataFormatada.append("T");
        dataFormatada.append(new SimpleDateFormat("HH:mm:ss").format(data) + ".00z");
        return dataFormatada.toString();
    }

    @Override
    public List<RetornoMoviDesk> buscaTicketsAbertosMovideskJsonObject(Date inicio, Date fim) {
        List<RetornoMoviDesk> lista = null;
        RetornoMoviDesk retornoMoviDesk = null;
        try {
            HttpClient client = HttpClientBuilder.create().build();
            HttpResponse response = client.execute(new HttpGet((URL_MOVIDESK_API + SELECT_MOVI_DESK_API).replaceAll("INICIO", formataDataParaMovidesk(inicio)).replaceAll("FIM", formataDataParaMovidesk(fim))));

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
            if (statusCode == 200) {
                lista = new ArrayList<RetornoMoviDesk>();
                JSONArray jsonArray = new JSONArray(responseBody);
                for (int x = 0; x < jsonArray.length(); x++) {
                    retornoMoviDesk = new RetornoMoviDesk();

                    JSONObject js = jsonArray.getJSONObject(x);
                    retornoMoviDesk.setJson(js.toString());

                    retornoMoviDesk.setId(Long.parseLong(js.get("id").toString()));
                    retornoMoviDesk.setServiceThirdLevel(js.get("serviceThirdLevel").toString());
                    retornoMoviDesk.setServiceSecondLevel(js.get("serviceSecondLevel").toString());
                    retornoMoviDesk.setServiceFirstLevel(js.get("serviceFirstLevel").toString());
                    retornoMoviDesk.setCategory(js.get("category").toString());
                    retornoMoviDesk.setBaseStatus(js.get("baseStatus").toString());
                    retornoMoviDesk.setResolvedIn(js.get("resolvedIn").toString());
                    retornoMoviDesk.setCreatedDate(js.get("createdDate").toString());
                    retornoMoviDesk.setStatus(js.get("status").toString());
                    retornoMoviDesk.setSubject(js.get("subject").toString());

                    montaOwnerECreateBy(retornoMoviDesk.getCreatedBy(), new JSONObject(js.get("createdBy").toString()));
                    montaOwnerECreateBy(retornoMoviDesk.getOwner(), new JSONObject(js.get("owner").toString()));

                    retornoMoviDesk.setMensagensEnviadas(empresaService.buscaMensagensEnviadas(retornoMoviDesk.getId()));

                    lista.add(retornoMoviDesk);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return lista;
    }

    @Override
    public void montaMensagemEnviar(RetornoMoviDesk obj) {
        obj.getMensagemEnviar().setTelefone(obj.getCreatedBy().getPhone());
        obj.getMensagemEnviar().setDataEnvio(new Date());
        obj.getMensagemEnviar().setNumeroChamado(obj.getId());

    }

    @Override
    public void montaOwnerECreateBy(RetornoOwnerMoviDesk obj, JSONObject jObj) {
        obj.setJson(jObj.toString());
        obj.setId(jObj.get("id").toString());
        obj.setPersonType(jObj.get("personType").toString());
        obj.setProfileType(jObj.get("profileType").toString());
        obj.setBusinessName(jObj.get("businessName").toString());
        obj.setEmail(jObj.get("email").toString());
        obj.setPhone(jObj.get("phone").toString());
        montaTelefone(obj);
        obj.setPathPicture(jObj.get("pathPicture").toString());
        //obj.setAddress(jObj.get("address").toString());
        //obj.setComplement(jObj.get("complement").toString());
        //obj.setCep(jObj.get("cep").toString());
        //obj.setCity(jObj.get("city").toString());
        //obj.setBairro(jObj.get("bairro").toString());
        //obj.setNumber(jObj.get("number").toString());
        //obj.setReferencel(jObj.get("reference").toString());
    }

    @Override
    public void montaTelefone(RetornoOwnerMoviDesk retorno) {
        retorno.setPhone("55" + retorno.getPhone()
                .replaceAll("[(]", "")
                .replaceAll("[)]", "")
                .replaceAll("-", "")
                .replaceAll("  ", "")
                .replaceAll(" ", ""));
        if (null != retorno.getPhone()
                && retorno.getPhone().trim().length() > 5
                && (retorno.getPhone().substring(4, 5).equalsIgnoreCase("9") || retorno.getPhone().substring(4, 5).equalsIgnoreCase("8"))) {
            retorno.setTipoTelefone("CELULAR");
        } else {
            retorno.setTipoTelefone("FIXO");
        }
    }

    public boolean isCelularConectado() {
        return celularConectado;
    }

    public void setCelularConectado(boolean celularConectado) {
        this.celularConectado = celularConectado;
    }

    public String getUrlQrCode() {
        if (null == urlQrCode || urlQrCode.trim().length() == 0)
            urlQrCode = CHAT_API_QR_CODE;
        return urlQrCode;
    }

    public void setUrlQrCode(String urlQrCode) {
        this.urlQrCode = urlQrCode;
    }

    public String getStatusLoading() {
        return statusLoading;
    }

    public void setStatusLoading(String statusLoading) {
        this.statusLoading = statusLoading;
    }

    public boolean isStatusLoadingDesconectar() {
        return statusLoadingDesconectar;
    }

    public void setStatusLoadingDesconectar(boolean statusLoadingDesconectar) {
        this.statusLoadingDesconectar = statusLoadingDesconectar;
    }
}
