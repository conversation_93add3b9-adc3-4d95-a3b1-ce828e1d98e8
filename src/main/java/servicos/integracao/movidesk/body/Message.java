package servicos.integracao.movidesk.body;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

public class Message {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer id;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date dataRegistro;
    private String msg;
    private String codigoExterno;
    private String tipo = "SMS";
    private String urlAudio;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long numberint;
    private String numero;

    public Message() {
    }

    public Message(Long numeroInt) {
        this.numberint = numeroInt;
    }

    public Integer getId() {
        return id;
    }

    public Message setId(Integer id) {
        this.id = id;
        return this;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public Message setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public Message setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public Message setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
        return this;
    }

    public String getTipo() {
        return tipo;
    }

    public Message setTipo(String tipo) {
        this.tipo = tipo;
        return this;
    }

    public String getUrlAudio() {
        return urlAudio;
    }

    public Message setUrlAudio(String urlAudio) {
        this.urlAudio = urlAudio;
        return this;
    }

    public Long getNumberint() {
        return numberint;
    }

    public Message setNumberint(Long numberint) {
        this.numberint = numberint;
        return this;
    }

    public String getNumero() {
        return numero;
    }

    public Message setNumero(String numero) {

        numero = numero.replaceAll("\\D", "");

        if(!numero.startsWith("55")){
            this.numero = "55"+numero;
        }else{
            this.numero = numero;
        }


        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Message)) return false;

        Message message = (Message) o;

        return numberint.equals(message.numberint);
    }

    @Override
    public int hashCode() {
        return numberint.hashCode();
    }
}
