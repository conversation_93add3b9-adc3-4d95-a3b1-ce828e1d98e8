package servicos.integracao.movidesk.body;

import br.com.pacto.annotation.AnotacaoTable;
import br.com.pacto.annotation.AnotacaoTransient;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity(name = "MensagensEnviadas")
@AnotacaoTable(nome = "mensagensEnviadas")
@Audited
public class MensagensEnviadas implements Serializable {

    @AnotacaoTransient
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long codigo = 0L;

    @Column(name = "mensagem", nullable = false)
    private String mensagem;

    @Column(name = "numeroChamado", nullable = false)
    private Long numeroChamado;

    @Column(name = "dataEnvio", nullable = false)
    private Date dataEnvio;

    @Column(name = "telefone", nullable = false)
    private String telefone;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Long getNumeroChamado() {
        return numeroChamado;
    }

    public void setNumeroChamado(Long numeroChamado) {
        this.numeroChamado = numeroChamado;
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }
}
