package servicos.integracao.movidesk.body;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

public class RetornoOwnerMoviDesk implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private String personType;
    private String profileType;
    private String businessName;
    private String email;
    private String phone;
    private String pathPicture;
    private String address;
    private String complement;
    private String cep;
    private String city;
    private String bairro;
    private String number;
    private String referencel;
    private String json;

    @JsonIgnore
    private String tipoTelefone;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPersonType() {
        return personType;
    }

    public void setPersonType(String personType) {
        this.personType = personType;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone.replaceAll("55null", "S/N");
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPathPicture() {
        return pathPicture;
    }

    public void setPathPicture(String pathPicture) {
        this.pathPicture = pathPicture;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getComplement() {
        return complement;
    }

    public void setComplement(String complement) {
        this.complement = complement;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getReferencel() {
        return referencel;
    }

    public void setReferencel(String referencel) {
        this.referencel = referencel;
    }

    public String getTipoTelefone() {
        return tipoTelefone;
    }

    public void setTipoTelefone(String tipoTelefone) {
        this.tipoTelefone = tipoTelefone;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getBusinessNameFmt() {
        try {
            String nome[] = this.businessName.split(" ");
            return (nome[0] + " " + nome[1]).toUpperCase();
        } catch (Exception e) {
            // e.printStackTrace();
            return this.businessName.toUpperCase();
        }
    }
}