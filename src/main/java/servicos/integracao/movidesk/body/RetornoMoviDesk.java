package servicos.integracao.movidesk.body;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.List;

public class RetornoMoviDesk implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String serviceThirdLevel;
    private String serviceSecondLevel;
    private String serviceFirstLevel;
    private String category;
    private String baseStatus;
    private String resolvedIn;
    private String createdDate;
    private String status;
    private String subject;
    private RetornoOwnerMoviDesk owner;
    private RetornoOwnerMoviDesk createdBy;
    private boolean enviarMensagem;
    private MensagensEnviadas mensagemEnviar;
    private List<MensagensEnviadas> mensagensEnviadas;
    private String json;
    private boolean foiEnviadomensagens = false;
    private String todasMensagensEnviadas;

    public String dataFormatada(String data) {
        try {
            return data.substring(8, 10) + "/" + data.substring(5, 7) + "/" + data.substring(0, 4) + " " + data.substring(11, 19);
        } catch (Exception e) {
            return data.replaceAll("null", "");
        }
    }

    public boolean isPermiteEnviar() {
        return getCreatedBy().getTipoTelefone().equals("CELULAR");
    }

    public String getTodasMensagensEnviadas() {
        todasMensagensEnviadas = "";
        for (MensagensEnviadas m : mensagensEnviadas) {
            try {
                todasMensagensEnviadas += m.getMensagem() + "\nEnviada em " + new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(m.getDataEnvio()) + "\n---------------------\n";
            } catch (Exception e) {
                e.printStackTrace();
                todasMensagensEnviadas += m.getMensagem() + "\n---------------------\n";
            }
        }
        return todasMensagensEnviadas;
    }

    public void setTodasMensagensEnviadas(String todasMensagensEnviadas) {
        this.todasMensagensEnviadas = todasMensagensEnviadas;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getServiceThirdLevel() {
        return serviceThirdLevel.toUpperCase();
    }

    public void setServiceThirdLevel(String serviceThirdLevel) {
        this.serviceThirdLevel = serviceThirdLevel;
    }

    public String getServiceSecondLevel() {
        return serviceSecondLevel.toUpperCase();
    }

    public void setServiceSecondLevel(String serviceSecondLevel) {
        this.serviceSecondLevel = serviceSecondLevel;
    }

    public String getServiceFirstLevel() {
        return serviceFirstLevel.toUpperCase();
    }

    public void setServiceFirstLevel(String serviceFirstLevel) {
        this.serviceFirstLevel = serviceFirstLevel;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getBaseStatus() {
        return baseStatus;
    }

    public void setBaseStatus(String baseStatus) {
        this.baseStatus = baseStatus;
    }

    public String getResolvedIn() {
        return dataFormatada(resolvedIn);
    }

    public void setResolvedIn(String resolvedIn) {
        this.resolvedIn = resolvedIn;
    }

    public String getCreatedDate() {
        return dataFormatada(createdDate);
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getStatus() {
        return status.replaceAll("Em Atendimento", "Em Atend.").toUpperCase();
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSubject() {
        return subject.toUpperCase();
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public RetornoOwnerMoviDesk getOwner() {
        if (null == owner)
            owner = new RetornoOwnerMoviDesk();
        return owner;
    }

    public void setOwner(RetornoOwnerMoviDesk owner) {
        this.owner = owner;
    }

    public RetornoOwnerMoviDesk getCreatedBy() {
        if (null == createdBy)
            createdBy = new RetornoOwnerMoviDesk();
        return createdBy;
    }

    public void setCreatedBy(RetornoOwnerMoviDesk createdBy) {
        this.createdBy = createdBy;
    }


    public boolean isEnviarMensagem() {
        return enviarMensagem;
    }

    public void setEnviarMensagem(boolean enviarMensagem) {
        this.enviarMensagem = enviarMensagem;
    }

    public MensagensEnviadas getMensagemEnviar() {
        if (null == mensagemEnviar)
            mensagemEnviar = new MensagensEnviadas();
        return mensagemEnviar;
    }

    public void setMensagemEnviar(MensagensEnviadas mensagemEnviar) {
        this.mensagemEnviar = mensagemEnviar;
    }

    public List<MensagensEnviadas> getMensagensEnviadas() {
        return mensagensEnviadas;
    }

    public void setMensagensEnviadas(List<MensagensEnviadas> mensagensEnviadas) {
        this.mensagensEnviadas = mensagensEnviadas;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public boolean isFoiEnviadomensagens() {
        if (null != getMensagensEnviadas() && !getMensagensEnviadas().isEmpty()) {
            foiEnviadomensagens = true;
        }
        return foiEnviadomensagens;
    }

    public void setFoiEnviadomensagens(boolean foiEnviadomensagens) {
        this.foiEnviadomensagens = foiEnviadomensagens;
    }
}
