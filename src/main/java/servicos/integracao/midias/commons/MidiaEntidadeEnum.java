/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.midias.commons;

/**
 *
 * <AUTHOR>
 */
public enum MidiaEntidadeEnum {

    FOTO_EMPRESA(1, ".jpg", "foto", true),
    FOTO_EMPRESA_RELATORIO(2, ".jpg", "fotoRelatorio", true),
    FOTO_EMPRESA_EMAIL(3, ".jpg", "fotoEmail", true),
    FOTO_EMPRESA_REDESOCIAL(4, ".jpg", "fotoRedeSocial", true),
    FOTO_EMPRESA_HomeBackground640x551(5, ".jpg", "homeBackground640x551", true),
    FOTO_EMPRESA_HomeBackground320x276(6, ".jpg", "homeBackground320x276", true),
    FOTO_PACOTE_STUDIO(7, ".jpg", "imagem", true),
    FOTO_PESSOA(8, ".jpg", "foto", true),
    FOTO_MODELO_MENSAGEM(9, ".jpg", "imagemModelo", true),
    FOTO_LOGIN_POST_PEQUENO(10, ".jpg", "imagem", true),
    FOTO_LOGIN_SLIDER_JPG(11, ".jpg", "slider", true),
    FOTO_LOGIN_SLIDER_GIF(12, ".gif", "slider_gif", true),
    DOCUMENTO_WORD97(13, ".doc", "documento", true),
    DOCUMENTO_WORD(14, ".docx", "documento", true),
    DOCUMENTO_PDF(15, ".pdf", "documento", true),
    FOTO_ANEXO_JPG(16, ".jpg", "documento", true),
    FOTO_ANEXO_PNG(17, ".png", "documento", true),
    DOCUMENTO_TEXTO(18, ".txt", "documento", true),
    ANEXO_ENDERECO_CONTRATO(19, ".jpg", "anexoEndereco", true),
    ANEXO_ATESTADO_APTIDAO(20, ".jpg", "atestadoAptidao", true),
    ASSINATURA_EMPRESA(21, ".jpg", "assinaturaEmpresa", true),
    ANEXO_ATESTADO_OPERACAO_CONTRATO(22,".jpg","atestadoOperacaoContrato", true),
    ANEXO_CLIENTE(23,".jpg","anexoCliente", true),
    FOTO_AVALIACAO_FISICA(24, ".jpg", "avaliacaoFisica", true),
    FOTO_AVALIACAO_POSTURAL(25, ".jpg", "avaliacaoPostural", true),
    ASSINATURA_PARQ(26, ".png", "assinaturaParq", true),
    FOTO_USUARIO_APP(27, ".jpg", "fotoUsuarioApp", true),
    IMG_FEED_APP(28, ".jpg", "imagemFeedApp", true),
    IMG_PRODUTO_PACTO(29, ".jpg", "imagemProdutoPacto", false),
    IMG_PRODUTO_PACTO_SUCESSO(30, ".jpg", "imagemSucessoProdutoPacto", false),
    ICONE_PRODUTO_PACTO(31, ".png", "iconeProdutoPacto", false)

    ;
    private Integer codigo;
    private String extensao;
    private String nomeCampo;
    private Boolean droparAlphaChannel;

    private MidiaEntidadeEnum(Integer codigo, final String extensao, final String nomeCampo, final boolean droparAlphaChannel) {
        this.codigo = codigo;
        this.extensao = extensao;
        this.nomeCampo = nomeCampo;
        this.droparAlphaChannel = droparAlphaChannel;
    }
    public static MidiaEntidadeEnum obterPorExtensao(String fileName){
         for(MidiaEntidadeEnum obj : values()){
             if(fileName.equals(obj.getExtensao())){
                 return obj;
             }
         }
        return null;
    }
    public Integer getCodigo() {
        return codigo;
    }

    public String getExtensao() {
        return extensao;
    }

    public String getNomeCampo() {
        return nomeCampo;
    }

    public Boolean getDroparAlphaChannel() {
        return droparAlphaChannel;
    }
}
