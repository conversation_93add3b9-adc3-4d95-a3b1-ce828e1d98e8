/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.midias.zwinternal;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import servicos.integracao.midias.MidiaService;

/**
 * <AUTHOR>
 */
public class MidiaZWInternalServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("image/jpeg");
        String url = request.getRequestURL().toString();
        final String urlServlet = request.getServletPath() + "/";
        String[] urlTemp = url.split(urlServlet);
        String key = urlTemp[1];

        try {
            boolean existe = MidiaService.getInstance().exists(key);

            if (existe) {
                String path = Aplicacao.getProp(Aplicacao.diretorioFotos);
                path += key;
                File f = new File(path);
                response.setHeader("Last-Modified", new Date(f.lastModified()).toGMTString());
                BufferedImage bi = ImageIO.read(f);
                OutputStream out = response.getOutputStream();
                ImageIO.write(bi, "jpg", out);
                out.close();
            }
        } catch (Exception ex) {
            Uteis.logar(ex, MidiaZWInternalServlet.class);
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
