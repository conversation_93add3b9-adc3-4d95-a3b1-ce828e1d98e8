package servicos.integracao.adm;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.integracao.IntegracaoCadastrosWSConsumer;
import br.com.pacto.objeto.Uteis;
import servicos.integracao.adm.client.AdmWS;
import servicos.integracao.adm.client.AdmWS_Service;
import servicos.integracao.adm.client.EmpresaTO;
import servicos.integracao.adm.client.EmpresaWS;
import servicos.integracao.zw.client.IntegracaoCadastrosWS;
import servicos.integracao.zw.client.IntegracaoCadastrosWS_Service;

import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by glauco on 19/11/2014.
 */
public class AdmWSConsumer {

    private static Map<String, AdmWS> m = Collections.synchronizedMap(new HashMap<String, AdmWS>());
    private static Map<String, Boolean> carregando = Collections.synchronizedMap(new HashMap<String, Boolean>());

    public static void clean() {
        m = null;
        m = new HashMap<>();
    }

    public static Map<String, AdmWS> getM() {
        if (m == null) {
            m = new HashMap<>();
        }
        return m;
    }

    private static AdmWS getInstance(final Empresa empresa) {
        final String url = empresa.getRoboControleSemHTTPS()+ "/AdmWS?wsdl";
        try {
            if (carregando != null && empresa != null && carregando.get(url) != null && carregando.get(url)) {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            if (getM().get(url) == null) {
                carregando.put(url, true);

                URL u = new URL(empresa.getRoboControleSemHTTPS()+ "/AdmWS?wsdl");
                QName qName = new QName("http://adm.integracao.servicos/", "AdmWS");
                AdmWS intf = new AdmWS_Service(u, qName).getAdmWSPort();
                Map<String, Object> reqContext = ((BindingProvider) intf).getRequestContext();
                reqContext.put("com.sun.xml.ws.connect.timeout", 10000);
                reqContext.put("com.sun.xml.ws.request.timeout", 200000);

                getM().put(url, intf);
                return intf;
            } else {
                return getM().get(url);
            }
        } catch (MalformedURLException ex) {
            Logger.getLogger(IntegracaoCadastrosWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            carregando.remove(url);
        }
        return null;
    }

    public static String obterModulos(Empresa empresa) {
        Uteis.logar(null, "Obtendo modulos para chave: " + empresa.getChave());
        EmpresaTO e = getInstance(empresa).obterEmpresa(empresa.getChave());
        final String modulos = e != null ? e.getModulos() : "";
        Uteis.logar(null, String.format("-> Modulos habilitados - Chave: %s Modulos: %s ", new Object[]{empresa.getChave(), modulos}));
        return modulos;
    }

    public static List<EmpresaWS> obterEmpresasZW(Empresa empresa, final Integer usuarioZW) {
        Uteis.logar(null, "Obtendo empresas para chave: " + empresa.getChave());
        return getInstance(empresa).obterEmpresasUsuario(empresa.getChave(), usuarioZW);
    }

    public static int alterarCreditoDCC(Empresa empresa, final Integer codigoEmpresa, final int quantidade) {
        Uteis.logar(null, "Adicionando " + quantidade + " créditos para a empresa " + codigoEmpresa + " da chave: " + empresa.getChave());
        return getInstance(empresa).alterarCreditoDCC(empresa.getChave(), codigoEmpresa, quantidade);
    }

    public static String gravarPrePagoDCCLancarAgendamentoFinan(Empresa empresa, final Integer codigoEmpresa, final int qtdCredito, final Integer qtdParcelas, final Double valorTotal,
                                                                final boolean gerarNota, final String nomeUsuarioOAMD, final String justificativa, final boolean gerarCobrancaFinanceiro) {
        Uteis.logar(null, "Adicionando credito Pre-Pago " + qtdCredito + " créditos para a empresa " + codigoEmpresa + " da chave: " + empresa.getChave());
        return getInstance(empresa).gravarPrePagoDCCLancarAgendamentoFinan(empresa.getChave(), codigoEmpresa, qtdCredito, qtdParcelas, valorTotal, gerarNota, nomeUsuarioOAMD, justificativa, gerarCobrancaFinanceiro);
    }

    public static String gravarPosPagoDCCLancarAgendamentoFinan(Empresa empresa, final Integer codigoEmpresa, final Integer tipoCobrancaPacto, final int qtdCredito, final Integer qtdParcelas,
                                                                final Double valorTotal, final boolean gerarNota, final String nomeUsuarioOAMD, final String justificativa, final boolean gerarCobrancaFinanceiro) {
        Uteis.logar(null, "Processando Pos-Pago da empresa " + codigoEmpresa + " da chave: " + empresa.getChave());
        return getInstance(empresa).gravarPosPagoDCCLancarAgendamentoFinan(empresa.getChave(), codigoEmpresa, tipoCobrancaPacto, qtdCredito, qtdParcelas, valorTotal, gerarNota, nomeUsuarioOAMD, justificativa, gerarCobrancaFinanceiro);
    }

    public static String alterarInformacoesEmpresaCobrancaPacto(Empresa empresa, final Integer codigoEmpresa, final Integer tipoCobrancaPacto, final boolean gerarCobrancaAutomaticaPacto,
                                                                final Integer qtdDiasFechamentoCobrancaPacto, final Double valorCreditoPacto, final boolean gerarNotaFiscalCobrancaPacto,
                                                                final Integer qtdParcelasCobrancaPacto, final Integer qtdCreditoRenovarPrePagoCobrancaPacto, final String nomeClienteCobrancaPacto,
                                                                final String emailClienteCobrancaPacto, final String celularClienteCobrancaPacto, final String nomeUsuarioOAMD) {
        Uteis.logar(null, "Alterando Informações de Cobranca Pacto da empresa " + codigoEmpresa + " da chave: " + empresa.getChave());
        return getInstance(empresa).alterarInformacoesEmpresaCobrancaPacto(empresa.getChave(), codigoEmpresa, tipoCobrancaPacto, gerarCobrancaAutomaticaPacto,
                qtdDiasFechamentoCobrancaPacto, valorCreditoPacto, gerarNotaFiscalCobrancaPacto, qtdParcelasCobrancaPacto, qtdCreditoRenovarPrePagoCobrancaPacto,
                nomeClienteCobrancaPacto, emailClienteCobrancaPacto, celularClienteCobrancaPacto, nomeUsuarioOAMD);
    }

    public static String consultarQtdCreditoUtilizado(Empresa empresa, final Integer codigoEmpresa, final Integer tipoCobrancaPacto) {
        Uteis.logar(null, "Consultar Quantidade Crédito DCC utilizado da empresa " + codigoEmpresa + " da chave: " + empresa.getChave());
        return getInstance(empresa).consultarQtdCreditoUtilizado(empresa.getChave(), codigoEmpresa, tipoCobrancaPacto);
    }

    public static List<EmpresaWS> obterEmpresasZW(Empresa empresa, String chave) {
        Uteis.logar(null, "Obtendo lista de empresas da chave: " + chave);
        return getInstance(empresa).obterEmpresas(chave);
    }

    public static List<EmpresaWS> obterEmpresasZWComSituacao(Empresa empresa, String chave, boolean ativa) {
        Uteis.logar(null, "Obtendo lista de empresas ativas da chave: " + chave);
        return getInstance(empresa).obterEmpresasComSituacao(chave, ativa);
    }

    public static String desvincularNotaFiscalEmitida(Empresa empresa, String json) {
        return getInstance(empresa).desvincularNotaFiscalEmitida(empresa.getChave(), json);
    }

    public static String executarProcessoLatitudeLongitude(Empresa e) {
        Uteis.logar(null, "Executando Processo");
        return getInstance(e).executarProcessoLatitudeLongitude(e.getChave());
    }

    public static String utilizarMoviDesk(final Empresa empresa, final Boolean deveUtilizarMoviDesk) {
        Uteis.logar("Marcando utilização do MoviDesk para " + deveUtilizarMoviDesk);
        return getInstance(empresa).utilizarMoviDesk(empresa.getChave(), String.valueOf(deveUtilizarMoviDesk));
    }

    public static String utilizarChatMoviDesk(final Empresa empresa, final Boolean deveUtilizarChatMoviDesk) {
        Uteis.logar("Marcando utilização do CHAT do MoviDesk para " + deveUtilizarChatMoviDesk);
        return getInstance(empresa).utilizarChatMoviDesk(empresa.getChave(), String.valueOf(deveUtilizarChatMoviDesk));
    }
    public static String atualizarDadosEmpresa(final Empresa empresa, final Boolean corrigirProtocolo) {
        Uteis.logar("Marcando correção de PROTOCOLO para " + corrigirProtocolo);
        return getInstance(empresa).atualizarDadosEmpresa(empresa.getChave(), String.valueOf(corrigirProtocolo));
    }

    public static String alterarGrupoChatMovidesk(final Empresa empresa, final String grupoChatMoviDesk) {
        Uteis.logar("Marcando grupo de CHAT do MoviDesk para " + grupoChatMoviDesk);
        return getInstance(empresa).alterarGrupoChatMovidesk(empresa.getChave(), grupoChatMoviDesk);
    }

    public static String ajustarEmpresaParaIniciarPosPago(final Empresa empresa, final String json) {
        return getInstance(empresa).ajustarEmpresaParaIniciarPosPago(empresa.getChave(), json);
    }

}
