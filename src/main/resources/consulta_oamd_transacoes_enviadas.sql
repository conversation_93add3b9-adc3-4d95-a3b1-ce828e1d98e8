SELECT
    count(codigo) as qtd,
    sum(valorparcela::numeric) as valor
FROM (
  select
    distinct(mpa.codigo),
    mpa.valorparcela,
    (select nrtentativaparcela from remessaitem where movparcela =  item.movparcela order by nrtentativaparcela desc limit 1) as nrtentativaparcela
      from remessaitem  item
      inner join remessa re on re.codigo =item.remessa
      inner join empresa emp on emp.codigo = re.empresa
      inner join movparcela mpa on mpa.codigo = item.movparcela
      inner join conveniocobranca cc on cc.codigo = re.conveniocobranca
      left join pagamentomovparcela pmp on pmp.movparcela = item.movparcela
      left join movpagamento mp on mp.codigo = pmp.movpagamento
      left join formapagamento fp on fp.codigo = mp.formapagamento
      WHERE 1 = 1
      AND item.movparcela IS NOT NULL
      AND re.dataregistro::date BETWEEN '01/10/2017' AND '31/10/2017'
      --AND re.empresa = 1 PEGA TODASOLO0O
      AND mpa.situacao  not in ('RG')
      AND re.tipo IN (2, 8, 12)
    ) as foo
