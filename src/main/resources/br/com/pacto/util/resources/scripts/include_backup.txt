#---------------------------------------------------------------------------------------------------------------------------
FILE=$DIRETORIO_LINUX/$NOME_BANCO_DADOS`date +-%Y-%m-%d`.backup
export PGPASSWORD=PWD_SGBD

echo -e "Gerando o Backup... $NOME_BANCO_DADOS `date +%Y-%m-%d` `date +"%T"`"
$PGDUMP --username USER_SGBD --host $SERVIDOR --port $PORTA --verbose --format custom --blobs $NOME_BANCO_DADOS --file $FILE

unset PGPASSWORD