CREATE TABLE Evento(
	codigo SERIAL NOT NULL,
	descricao VARCHAR(50),
	status VARCHAR(2),
	
	PRIMARY KEY(codigo)
);
----------------------------------------------------------------------------------------------------------

CREATE TABLE GrupoColaborador (	
    gerente INT, 
    descricao VARCHAR(50), 
    codigo SERIAL NOT NULL, 
	tipoGrupo VARCHAR(2),
	situacaogrupo VARCHAR(2),
	CONSTRAINT FK_GrupoColaborador_gerente FOREIGN KEY (gerente) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
    PRIMARY KEY (codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE GrupoColaboradorParticipante (	
	codigo SERIAL NOT NULL,
	grupocolaborador INT,
	tipovisao varchar(2),
	colaboradorParticipante INT,
	CONSTRAINT FK_GrupoColaboradorParicipante_grupocolaborador FOREIGN KEY (grupocolaborador) REFERENCES grupocolaborador(codigo) ON UPDATE CASCADE ON DELETE CASCADE, 
	CONSTRAINT FK_GrupoColaboradorParicipante_colaboradorParticipante FOREIGN KEY (colaboradorParticipante) REFERENCES colaborador(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
    PRIMARY KEY (codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE DefinirLayout (	
    sequencia INT, 
    url VARCHAR(100), 
    titulo VARCHAR(50), 
    usuario INT NOT NULL, 
    codigo SERIAL NOT NULL, 
	
	CONSTRAINT FK_DefinirLayout_usuario FOREIGN KEY (usuario) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
    PRIMARY KEY (codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE ConfiguracaoSistemaCRM(
	codigo SERIAL NOT NULL,
	remetentepadrao VARCHAR(50),
	emailpadrao VARCHAR(50),
	mailserver VARCHAR(50),
	login VARCHAR(50),
	senha VARCHAR(30),
	abertosabado BOOLEAN,
	abertodomingo BOOLEAN,
	nrfaltaplanomensal INT,
	nrfaltaplanotrimestral INT,
	nrfaltaplanoacimasemestral INT,
	nrDiasParaClientePreveRenovacao INT,
	nrDiasParaClientePrevePerda INT,
	nrrisco INT,
	conexaosegura BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE ConfiguracaoDiasPosVenda(
	codigo SERIAL NOT NULL,
	configuracaoSistemaCRM INT NOT NULL,
	nrDia INT,
	descricao VARCHAR(50),
	
	CONSTRAINT FK_ConfiguracaoDiasPosVenda_configuracaoSistemaCRM FOREIGN KEY (configuracaoSistemaCRM) REFERENCES ConfiguracaoSistemaCRM(codigo) ON DELETE CASCADE ON UPDATE CASCADE, 
    PRIMARY KEY (codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE Passivo(
		codigo SERIAL NOT NULL,
		nome VARCHAR(50) NOT NULL,
		telefoneresidencial VARCHAR(14),
		telefonecelular VARCHAR(14),
		telefonetrabalho VARCHAR(14),
		responsavelcadastro INT ,
		dia TIMESTAMP,
		observacao TEXT,
		colaboradorResponsavel INT ,
		email varchar(50),
		evento INT,
		cliente int,
			
		CONSTRAINT FK_Passivo_colaboradorResponsavel FOREIGN KEY (colaboradorResponsavel) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
		CONSTRAINT FK_Passivo_responsavelCadastro FOREIGN KEY (responsavelCadastro) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
		CONSTRAINT FK_Passivo_evento FOREIGN KEY (evento) REFERENCES Evento(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
		CONSTRAINT fk_passivo_cliente FOREIGN KEY (cliente)  REFERENCES cliente (codigo) MATCH SIMPLE   ON UPDATE RESTRICT ON DELETE RESTRICT,

        PRIMARY KEY (codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE Indicacao(
		codigo SERIAL NOT NULL,
		responsavelCadastro INT,
		clienteQueIndicou INT,
		colaboradorQueIndicou INT,
		colaboradorResponsavel INT,
		observacao VARCHAR(100),
		dia TIMESTAMP,		
		evento INT,
		
		CONSTRAINT FK_Indicacao_responsavelCadastro FOREIGN KEY (responsavelCadastro) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
		CONSTRAINT FK_Indicacao_colaboradorResponsavel FOREIGN KEY (colaboradorResponsavel) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
		CONSTRAINT FK_Indicacao_clienteQueIndicou FOREIGN KEY (clienteQueIndicou) REFERENCES Cliente(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
		CONSTRAINT FK_Indicacao_colaboradorQueIndicou FOREIGN KEY (colaboradorQueIndicou) REFERENCES Colaborador(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
		CONSTRAINT FK_Indicacao_evento FOREIGN KEY (evento) REFERENCES Evento(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
		PRIMARY KEY(codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE Indicado(
	codigo SERIAL NOT NULL,
	nomeIndicado VARCHAR(50),
	telefoneIndicado VARCHAR(14),
	telefone VARCHAR(14),
	email VARCHAR(60),
	indicacao INT,
	cliente int,
	
	CONSTRAINT FK_Indicado_indicacao FOREIGN KEY (indicacao) REFERENCES Indicacao(codigo) ON UPDATE CASCADE ON DELETE CASCADE, 
    CONSTRAINT fk_indicado_cliente FOREIGN KEY (cliente) REFERENCES cliente (codigo) MATCH SIMPLE  ON UPDATE RESTRICT ON DELETE RESTRICT,

	PRIMARY KEY(codigo)
);

----------------------------------------------------------------------------------------------------------
CREATE TABLE modeloMensagem (
	mensagem text,
	tipoMensagem VARCHAR(2) NOT NULL,
    titulo VARCHAR(225) NOT NULL,
    codigo SERIAL NOT NULL,  
    PRIMARY KEY (codigo)
);

----------------------------------------------------------------------------------------------------------
CREATE TABLE mensagemNotificacao (
	dataenvio TIMESTAMP,
	datacriacao TIMESTAMP,
	mensagem text,	
    titulo VARCHAR(225) NOT NULL,
	remetente integer NOT NULL,
	modelomensagem integer,
    codigo SERIAL NOT NULL,
	CONSTRAINT FK_mensagemNotificacao_remetente FOREIGN KEY (remetente) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_mensagemNotificacao_modelomensagem FOREIGN KEY (modelomensagem) REFERENCES modelomensagem(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	
    PRIMARY KEY (codigo)
);

----------------------------------------------------------------------------------------------------------
CREATE TABLE NotificacaoEnviada (
    mensagemNotificacao integer NOT NULL,
	cliente integer,
	passivo integer,
	indicado integer,
    codigo SERIAL NOT NULL, 
	
    CONSTRAINT fk_notificacaoenviada_indicado FOREIGN KEY (indicado)   REFERENCES indicado (codigo) MATCH SIMPLE  ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_notificacaoenviada_passivo FOREIGN KEY (passivo)   REFERENCES passivo (codigo) MATCH SIMPLE  ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT FK_NotificacaoEnviada_mensagemNotificacao FOREIGN KEY (mensagemNotificacao) REFERENCES mensagemNotificacao(codigo) ON UPDATE CASCADE ON DELETE CASCADE, 
	CONSTRAINT FK_NotificacaoEnviada_cliente FOREIGN KEY (cliente) REFERENCES cliente(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	
    PRIMARY KEY (codigo)
);
	
----------------------------------------------------------------------------------------------------------
CREATE TABLE Feriado(
	codigo SERIAL NOT NULL,
	descricao VARCHAR(50),
	dia TIMESTAMP,
	mes VARCHAR(15),
	nacional BOOLEAN,
	estadual BOOLEAN,
	estado INT,
	naoRecorrente BOOLEAN,
	cidade INT,
	pais INT,
	
	CONSTRAINT FK_Feriado_pais FOREIGN KEY (pais) REFERENCES Pais(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_Feriado_cidade FOREIGN KEY (cidade) REFERENCES Cidade(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_Feriado_estado FOREIGN KEY (estado) REFERENCES Estado(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	PRIMARY KEY(codigo)
);
----------------------------------------------------------------------------------------------------------
	CREATE TABLE Agenda(
	codigo SERIAL NOT NULL,
    dataAgendamento TIMESTAMP,
	dataLancamento TIMESTAMP,
	hora VARCHAR(05),
	minuto character varying(2),
    tipoAgendamento VARCHAR(50),
    modalidade INT,   
    passivo INT,
    colaboradorResponsavel INT,
    cliente INT,
    responsavelCadastro INT,
    indicado INT,
	dataComparecimento TIMESTAMP,
	responsavelComparecimento INT,
	
	
	
	CONSTRAINT FK_Agenda_modalidade FOREIGN KEY (modalidade) REFERENCES Modalidade(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_Agenda_passivo FOREIGN KEY (passivo) REFERENCES Passivo(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_Agenda_cliente FOREIGN KEY (cliente) REFERENCES Cliente(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_Agenda_indicado FOREIGN KEY (indicado) REFERENCES Indicado(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_Agenda_colaboradorResponsavel FOREIGN KEY (colaboradorResponsavel) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_Agenda_responsavelCadastro FOREIGN KEY (responsavelCadastro) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_Agenda_responsavelComparecimento FOREIGN KEY (responsavelComparecimento) REFERENCES Usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 	
	PRIMARY KEY(codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE AberturaMeta(
	codigo SERIAL NOT NULL,
	colaboradorResponsavel INT,
	responsavelCadastro INT,
	dia TIMESTAMP,
	diaFechamento TIMESTAMP,
	fecharMeta BOOLEAN,
	metaemaberto BOOLEAN,
	responsavelliberacaotrocacolaboradorresponsavel integer,
	CONSTRAINT fk_aberturameta_responsavelliberacaotrocacolaboradorresponsavel FOREIGN KEY (responsavelliberacaotrocacolaboradorresponsavel) REFERENCES usuario (codigo) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT FK_AberturaMeta_colaboradorResponsavel FOREIGN KEY (colaboradorResponsavel) REFERENCES usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	CONSTRAINT FK_AberturaMeta_responsavelCadastro FOREIGN KEY (responsavelCadastro) REFERENCES usuario(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT, 
	
	PRIMARY KEY(codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE FecharMeta(
	codigo SERIAL NOT NULL,
	dataRegistro TIMESTAMP,
	meta REAL,
	metaAtingida REAL,
	porcentagem REAL,
	justificativa VARCHAR(50),	
	aberturaMeta INT,
	identificadorMeta VARCHAR(50),
	repescagem int,	  
	CONSTRAINT FK_FecharMeta_aberturaMeta FOREIGN KEY (aberturaMeta) REFERENCES AberturaMeta(codigo) ON UPDATE CASCADE ON DELETE CASCADE, 
	
	PRIMARY KEY(codigo)
);


---------------------------------------------------------------------------------------------------------
CREATE TABLE objecao(
	codigo SERIAL NOT NULL,
	descricao text,
	grupo varchar(50),
	comentario text,
	tipoGrupo varchar(2),
	
	CONSTRAINT objecao_pkey PRIMARY KEY (codigo)
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE historicocontato
(
  codigo serial NOT NULL,
  dia timestamp without time zone,
  cliente integer,
  passivo integer,
  indicado integer,
  mensagemNotificacao integer,
  observacao text,
  tipoOperacao character varying(2),  
  responsavelcadastro integer,
  objecao integer,
  agenda integer,
  fase varchar(50),
  resultado varchar(255),
  tipoContato varchar(2),

  CONSTRAINT historicocontato_pkey PRIMARY KEY (codigo),
  CONSTRAINT fk_historicocontato_cliente FOREIGN KEY (cliente) REFERENCES cliente (codigo) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE CASCADE,
  CONSTRAINT fk_historicocontato_passivo FOREIGN KEY (passivo) REFERENCES passivo (codigo) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_historicocontato_indicado FOREIGN KEY (indicado) REFERENCES Indicado (codigo) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_historicocontato_mensagemNotificacao FOREIGN KEY (mensagemNotificacao) REFERENCES mensagemNotificacao (codigo) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_historicocontato_responsavelcadastro FOREIGN KEY (responsavelcadastro) REFERENCES usuario (codigo) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_historicocontato_objecao FOREIGN KEY (objecao) REFERENCES Objecao (codigo) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_historicocontato_agenda FOREIGN KEY (agenda) REFERENCES Agenda (codigo) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT
  
);
----------------------------------------------------------------------------------------------------------
CREATE TABLE FecharMetaDetalhado(
	codigo SERIAL NOT NULL,
	fecharMeta INT,
	obteveSucesso BOOLEAN,
	cliente INT,
	passivo INT,
	indicado INT,
	historicoContato INT,
	configuracaoDiasPosVenda Int,
	motivoParaEntrarEmContatoComClientePosVenda varchar(50),
	contrato integer,
    acessoCliente integer,
    codigoorigem integer not null,
    origem  character varying(50) not null,
	pesoRisco INT,
	
	CONSTRAINT FK_FecharMetaDetalhado_contrato FOREIGN KEY (contrato) REFERENCES contrato(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT FK_FecharMetaDetalhado_acessocliente FOREIGN KEY (acessocliente ) REFERENCES acessocliente (codigo) ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT FK_FecharMetaDetalhado_fecharMeta FOREIGN KEY (fecharMeta) REFERENCES FecharMeta(codigo) ON UPDATE CASCADE ON DELETE CASCADE,
	CONSTRAINT FK_FecharMetaDetalhado_cliente FOREIGN KEY (cliente) REFERENCES Cliente(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT FK_FecharMetaDetalhado_passivo FOREIGN KEY (passivo) REFERENCES Passivo(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT FK_FecharMetaDetalhado_indicado FOREIGN KEY (indicado) REFERENCES Indicado(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT FK_FecharMetaDetalhado_historicoContato FOREIGN KEY (historicoContato) REFERENCES historicoContato(codigo) ON UPDATE RESTRICT ON DELETE RESTRICT,
	CONSTRAINT fk_fecharmetadetalhado_configuracaoDiasPosVenda FOREIGN KEY (configuracaoDiasPosVenda)   REFERENCES configuracaoDiasPosVenda (codigo) MATCH SIMPLE   ON UPDATE RESTRICT ON DELETE RESTRICT,
	
	PRIMARY KEY(codigo)
);
	
	
		
		
	

	


